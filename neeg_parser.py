#!/usr/bin/env python3
"""
NEEG文件解析器
用于解析Android脑电采集应用生成的.neeg格式文件

文件格式：
- 文件头：64字节（魔数、版本、采样率、通道数、设备名、时间戳等）
- 数据包：[8字节时间戳 + 244字节原始蓝牙数据] × N包

原始蓝牙数据包结构（244字节）：
- 信号数据：4字节（包序号、信号状态、保留、电池电量）
- AF7通道：120字节（40个点 × 3字节ADC数据）
- AF8通道：120字节（40个点 × 3字节ADC数据）

使用示例：
    parser = NEEGParser('recording.neeg')
    header = parser.get_header()
    df = parser.to_dataframe()
    parser.plot_channels()
"""

import struct
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import io

class NEEGParser:
    """NEEG文件解析器"""
    
    # 文件格式常量
    FILE_MAGIC = b'NEEG'
    HEADER_SIZE = 64
    PACKET_SIZE = 244
    TIMESTAMP_SIZE = 8
    DATA_PACKET_SIZE = TIMESTAMP_SIZE + PACKET_SIZE  # 8 + 244 = 252字节
    
    # ADC转换常量（来自UniApp实现）
    ADC_REFERENCE = 4.84
    ADC_BITS = 24
    ADC_SCALE = 256.0
    GAIN_FACTOR = 6.0
    MICROVOLT_SCALE = 1_000_000.0
    
    def __init__(self, file_path: str):
        """
        初始化解析器
        
        Args:
            file_path: .neeg文件路径
        """
        self.file_path = file_path
        self.header = None
        self.packets = []
        self.timestamps = []
        self.af7_data = []
        self.af8_data = []
        self.signal_status = []
        self.battery_levels = []
        self.packet_indices = []
        
        # 自动解析文件
        self._parse_file()
    
    def _parse_file(self):
        """解析整个NEEG文件"""
        with open(self.file_path, 'rb') as f:
            # 解析文件头
            self.header = self._parse_header(f)
            
            # 解析数据包
            while True:
                try:
                    timestamp, packet_data = self._parse_data_packet(f)
                    if timestamp is None:
                        break
                    
                    # 解析EEG数据包
                    eeg_data = self._parse_eeg_packet(packet_data)
                    if eeg_data:
                        self.timestamps.append(timestamp)
                        self.packet_indices.append(eeg_data['packet_index'])
                        self.signal_status.append(eeg_data['signal_status'])
                        self.battery_levels.append(eeg_data['battery_level'])
                        self.af7_data.extend(eeg_data['af7_data'])
                        self.af8_data.extend(eeg_data['af8_data'])
                        
                except struct.error:
                    break
        
        print(f"解析完成：共{len(self.timestamps)}个数据包，{len(self.af7_data)}个数据点")
    
    def _parse_header(self, f: io.BufferedReader) -> Dict:
        """
        解析64字节文件头
        
        Returns:
            包含头部信息的字典
        """
        header_data = f.read(self.HEADER_SIZE)
        if len(header_data) != self.HEADER_SIZE:
            raise ValueError("文件头长度不正确")
        
        # 解析头部字段（小端序）
        magic = header_data[0:4]
        if magic != self.FILE_MAGIC:
            raise ValueError(f"文件魔数不正确：{magic}")
        
        # 使用struct解析
        format_str = '<H H B 32s Q 15s'  # 小端序：版本(2), 采样率(2), 通道数(1), 设备名(32), 时间戳(8), 保留(15)
        unpacked = struct.unpack(format_str, header_data[4:])
        
        header = {
            'magic': magic.decode('ascii'),
            'version': unpacked[0],
            'sample_rate': unpacked[1],
            'channel_count': unpacked[2],
            'device_name': unpacked[3].rstrip(b'\x00').decode('utf-8'),
            'start_timestamp': unpacked[4],
            'start_time': datetime.fromtimestamp(unpacked[4] / 1000.0),
            'reserved': unpacked[5]
        }
        
        print(f"文件头信息：{header}")
        return header
    
    def _parse_data_packet(self, f: io.BufferedReader) -> Tuple[Optional[int], Optional[bytes]]:
        """
        解析单个数据包：8字节时间戳 + 244字节原始数据
        
        Returns:
            (timestamp, packet_data) 或 (None, None) 如果文件结束
        """
        # 读取时间戳
        timestamp_data = f.read(self.TIMESTAMP_SIZE)
        if len(timestamp_data) != self.TIMESTAMP_SIZE:
            return None, None
        
        timestamp = struct.unpack('<Q', timestamp_data)[0]  # 小端序64位无符号整数
        
        # 读取244字节原始数据
        packet_data = f.read(self.PACKET_SIZE)
        if len(packet_data) != self.PACKET_SIZE:
            return None, None
        
        return timestamp, packet_data
    
    def _parse_eeg_packet(self, packet_data: bytes) -> Optional[Dict]:
        """
        解析244字节EEG数据包
        
        Args:
            packet_data: 244字节原始蓝牙数据
            
        Returns:
            解析后的EEG数据字典
        """
        if len(packet_data) != self.PACKET_SIZE:
            return None
        
        # 解析信号数据（前4字节）
        signal_bytes = packet_data[0:4]
        packet_index = signal_bytes[0]
        signal_status = signal_bytes[1]
        reserved = signal_bytes[2]
        battery_level = signal_bytes[3]
        
        # 解析AF7数据（字节4-123）
        af7_raw = packet_data[4:124]
        af7_data = self._parse_channel_data(af7_raw)
        
        # 解析AF8数据（字节124-243）
        af8_raw = packet_data[124:244]
        af8_data = self._parse_channel_data(af8_raw)
        
        return {
            'packet_index': packet_index,
            'signal_status': signal_status,
            'reserved': reserved,
            'battery_level': battery_level,
            'af7_data': af7_data,
            'af8_data': af8_data
        }
    
    def _parse_channel_data(self, channel_bytes: bytes) -> List[float]:
        """
        解析单个通道数据（120字节 = 40个点 × 3字节）
        
        Args:
            channel_bytes: 120字节通道数据
            
        Returns:
            40个微伏值的列表
        """
        if len(channel_bytes) != 120:
            raise ValueError(f"通道数据长度不正确：{len(channel_bytes)}")
        
        data_points = []
        for i in range(40):  # 40个数据点
            byte_index = i * 3
            
            # 提取3字节并转换为有符号整数（使用UniApp方法）
            byte1 = channel_bytes[byte_index]
            byte2 = channel_bytes[byte_index + 1]
            byte3 = channel_bytes[byte_index + 2]
            
            # UniApp方式：(byte1 * 0x1000000 + byte2 * 0x10000 + byte3 * 0x100) / 0x100
            raw_value = (byte1 * 0x1000000 + byte2 * 0x10000 + byte3 * 0x100) // 0x100
            
            # 转换为有符号32位整数
            if raw_value >= 0x80000000:
                raw_value -= 0x100000000
            
            # 转换为微伏：value * (4.84 / 2^24) * 10^6 / 6
            microvolts = raw_value * (self.ADC_REFERENCE / (2 ** self.ADC_BITS)) * self.MICROVOLT_SCALE / self.GAIN_FACTOR
            data_points.append(microvolts)
        
        return data_points
    
    def get_header(self) -> Dict:
        """获取文件头信息"""
        return self.header
    
    def get_data_summary(self) -> Dict:
        """获取数据摘要信息"""
        if not self.timestamps:
            return {}
        
        duration_ms = self.timestamps[-1] - self.timestamps[0]
        duration_s = duration_ms / 1000.0
        
        return {
            'total_packets': len(self.timestamps),
            'total_samples': len(self.af7_data),
            'duration_seconds': duration_s,
            'duration_minutes': duration_s / 60.0,
            'sample_rate_actual': len(self.af7_data) / duration_s if duration_s > 0 else 0,
            'first_timestamp': datetime.fromtimestamp(self.timestamps[0] / 1000.0),
            'last_timestamp': datetime.fromtimestamp(self.timestamps[-1] / 1000.0),
            'af7_range': (min(self.af7_data), max(self.af7_data)) if self.af7_data else (0, 0),
            'af8_range': (min(self.af8_data), max(self.af8_data)) if self.af8_data else (0, 0),
        }
    
    def to_dataframe(self) -> pd.DataFrame:
        """
        将数据转换为pandas DataFrame
        
        Returns:
            包含时间序列EEG数据的DataFrame
        """
        if not self.af7_data:
            return pd.DataFrame()
        
        # 创建时间索引（为每个采样点分配时间戳）
        sample_count = len(self.af7_data)
        if len(self.timestamps) > 1:
            # 根据包的时间戳线性插值计算每个采样点的时间
            packet_timestamps = np.array(self.timestamps)
            
            # 每包40个点，计算每个点的时间戳
            sample_timestamps = []
            for i, packet_time in enumerate(packet_timestamps):
                if i < len(packet_timestamps) - 1:
                    # 计算到下一包的时间间隔
                    next_packet_time = packet_timestamps[i + 1]
                    time_interval = (next_packet_time - packet_time) / 40.0
                    
                    # 为当前包的40个点生成时间戳
                    for j in range(40):
                        sample_timestamps.append(packet_time + j * time_interval)
                else:
                    # 最后一包，使用前一包的间隔
                    if len(packet_timestamps) > 1:
                        prev_interval = (packet_time - packet_timestamps[i-1]) / 40.0
                        for j in range(40):
                            sample_timestamps.append(packet_time + j * prev_interval)
                    else:
                        # 只有一包数据，假设250Hz采样率
                        for j in range(40):
                            sample_timestamps.append(packet_time + j * 4.0)  # 4ms间隔
        else:
            # 只有一个包，使用默认采样率
            base_time = self.timestamps[0] if self.timestamps else 0
            sample_timestamps = [base_time + i * 4.0 for i in range(sample_count)]  # 4ms间隔
        
        # 创建DataFrame
        df = pd.DataFrame({
            'timestamp_ms': sample_timestamps[:sample_count],
            'af7_uv': self.af7_data,
            'af8_uv': self.af8_data
        })
        
        # 添加时间列
        df['datetime'] = pd.to_datetime(df['timestamp_ms'], unit='ms')
        df.set_index('datetime', inplace=True)
        
        return df
    
    def to_numpy(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        转换为NumPy数组
        
        Returns:
            (timestamps, af7_data, af8_data) NumPy数组
        """
        df = self.to_dataframe()
        if df.empty:
            return np.array([]), np.array([]), np.array([])
        
        return (
            df['timestamp_ms'].values,
            df['af7_uv'].values,
            df['af8_uv'].values
        )
    
    def plot_channels(self, start_time: Optional[float] = None, 
                     duration: Optional[float] = None,
                     save_path: Optional[str] = None):
        """
        绘制双通道脑电波形图
        
        Args:
            start_time: 开始时间（秒），None表示从头开始
            duration: 持续时间（秒），None表示到结尾
            save_path: 保存路径，None表示显示图像
        """
        df = self.to_dataframe()
        if df.empty:
            print("没有数据可绘制")
            return
        
        # 计算时间轴（相对于开始时间的秒数）
        start_timestamp = df['timestamp_ms'].iloc[0]
        time_seconds = (df['timestamp_ms'] - start_timestamp) / 1000.0
        
        # 筛选时间范围
        if start_time is not None:
            mask = time_seconds >= start_time
            if duration is not None:
                mask &= time_seconds <= (start_time + duration)
            df = df[mask]
            time_seconds = time_seconds[mask]
        elif duration is not None:
            mask = time_seconds <= duration
            df = df[mask]
            time_seconds = time_seconds[mask]
        
        if df.empty:
            print("指定时间范围内没有数据")
            return
        
        # 创建图像
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
        
        # 绘制AF7
        ax1.plot(time_seconds, df['af7_uv'], 'b-', linewidth=0.8, label='AF7-FPz')
        ax1.set_ylabel('幅值 (μV)')
        ax1.set_title('AF7通道脑电波形')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 绘制AF8
        ax2.plot(time_seconds, df['af8_uv'], 'r-', linewidth=0.8, label='AF8-FPz')
        ax2.set_ylabel('幅值 (μV)')
        ax2.set_xlabel('时间 (秒)')
        ax2.set_title('AF8通道脑电波形')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图像已保存到: {save_path}")
        else:
            plt.show()
    
    def export_to_csv(self, csv_path: str):
        """
        导出数据到CSV文件
        
        Args:
            csv_path: CSV文件路径
        """
        df = self.to_dataframe()
        if df.empty:
            print("没有数据可导出")
            return
        
        df.to_csv(csv_path)
        print(f"数据已导出到: {csv_path}")
    
    def get_signal_quality_stats(self) -> Dict:
        """获取信号质量统计"""
        if not self.signal_status:
            return {}
        
        signal_array = np.array(self.signal_status)
        good_signal_count = np.sum(signal_array == 0)
        poor_signal_count = len(signal_array) - good_signal_count
        
        return {
            'total_packets': len(self.signal_status),
            'good_signal_packets': good_signal_count,
            'poor_signal_packets': poor_signal_count,
            'signal_quality_percentage': (good_signal_count / len(self.signal_status)) * 100
        }


def main():
    """示例用法"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python neeg_parser.py <neeg_file_path>")
        return
    
    neeg_file = sys.argv[1]
    
    try:
        # 创建解析器
        parser = NEEGParser(neeg_file)
        
        # 显示文件信息
        print("\n=== 文件头信息 ===")
        header = parser.get_header()
        for key, value in header.items():
            print(f"{key}: {value}")
        
        # 显示数据摘要
        print("\n=== 数据摘要 ===")
        summary = parser.get_data_summary()
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        # 显示信号质量
        print("\n=== 信号质量 ===")
        quality = parser.get_signal_quality_stats()
        for key, value in quality.items():
            print(f"{key}: {value}")
        
        # 转换为DataFrame并显示前几行
        print("\n=== 数据预览 ===")
        df = parser.to_dataframe()
        print(df.head(10))
        print(f"数据形状: {df.shape}")
        
        # 绘制波形图（前10秒）
        print("\n=== 生成波形图 ===")
        parser.plot_channels(duration=10.0, save_path=f"{neeg_file}_waveform.png")
        
        # 导出CSV
        csv_path = f"{neeg_file}.csv"
        parser.export_to_csv(csv_path)
        
    except Exception as e:
        print(f"解析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()