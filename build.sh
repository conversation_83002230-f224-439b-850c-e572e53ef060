#!/bin/bash

# 脑域奇迹蓝牙安卓项目构建脚本
# 使用方法: ./build.sh [命令]

set -e

echo "🚀 脑域奇迹蓝牙安卓项目构建工具"
echo "================================"

case "${1:-help}" in
    "clean")
        echo "🧹 清理项目..."
        ./gradlew clean
        echo "✅ 清理完成"
        ;;
    "debug")
        echo "🔨 构建Debug版本..."
        ./gradlew assembleDebug -x test
        echo "✅ Debug APK构建完成: app/build/outputs/apk/debug/app-debug.apk"
        ;;
    "release")
        echo "🔨 构建Release版本..."
        ./gradlew assembleRelease -x test
        echo "✅ Release APK构建完成: app/build/outputs/apk/release/app-release-unsigned.apk"
        ;;
    "both")
        echo "🔨 构建Debug和Release版本..."
        ./gradlew assembleDebug assembleRelease -x test
        echo "✅ 两个版本APK构建完成:"
        echo "   - Debug: app/build/outputs/apk/debug/app-debug.apk"
        echo "   - Release: app/build/outputs/apk/release/app-release-unsigned.apk"
        ;;
    "install")
        echo "📱 安装Debug版本到设备..."
        ./gradlew installDebug
        echo "✅ 应用已安装到设备"
        ;;
    "run")
        echo "🚀 构建并运行Debug版本..."
        ./gradlew assembleDebug -x test
        ./gradlew installDebug
        echo "📱 启动应用..."
        adb shell am start -n com.brainwonderful.naoyu_bluetooth_android/.MainActivity
        echo "✅ 应用已在设备上启动"
        ;;
    "devices")
        echo "📱 检查连接的设备..."
        adb devices -l
        ;;
    "logcat")
        echo "📋 显示应用日志 (按Ctrl+C退出)..."
        adb logcat | grep -E "(naoyu|brainwonderful|AndroidRuntime|System.err)"
        ;;
    "logcat-full")
        echo "📋 显示完整系统日志 (按Ctrl+C退出)..."
        adb logcat
        ;;
    "logcat-clear")
        echo "🧹 清理日志缓冲区..."
        adb logcat -c
        echo "✅ 日志已清理"
        ;;
    "shell")
        echo "🐚 进入设备Shell..."
        adb shell
        ;;
    "uninstall")
        echo "🗑️ 卸载应用..."
        adb uninstall com.brainwonderful.naoyu_bluetooth_android
        echo "✅ 应用已卸载"
        ;;
    "reinstall")
        echo "🔄 重新安装应用..."
        echo "🗑️ 先卸载旧版本..."
        adb uninstall com.brainwonderful.naoyu_bluetooth_android 2>/dev/null || echo "应用未安装，跳过卸载"
        echo "🔨 构建新版本..."
        ./gradlew assembleDebug -x test
        echo "📱 安装新版本..."
        ./gradlew installDebug
        echo "🚀 启动应用..."
        adb shell am start -n com.brainwonderful.naoyu_bluetooth_android/.MainActivity
        echo "✅ 应用重新安装并启动完成"
        ;;
    "debug-info")
        echo "🔍 显示调试信息..."
        echo "📱 连接的设备:"
        adb devices -l
        echo ""
        echo "📦 已安装的应用版本:"
        adb shell pm list packages | grep naoyu || echo "应用未安装"
        echo ""
        echo "💾 应用存储信息:"
        adb shell dumpsys package com.brainwonderful.naoyu_bluetooth_android | grep -E "(versionCode|versionName|dataDir)" || echo "应用未安装"
        ;;
    "monitor")
        echo "📊 启动性能监控 (按Ctrl+C退出)..."
        echo "监控CPU和内存使用情况..."
        while true; do
            echo "$(date): $(adb shell top -n 1 | grep naoyu || echo '应用未运行')"
            sleep 2
        done
        ;;
    "screenshot")
        echo "📸 截取设备屏幕..."
        timestamp=$(date +"%Y%m%d_%H%M%S")
        filename="screenshot_${timestamp}.png"
        adb exec-out screencap -p > "$filename"
        echo "✅ 截图已保存: $filename"
        ;;
    "test")
        echo "🧪 运行单元测试..."
        ./gradlew testDebugUnitTest
        ;;
    "test-device")
        echo "🧪 在设备上运行集成测试..."
        ./gradlew connectedDebugAndroidTest
        ;;
    "lint")
        echo "🔍 运行代码检查..."
        ./gradlew lintDebug
        echo "✅ 代码检查完成"
        ;;
    "tasks")
        echo "📋 显示所有可用任务..."
        ./gradlew tasks
        ;;
    "deps")
        echo "📦 显示项目依赖..."
        ./gradlew :app:dependencies --configuration debugCompileClasspath
        ;;
    "info")
        echo "ℹ️ 项目信息:"
        echo "   - 项目名称: 脑奇妙蓝牙安卓版"
        echo "   - 包名: com.brainwonderful.naoyu_bluetooth_android"
        echo "   - 最小SDK: 24 (Android 7.0)"
        echo "   - 目标SDK: 35 (Android 15)"
        echo "   - 编译SDK: 35"
        echo "   - Java版本: 17"
        echo "   - Kotlin版本: 2.0.21"
        echo "   - Gradle版本: 8.11.1"
        ;;
    "help"|*)
        echo "📖 使用说明:"
        echo ""
        echo "🔨 构建命令:"
        echo "   ./build.sh clean        - 清理项目"
        echo "   ./build.sh debug        - 构建Debug版本APK"
        echo "   ./build.sh release      - 构建Release版本APK"
        echo "   ./build.sh both         - 构建Debug和Release版本"
        echo ""
        echo "📱 设备管理:"
        echo "   ./build.sh devices      - 显示连接的设备"
        echo "   ./build.sh install      - 安装Debug版本到设备"
        echo "   ./build.sh run          - 构建、安装并启动应用"
        echo "   ./build.sh uninstall    - 卸载应用"
        echo "   ./build.sh reinstall    - 重新安装并启动应用"
        echo ""
        echo "🐛 调试工具:"
        echo "   ./build.sh logcat       - 显示应用日志"
        echo "   ./build.sh logcat-full  - 显示完整系统日志"
        echo "   ./build.sh logcat-clear - 清理日志缓冲区"
        echo "   ./build.sh shell        - 进入设备Shell"
        echo "   ./build.sh debug-info   - 显示调试信息"
        echo "   ./build.sh monitor      - 性能监控"
        echo "   ./build.sh screenshot   - 截取屏幕"
        echo ""
        echo "🧪 测试命令:"
        echo "   ./build.sh test         - 运行单元测试"
        echo "   ./build.sh test-device  - 在设备上运行集成测试"
        echo "   ./build.sh lint         - 运行代码检查"
        echo ""
        echo "ℹ️ 信息命令:"
        echo "   ./build.sh tasks        - 显示所有Gradle任务"
        echo "   ./build.sh deps         - 显示项目依赖"
        echo "   ./build.sh info         - 显示项目信息"
        echo "   ./build.sh help         - 显示此帮助信息"
        ;;
esac 