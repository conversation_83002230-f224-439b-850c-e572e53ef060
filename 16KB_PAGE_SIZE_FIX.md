# 16KB页面大小兼容性修复

## 问题描述

在Android Studio中运行项目到Android设备时出现以下错误：

```
APK app-debug.apk is not compatible with 16 KB devices. Some libraries have LOAD segments not aligned at 16 KB boundaries:
lib/arm64-v8a/libnaoyu_filters.so
Starting November 1st, 2025, all new apps and updates to existing apps submitted to Google Play and targeting Android 15+ devices must support 16 KB page sizes.
```

## 解决方案

### 1. 修改CMakeLists.txt

在 `app/src/main/cpp/CMakeLists.txt` 中添加链接器标志：

```cmake
# Set linker flags for 16KB page size alignment
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,-z,max-page-size=16384")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-z,max-page-size=16384")
```

### 2. 修改build.gradle.kts

在 `app/build.gradle.kts` 中添加cmake参数：

```kotlin
externalNativeBuild {
    cmake {
        cppFlags += "-std=c++17"
        // Add linker flags for 16KB page size alignment
        arguments += listOf(
            "-DCMAKE_SHARED_LINKER_FLAGS=-Wl,-z,max-page-size=16384",
            "-DCMAKE_EXE_LINKER_FLAGS=-Wl,-z,max-page-size=16384"
        )
    }
}
```

同时添加packaging配置：

```kotlin
// Ensure 16KB page size compatibility
packaging {
    jniLibs {
        useLegacyPackaging = false
    }
}
```

## 验证修复

使用提供的验证脚本检查修复结果：

```bash
./verify_16kb_alignment.sh
```

## 验证结果

所有架构的native库都已正确对齐到16KB页面大小：

- ✅ armeabi-v7a: 所有LOAD段对齐值为0x4000 (16KB)
- ✅ x86: 所有LOAD段对齐值为0x4000 (16KB)  
- ✅ arm64-v8a: 所有LOAD段对齐值为0x4000 (16KB)
- ✅ x86_64: 所有LOAD段对齐值为0x4000 (16KB)

## 相关链接

- [Android 16KB页面大小支持文档](https://developer.android.com/16kb-page-size)
- [Google Play政策更新](https://support.google.com/googleplay/android-developer/answer/15064112)

## 修复日期

2025年1月25日
