# 脑奇妙蓝牙安卓版

这是脑域奇迹蓝牙项目的安卓原生实现版本，基于UniApp版本重构而来。

## 项目特性

- 🧠 **EEG数据采集**: 通过蓝牙连接脑电设备进行数据采集
- 🔧 **C++滤波算法**: 核心滤波算法使用C++实现，确保高性能
- 📱 **现代UI**: 使用Jetpack Compose构建现代化用户界面
- 🔄 **实时处理**: 实时处理和显示脑电数据
- 🧪 **单元测试**: 完整的滤波算法单元测试
- 🐛 **完整调试支持**: 支持命令行真机运行和调试

## 技术栈

- **语言**: Kotlin + C++
- **UI框架**: Jetpack Compose
- **构建工具**: Gradle 8.11.1
- **最小SDK**: Android 7.0 (API 24)
- **目标SDK**: Android 15 (API 35)
- **Java版本**: OpenJDK 17

## 环境要求

### 必需软件
- **Java 17**: OpenJDK 17或更高版本
- **Android SDK**: API 24-35
- **Android Platform Tools**: ADB和Fastboot工具
- **CMake**: 3.22.1或更高版本（用于C++编译）
- **NDK**: 支持armeabi-v7a, arm64-v8a, x86, x86_64

### 可选软件
- **Android Studio**: 推荐用于开发和调试
- **Android设备或模拟器**: 用于测试应用

## 快速开始

### 1. 环境安装

如果你的系统还没有安装Java 17和Android工具，可以使用Homebrew安装：

```bash
# 安装OpenJDK 17
brew install openjdk@17

# 安装Android平台工具
brew install android-platform-tools

# 设置环境变量
echo 'export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"' >> ~/.zshrc
echo 'export JAVA_HOME="/opt/homebrew/opt/openjdk@17"' >> ~/.zshrc
source ~/.zshrc

# 验证安装
java -version
adb version
```

### 2. 设备准备

**连接Android设备：**
1. 在设备上启用"开发者选项"
2. 启用"USB调试"
3. 通过USB连接设备到电脑
4. 授权电脑进行调试

**验证设备连接：**
```bash
adb devices
```

### 3. 克隆项目

```bash
git clone <repository-url>
cd naoyu-bluetooth-android
```

### 4. 构建和运行

我们提供了便捷的构建脚本：

```bash
# 查看所有可用命令
./build.sh help

# 一键构建、安装并运行
./build.sh run

# 或者分步执行
./build.sh debug    # 构建Debug版本
./build.sh install  # 安装到设备
```

## 真机运行和调试

### 🚀 快速运行

```bash
# 一键运行（构建+安装+启动）
./build.sh run

# 检查连接的设备
./build.sh devices

# 重新安装并启动
./build.sh reinstall
```

### 🐛 专业调试工具

项目提供了专门的调试脚本 `debug.sh`，包含丰富的调试功能：

```bash
# 查看调试工具帮助
./debug.sh help

# 应用控制
./debug.sh start      # 启动应用
./debug.sh stop       # 停止应用
./debug.sh restart    # 重启应用

# 实时日志监控
./debug.sh log        # 显示应用日志
./debug.sh log-crash  # 显示崩溃日志

# 性能监控
./debug.sh memory     # 内存使用情况
./debug.sh cpu        # CPU使用情况
./debug.sh profile    # 实时性能监控

# 截图录屏
./debug.sh screenshot    # 截取屏幕
./debug.sh record 15     # 录制15秒视频
```

### 📋 日志调试

**实时查看应用日志：**
```bash
./debug.sh log
```

**查看崩溃日志：**
```bash
./debug.sh log-crash
```

**保存日志到文件：**
```bash
./debug.sh log-save
```

### 📊 性能分析

**查看内存使用：**
```bash
./debug.sh memory
```

**实时性能监控：**
```bash
./debug.sh profile
```

**查看应用权限：**
```bash
./debug.sh permissions
```

### 🧪 测试工具

**运行Monkey测试：**
```bash
./debug.sh monkey 500  # 运行500个随机事件
```

**清理应用数据：**
```bash
./debug.sh clear-data
```

## 构建脚本使用

项目包含一个便捷的构建脚本 `build.sh`，支持以下命令：

### 🔨 构建命令
| 命令 | 说明 |
|------|------|
| `./build.sh clean` | 清理项目构建文件 |
| `./build.sh debug` | 构建Debug版本APK |
| `./build.sh release` | 构建Release版本APK |
| `./build.sh both` | 构建Debug和Release版本 |

### 📱 设备管理
| 命令 | 说明 |
|------|------|
| `./build.sh devices` | 显示连接的设备 |
| `./build.sh install` | 安装Debug版本到设备 |
| `./build.sh run` | 构建、安装并启动应用 |
| `./build.sh uninstall` | 卸载应用 |
| `./build.sh reinstall` | 重新安装并启动应用 |

### 🐛 调试工具
| 命令 | 说明 |
|------|------|
| `./build.sh logcat` | 显示应用日志 |
| `./build.sh logcat-full` | 显示完整系统日志 |
| `./build.sh shell` | 进入设备Shell |
| `./build.sh debug-info` | 显示调试信息 |
| `./build.sh monitor` | 性能监控 |
| `./build.sh screenshot` | 截取屏幕 |

## 原生Gradle命令

如果你更喜欢使用原生Gradle命令：

```bash
# 清理项目
./gradlew clean

# 构建Debug APK
./gradlew assembleDebug -x test

# 构建Release APK
./gradlew assembleRelease -x test

# 安装到设备
./gradlew installDebug

# 运行测试
./gradlew testDebugUnitTest

# 在设备上运行集成测试
./gradlew connectedDebugAndroidTest

# 查看所有任务
./gradlew tasks
```

## 项目结构

```
naoyu-bluetooth-android/
├── app/                          # 主应用模块
│   ├── src/main/
│   │   ├── java/                 # Kotlin源代码
│   │   │   └── com/brainwonderful/naoyu_bluetooth_android/
│   │   │       ├── bluetooth/    # 蓝牙管理
│   │   │       ├── filters/      # 滤波算法JNI接口
│   │   │       ├── ui/           # UI组件
│   │   │       └── viewmodel/    # ViewModel
│   │   ├── cpp/                  # C++源代码
│   │   │   ├── filters/          # 滤波算法实现
│   │   │   └── CMakeLists.txt    # CMake配置
│   │   └── res/                  # 资源文件
│   ├── src/test/                 # 单元测试
│   └── build.gradle.kts          # 应用构建配置
├── build.gradle.kts              # 项目构建配置
├── settings.gradle.kts           # 项目设置
├── build.sh                     # 构建脚本
├── debug.sh                     # 调试脚本
└── README.md                    # 项目文档
```

## 滤波算法

项目包含以下C++实现的滤波算法：

- **FIR滤波器**: 有限冲激响应滤波器
- **陷波滤波器**: 用于去除50Hz/60Hz工频干扰
- **FFT**: 快速傅里叶变换
- **边缘检测**: 信号边缘检测算法
- **Douglas-Peucker**: 数据压缩算法

所有算法都有对应的单元测试，使用专用测试数据进行验证。

## 开发说明

### 单元测试

运行滤波算法的单元测试：

```bash
./build.sh test
```

注意：单元测试需要C++库正确加载，如果在某些环境下测试失败，可以跳过测试进行构建：

```bash
./gradlew assembleDebug -x test
```

### 设备测试

在真机上运行集成测试：

```bash
./build.sh test-device
```

### 代码检查

运行代码质量检查：

```bash
./build.sh lint
```

### 调试

**使用命令行调试：**
1. 连接Android设备并启用USB调试
2. 运行 `./build.sh run` 安装并启动应用
3. 使用 `./debug.sh log` 查看实时日志
4. 使用 `./debug.sh memory` 监控内存使用

**使用Android Studio调试：**
1. 使用Android Studio打开项目
2. 连接Android设备或启动模拟器
3. 点击"Run"按钮或使用快捷键Shift+F10

## 故障排除

### Java环境问题

如果遇到"Unable to locate a Java Runtime"错误：

```bash
# 检查Java安装
java -version

# 检查JAVA_HOME
echo $JAVA_HOME

# 重新安装Java 17
brew install openjdk@17
```

### 设备连接问题

如果设备无法连接：

```bash
# 检查设备连接
adb devices

# 重启ADB服务
adb kill-server
adb start-server

# 检查设备是否启用USB调试
./debug.sh device-info
```

### 构建失败

如果构建失败，尝试清理项目：

```bash
./build.sh clean
./build.sh debug
```

### 应用崩溃

如果应用崩溃，查看崩溃日志：

```bash
./debug.sh log-crash
```

### 单元测试失败

单元测试失败通常是因为C++库加载问题，可以跳过测试：

```bash
./gradlew assembleDebug -x test
```

## 常用调试场景

### 🔍 应用无法启动
```bash
./debug.sh log-crash        # 查看崩溃日志
./debug.sh permissions      # 检查权限
./debug.sh clear-data       # 清理应用数据
./debug.sh reinstall        # 重新安装
```

### 📊 性能问题
```bash
./debug.sh memory           # 检查内存使用
./debug.sh cpu              # 检查CPU使用
./debug.sh profile          # 实时性能监控
```

### 🐛 功能异常
```bash
./debug.sh log              # 实时日志
./debug.sh screenshot       # 截图记录
./debug.sh record 30        # 录制30秒视频
```

## 贡献

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

[添加许可证信息]

## 联系方式

[添加联系方式] 