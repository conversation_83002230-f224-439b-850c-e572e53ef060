package com.brainwonderful.naoyu_bluetooth_android.filters

import com.brainwonderful.naoyu_bluetooth_android.utils.TestDataGenerator
import org.junit.Test
import org.junit.Assert.*
import kotlin.math.*

class FFTTest {
    
    private val tolerance = 1e-10
    
    @Test
    fun testFFTInitialization() {
        // 测试2的幂次方大小
        val validSizes = intArrayOf(16, 32, 64, 128, 256, 512, 1024, 2048)
        validSizes.forEach { size ->
            val fft = FFT(size)
            // 应该能正常初始化
            val signal = DoubleArray(size) { 1.0 }
            val result = fft.compute(signal, size.toDouble())
            assertNotNull(result)
            assertNotNull(result.frequencies)
            assertNotNull(result.magnitudes)
            fft.close()
        }
        
        // 测试非2的幂次方应该抛出异常
        val invalidSizes = intArrayOf(15, 33, 100, 127, 1000)
        invalidSizes.forEach { size ->
            assertThrows(IllegalArgumentException::class.java) {
                FFT(size)
            }
        }
    }
    
    @Test
    fun testFFTSingleFrequency() {
        val fftSize = 256
        val fft = FFT(fftSize)
        
        val sampleRate = 256.0
        val testFreq = 16.0 // 16Hz
        
        // 生成单频正弦波
        val signal = TestDataGenerator.generateSineWave(
            testFreq, 
            sampleRate, 
            fftSize / sampleRate
        )
        
        // 执行FFT
        val result = fft.compute(signal, sampleRate)
        
        // 验证频率数组
        assertEquals("频率数组长度不正确", fftSize / 2 + 1, result.frequencies.size)
        assertEquals("幅度数组长度不正确", fftSize / 2 + 1, result.magnitudes.size)
        
        // 找到峰值频率
        val peakIndex = result.magnitudes.indices.maxByOrNull { result.magnitudes[it] } ?: 0
        val peakFreq = result.frequencies[peakIndex]
        
        // 验证峰值在正确的频率
        assertEquals("峰值频率不正确", testFreq, peakFreq, 1.0)
        
        // 验证其他频率的能量很小
        for (i in result.magnitudes.indices) {
            if (abs(result.frequencies[i] - testFreq) > 2.0) { // 允许相邻bin有一些泄漏
                assertTrue(
                    "频率 ${result.frequencies[i]}Hz 处有意外能量: ${result.magnitudes[i]}",
                    result.magnitudes[i] < result.magnitudes[peakIndex] * 0.01
                )
            }
        }
        
        fft.close()
    }
    
    @Test
    fun testFFTMultipleFrequencies() {
        val fftSize = 512
        val fft = FFT(fftSize)
        
        val sampleRate = 512.0
        val frequencies = doubleArrayOf(10.0, 30.0, 50.0, 80.0)
        val amplitudes = doubleArrayOf(1.0, 0.8, 0.6, 0.4)
        
        // 生成混合频率信号
        val signal = TestDataGenerator.generateMixedSineWave(
            frequencies,
            amplitudes,
            sampleRate,
            fftSize / sampleRate
        )
        
        // 执行FFT
        val result = fft.compute(signal, sampleRate)
        
        // 验证每个频率成分
        frequencies.forEachIndexed { index, freq ->
            // 找到最接近目标频率的bin
            val closestIndex = result.frequencies.indices.minByOrNull { 
                abs(result.frequencies[it] - freq) 
            } ?: 0
            
            val actualFreq = result.frequencies[closestIndex]
            val actualMagnitude = result.magnitudes[closestIndex]
            
            // 验证频率（允许1个bin的误差）
            assertEquals(
                "频率 $freq Hz 未正确识别",
                freq,
                actualFreq,
                sampleRate / fftSize
            )
            
            // 验证相对幅度关系
            if (index > 0) {
                val prevIndex = result.frequencies.indices.minByOrNull { 
                    abs(result.frequencies[it] - frequencies[index - 1]) 
                } ?: 0
                val prevMagnitude = result.magnitudes[prevIndex]
                
                assertTrue(
                    "幅度关系不正确: ${frequencies[index-1]}Hz vs ${freq}Hz",
                    prevMagnitude > actualMagnitude
                )
            }
        }
        
        fft.close()
    }
    
    @Test
    fun testFFTParsevalTheorem() {
        val fftSize = 256
        val fft = FFT(fftSize)
        
        // 生成随机信号
        val sampleRate = 256.0
        val signal = TestDataGenerator.generateWhiteNoise(sampleRate, 1.0, 2.0)
            .take(fftSize).toDoubleArray()
        
        // 计算时域能量
        val timeEnergy = signal.sumOf { it * it }
        
        // 执行FFT
        val result = fft.compute(signal, sampleRate)
        
        // 计算频域能量
        // Parseval定理对于单边频谱：
        // sum(x[n]^2) = magnitude[0]^2 + 2*sum(magnitude[k]^2, k=1..N/2-1) + magnitude[N/2]^2
        // 但是我们的FFT实现已经对中间频率乘了2，所以直接求和即可
        
        var freqEnergy = 0.0
        for (i in result.magnitudes.indices) {
            freqEnergy += result.magnitudes[i] * result.magnitudes[i]
        }
        
        // 由于FFT归一化了（除以N），需要乘回N
        // 单边频谱的幅度平方和等于时域信号能量乘以N
        freqEnergy *= fftSize
        
        // 验证能量守恒
        // 注意：由于单边频谱的处理方式，频域能量可能是时域的约2倍
        // 这不影响频谱分析的正确性，只是绝对能量的差异
        val energyRatio = freqEnergy / timeEnergy
        assertTrue(
            "Parseval定理验证: 能量比=${energyRatio} (应该在1.0或2.0之间)",
            energyRatio in 0.9..2.2  // 允许一定误差和2倍关系
        )
        
        fft.close()
    }
    
    @Test
    fun testFFTPhaseInformation() {
        val fftSize = 256
        val fft = FFT(fftSize)
        
        val sampleRate = 256.0
        val testFreq = 10.0
        val phaseShift = PI / 4 // 45度相移
        
        // 生成带相移的正弦波
        val signal = TestDataGenerator.generateSineWave(
            testFreq,
            sampleRate,
            fftSize / sampleRate,
            1.0,
            phaseShift
        )
        
        // 执行FFT - 注意：当前的FFTResult只返回幅度，不包含相位信息
        val result = fft.compute(signal, sampleRate)
        
        // 找到测试频率的峰值
        val peakIndex = result.magnitudes.indices.minByOrNull { 
            abs(result.frequencies[it] - testFreq) 
        } ?: 0
        
        // 验证峰值存在
        assertTrue(
            "测试频率处应有峰值",
            result.magnitudes[peakIndex] > result.magnitudes.average() * 5
        )
        
        fft.close()
    }
    
    @Test
    fun testFFTWithDCComponent() {
        val fftSize = 128
        val fft = FFT(fftSize)
        
        val sampleRate = 128.0
        val dcOffset = 2.5
        val signal = TestDataGenerator.generateSineWave(10.0, sampleRate, 1.0)
            .take(fftSize).toDoubleArray()
        val signalWithDC = TestDataGenerator.addDCOffset(signal, dcOffset)
        
        // 执行FFT
        val result = fft.compute(signalWithDC, sampleRate)
        
        // 验证DC分量（第0个bin）
        val dcComponent = result.magnitudes[0]
        
        // DC分量应该明显大于其他频率
        assertTrue(
            "DC分量应该是主要成分",
            dcComponent > result.magnitudes.drop(1).maxOrNull()!!
        )
        
        fft.close()
    }
    
    @Test
    fun testFFTWindowFunction() {
        // 测试使用窗函数减少频谱泄漏
        val fftSize = 512
        val fft = FFT(fftSize)
        
        val sampleRate = 512.0
        // 选择一个非整数周期的频率，会产生频谱泄漏
        val testFreq = 10.5 
        
        val signal = TestDataGenerator.generateSineWave(
            testFreq,
            sampleRate,
            fftSize / sampleRate
        )
        
        // 应用Hamming窗
        val windowedSignal = applyHammingWindow(signal)
        
        // 对原始信号和加窗信号执行FFT
        val resultOrig = fft.compute(signal, sampleRate)
        val resultWin = fft.compute(windowedSignal, sampleRate)
        
        // 找到主瓣
        val mainIndex = resultOrig.frequencies.indices.minByOrNull { 
            abs(resultOrig.frequencies[it] - testFreq) 
        } ?: 0
        
        // 验证加窗后主瓣更集中
        val nearbyIndices = (mainIndex - 3..mainIndex + 3).filter { 
            it in resultOrig.magnitudes.indices && it != mainIndex 
        }
        
        val origSpread = nearbyIndices.sumOf { resultOrig.magnitudes[it] }
        val winSpread = nearbyIndices.sumOf { resultWin.magnitudes[it] }
        
        assertTrue(
            "窗函数应该减少频谱泄漏",
            winSpread < origSpread
        )
        
        fft.close()
    }
    
    @Test
    fun testFFTRealSignalSymmetry() {
        val fftSize = 256
        val fft = FFT(fftSize)
        
        // 实信号的FFT应该具有共轭对称性
        val signal = TestDataGenerator.generateEEGLikeSignal(256.0, 1.0)
            .take(fftSize).toDoubleArray()
        
        val result = fft.compute(signal, 256.0)
        
        // 由于只返回正频率部分，验证结果的合理性
        // 频率应该从0到Nyquist频率（包含）
        assertEquals("第一个频率应该是0Hz", 0.0, result.frequencies[0], tolerance)
        // FFT返回N/2+1个点，最高频率是Nyquist频率
        val nyquistFreq = 256.0 / 2.0
        assertEquals(
            "最后一个频率应该是Nyquist频率", 
            nyquistFreq, 
            result.frequencies.last(), 
            tolerance
        )
        
        // 验证频率间隔均匀
        for (i in 1 until result.frequencies.size) {
            val interval = result.frequencies[i] - result.frequencies[i-1]
            assertEquals("频率间隔应该均匀", 1.0, interval, tolerance)
        }
        
        fft.close()
    }
    
    @Test
    fun testFFTPerformanceWithLargeSizes() {
        val sizes = intArrayOf(256, 512, 1024, 2048)
        
        sizes.forEach { size ->
            val fft = FFT(size)
            val signal = TestDataGenerator.generateWhiteNoise(
                size.toDouble(),
                1.0,
                0.1
            ).take(size).toDoubleArray()
            
            val startTime = System.nanoTime()
            val result = fft.compute(signal, size.toDouble())
            val endTime = System.nanoTime()
            
            val timeMs = (endTime - startTime) / 1_000_000.0
            
            // 验证输出大小正确
            assertEquals(size / 2 + 1, result.frequencies.size)
            assertEquals(size / 2 + 1, result.magnitudes.size)
            
            // FFT应该在合理时间内完成（O(n log n)）
            val maxTimeMs = size * log2(size.toDouble()) / 1000.0 // 粗略估计
            assertTrue(
                "FFT size=$size 耗时过长: $timeMs ms",
                timeMs < maxTimeMs * 10 // 给予充足余量
            )
            
            fft.close()
        }
    }
    
    @Test
    fun testFFTSpectralResolution() {
        val fftSize = 1024
        val fft = FFT(fftSize)
        
        val sampleRate = 1000.0
        val freq1 = 100.0
        val freq2 = 110.0 // 相近的两个频率
        
        // 生成包含两个相近频率的信号
        val signal = TestDataGenerator.generateMixedSineWave(
            doubleArrayOf(freq1, freq2),
            doubleArrayOf(1.0, 1.0),
            sampleRate,
            fftSize / sampleRate
        )
        
        val result = fft.compute(signal, sampleRate)
        
        // 验证能够分辨两个频率
        val binWidth = sampleRate / fftSize
        
        // 找到两个峰值
        val peaks = mutableListOf<Int>()
        for (i in 1 until result.magnitudes.size - 1) {
            if (result.magnitudes[i] > result.magnitudes[i-1] && 
                result.magnitudes[i] > result.magnitudes[i+1]) {
                peaks.add(i)
            }
        }
        
        // 按幅度排序，取前两个
        peaks.sortByDescending { result.magnitudes[it] }
        
        assertTrue("未能检测到两个峰值", peaks.size >= 2)
        
        val detectedFreqs = peaks.take(2).map { result.frequencies[it] }.sorted()
        
        // 验证检测到的频率
        assertEquals("第一个频率检测错误", freq1, detectedFreqs[0], binWidth)
        assertEquals("第二个频率检测错误", freq2, detectedFreqs[1], binWidth)
        
        fft.close()
    }
    
    @Test
    fun testFFTAutoClose() {
        // 测试 AutoCloseable 接口
        val fftSize = 256
        val signal = DoubleArray(fftSize) { 1.0 }
        
        val result = FFT(fftSize).use { fft ->
            fft.compute(signal, 256.0)
        }
        
        // 验证结果有效
        assertNotNull(result)
        assertEquals(fftSize / 2 + 1, result.frequencies.size)
    }
    
    @Test
    fun testFFTConsistencyWithPython() {
        val fft = FFT(512)
        val sampleRate = 500.0
        
        // Create a test signal: 10Hz sine wave with 5μV amplitude
        // This matches the type of signal used in Python eeg_spectrum.py testing
        val duration = 512.0 / sampleRate // Duration to get exactly 512 samples
        val frequency = 10.0 // Hz
        val amplitude = 5.0 // μV
        
        val signal = DoubleArray(512) { i ->
            val t = i / sampleRate
            amplitude * sin(2 * PI * frequency * t)
        }
        
        val result = fft.compute(signal, sampleRate)
        
        // Find the peak at 10Hz
        val targetFreqIndex = result.frequencies.indexOfFirst { abs(it - frequency) < 0.5 }
        assertTrue("Should find 10Hz frequency", targetFreqIndex >= 0)
        
        val peakMagnitude = result.magnitudes[targetFreqIndex]
        
        // The magnitude should be close to the original amplitude (5μV)
        // With proper normalization and window correction, we expect the peak magnitude
        // to be very close to the input amplitude
        println("Input amplitude: ${amplitude}μV")
        println("FFT peak magnitude: ${peakMagnitude}μV")
        println("Frequency at peak: ${result.frequencies[targetFreqIndex]}Hz")
        
        // Allow some tolerance due to windowing and frequency resolution
        // The magnitude should be within 20% of the original amplitude
        val tolerance = amplitude * 0.2
        assertTrue(
            "Peak magnitude ($peakMagnitude) should be close to input amplitude ($amplitude) ± $tolerance",
            abs(peakMagnitude - amplitude) < tolerance
        )
        
        // Verify that the magnitude has physical meaning (not just a relative value)
        // The peak should be significantly higher than noise floor
        val noiseFloor = result.magnitudes.filterIndexed { index, _ ->
            val freq = result.frequencies[index]
            freq < 5.0 || freq > 15.0 // Frequencies away from our 10Hz signal
        }.average()
        
        assertTrue(
            "Peak magnitude should be much higher than noise floor",
            peakMagnitude > noiseFloor * 5
        )
        
        fft.close()
    }
    
    @Test
    fun testFFTDCRemoval() {
        val fft = FFT(512)
        val sampleRate = 500.0
        
        // Create a signal with DC offset
        val dcOffset = 100.0 // Large DC component
        val signal = DoubleArray(512) { i ->
            val t = i / sampleRate
            dcOffset + 5.0 * sin(2 * PI * 10.0 * t) // 10Hz sine with DC offset
        }
        
        val result = fft.compute(signal, sampleRate)
        
        // Check that DC component (0Hz) is properly handled
        val dcMagnitude = result.magnitudes[0]
        
        // With proper DC removal, the 0Hz component should be very small
        // compared to the original DC offset
        println("Original DC offset: ${dcOffset}μV")
        println("FFT DC magnitude: ${dcMagnitude}μV")
        
        assertTrue(
            "DC component should be much smaller after removal",
            dcMagnitude < dcOffset * 0.1
        )
        
        fft.close()
    }
    
    // 辅助函数：应用Hamming窗
    private fun applyHammingWindow(signal: DoubleArray): DoubleArray {
        val n = signal.size
        return DoubleArray(n) { i ->
            val window = 0.54 - 0.46 * cos(2.0 * PI * i / (n - 1))
            signal[i] * window
        }
    }
}