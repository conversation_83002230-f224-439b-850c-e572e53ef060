package com.brainwonderful.naoyu_bluetooth_android.filters

import com.brainwonderful.naoyu_bluetooth_android.utils.TestDataGenerator
import org.junit.Test
import org.junit.Assert.*
import kotlin.math.abs
import kotlin.math.log10
import kotlin.math.pow
import kotlin.math.sqrt

class NotchFilterTest {
    
    private val sampleRate = 250.0 // 采样率250Hz
    private val notchFreq = 50.0   // 陷波频率50Hz
    
    @Test
    fun testNotchFilterInitialization() {
        val filter = NotchFilter(sampleRate, notchFreq)
        
        // 验证滤波器已初始化
        val testSignal = doubleArrayOf(1.0, 0.0, -1.0)
        val result = filter.processArray(testSignal)
        assertNotNull(result)
        assertEquals(testSignal.size, result.size)
        
        filter.close()
    }
    
    @Test
    fun testNotchFilterRemoves50Hz() {
        val filter = NotchFilter(sampleRate, notchFreq)
        
        // 生成包含50Hz干扰的信号
        val duration = 2.0 // 2秒
        val signal10Hz = TestDataGenerator.generateSineWave(10.0, sampleRate, duration, 1.0)
        val signal50Hz = TestDataGenerator.generateSineWave(50.0, sampleRate, duration, 1.0)
        val mixedSignal = DoubleArray(signal10Hz.size) { i -> 
            signal10Hz[i] + signal50Hz[i]
        }
        
        // 应用陷波滤波器
        val filtered = filter.processArray(mixedSignal)
        
        // 计算50Hz分量的衰减
        val attenuation50Hz = calculateFrequencyAttenuation(mixedSignal, filtered, 50.0, sampleRate)
        val attenuation10Hz = calculateFrequencyAttenuation(mixedSignal, filtered, 10.0, sampleRate)
        
        // 验证50Hz被显著衰减（至少20dB）
        assertTrue("50Hz衰减不足: $attenuation50Hz dB", attenuation50Hz > 20.0)
        // 验证10Hz基本保持不变（衰减小于3dB）
        assertTrue("10Hz衰减过大: $attenuation10Hz dB", attenuation10Hz < 3.0)
        
        filter.close()
    }
    
    @Test
    fun testNotchFilterWithComplexSignal() {
        val filter = NotchFilter(sampleRate, notchFreq)
        
        // 生成模拟EEG信号（包含50Hz干扰）
        val duration = 4.0
        val eegSignal = TestDataGenerator.generateEEGLikeSignal(sampleRate, duration)
        
        // 应用陷波滤波器
        val filtered = filter.processArray(eegSignal)
        
        // 验证输出信号长度
        assertEquals(eegSignal.size, filtered.size)
        
        // 验证信号能量主要保留（总能量损失小于30%）
        val originalEnergy = calculateSignalEnergy(eegSignal)
        val filteredEnergy = calculateSignalEnergy(filtered)
        val energyLossPercent = (1 - filteredEnergy / originalEnergy) * 100
        
        assertTrue("能量损失过大: $energyLossPercent%", energyLossPercent < 30)
        
        filter.close()
    }
    
    @Test
    fun testNotchFilterStability() {
        val filter = NotchFilter(sampleRate, notchFreq)
        
        // 测试脉冲响应的稳定性
        val impulse = TestDataGenerator.generateImpulse(intArrayOf(0), 1000, 1.0)
        val response = filter.processArray(impulse)
        
        // 验证输出是有限的（没有发散）
        response.forEach { value ->
            assertTrue("输出包含非有限值", value.isFinite())
            assertTrue("输出值过大，可能不稳定", abs(value) < 10.0)
        }
        
        // 验证响应逐渐衰减
        val peakIndex = response.indices.maxByOrNull { abs(response[it]) } ?: 0
        val peakValue = abs(response[peakIndex])
        val endValue = abs(response[response.size - 1])
        
        assertTrue("滤波器响应未衰减", endValue < peakValue * 0.01)
        
        filter.close()
    }
    
    @Test
    fun testNotchFilterFrequencyResponse() {
        val filter = NotchFilter(sampleRate, notchFreq)
        
        // 测试不同频率的响应
        val testFrequencies = doubleArrayOf(1.0, 5.0, 10.0, 20.0, 30.0, 40.0, 45.0, 48.0, 50.0, 52.0, 55.0, 60.0, 70.0, 80.0, 90.0, 100.0)
        val duration = 2.0
        
        testFrequencies.forEach { freq ->
            val signal = TestDataGenerator.generateSineWave(freq, sampleRate, duration, 1.0)
            val filtered = filter.processArray(signal)
            
            val attenuation = calculateFrequencyAttenuation(signal, filtered, freq, sampleRate)
            
            when {
                abs(freq - notchFreq) < 2.0 -> {
                    // 陷波频率附近应该有显著衰减
                    assertTrue("$freq Hz 衰减不足: $attenuation dB", attenuation > 15.0)
                }
                abs(freq - notchFreq) < 5.0 -> {
                    // 过渡带（调整为与androidTest一致）
                    assertTrue("$freq Hz 过渡带衰减异常: $attenuation dB", attenuation > 1.5)
                }
                else -> {
                    // 通带应该衰减很小
                    assertTrue("$freq Hz 通带衰减过大: $attenuation dB", attenuation < 3.0)
                }
            }
        }
        
        filter.close()
    }
    
    @Test
    fun testNotchFilterPhaseResponse() {
        val filter = NotchFilter(sampleRate, notchFreq)
        
        // 测试相位响应的线性度（对于IIR滤波器，主要关注通带）
        val testFreq = 20.0 // 测试20Hz信号
        val duration = 2.0
        val signal = TestDataGenerator.generateSineWave(testFreq, sampleRate, duration)
        
        // 处理信号
        val filtered = filter.processArray(signal)
        
        // 跳过瞬态响应，计算稳态相位差
        val startIdx = (sampleRate * 0.5).toInt() // 跳过前0.5秒
        val phaseShift = calculatePhaseShift(signal, filtered, startIdx)
        
        // IIR滤波器会引入相位失真，但应该是连续的
        assertTrue("相位偏移异常", abs(phaseShift) < 180.0)
        
        filter.close()
    }
    
    @Test
    fun testNotchFilterWithDCOffset() {
        val filter = NotchFilter(sampleRate, notchFreq)
        
        // 生成带DC偏移的信号
        val duration = 1.0
        val signal = TestDataGenerator.generateSineWave(10.0, sampleRate, duration)
        val dcOffset = 2.5
        val signalWithDC = TestDataGenerator.addDCOffset(signal, dcOffset)
        
        // 应用滤波器
        val filtered = filter.processArray(signalWithDC)
        
        // 计算滤波后的DC分量
        val filteredDC = filtered.average()
        
        // NotchFilter应该保留DC分量（陷波器在0Hz的增益为1）
        // 注意：IIR滤波器可能有轻微的DC偏移
        assertEquals("DC分量保留不正确", dcOffset, filteredDC, dcOffset * 0.05)  // 允许5%误差
        
        filter.close()
    }
    
    @Test
    fun testNotchFilterBetaParameter() {
        // 测试不同beta值的影响
        val betaValues = doubleArrayOf(0.9, 0.95, 0.96, 0.98)
        val duration = 2.0
        val signal50Hz = TestDataGenerator.generateSineWave(50.0, sampleRate, duration)
        
        betaValues.forEach { beta ->
            val filter = NotchFilter(sampleRate, notchFreq, beta)
            
            val filtered = filter.processArray(signal50Hz)
            val attenuation = calculateFrequencyAttenuation(signal50Hz, filtered, 50.0, sampleRate)
            
            // beta越大，陷波越窄，衰减越大
            assertTrue("Beta=$beta 时衰减异常: $attenuation dB", attenuation > 10.0)
            
            filter.close()
        }
    }
    
    // 辅助函数：计算特定频率的衰减（dB）
    private fun calculateFrequencyAttenuation(
        original: DoubleArray,
        filtered: DoubleArray,
        frequency: Double,
        sampleRate: Double
    ): Double {
        // 跳过瞬态响应
        val skipSamples = (sampleRate * 0.2).toInt()
        val steadyStateLength = original.size - skipSamples
        
        // 计算原始信号在目标频率的功率
        val originalPower = calculateFrequencyPower(
            original.sliceArray(skipSamples until original.size),
            frequency,
            sampleRate
        )
        
        // 计算滤波信号在目标频率的功率
        val filteredPower = calculateFrequencyPower(
            filtered.sliceArray(skipSamples until filtered.size),
            frequency,
            sampleRate
        )
        
        // 计算衰减（dB）
        return if (filteredPower > 0 && originalPower > 0) {
            20 * log10(originalPower / filteredPower)
        } else {
            Double.POSITIVE_INFINITY
        }
    }
    
    // 计算信号在特定频率的功率（使用Goertzel算法）
    private fun calculateFrequencyPower(
        signal: DoubleArray,
        frequency: Double,
        sampleRate: Double
    ): Double {
        val n = signal.size
        val k = (frequency * n / sampleRate).toInt()
        val omega = 2.0 * Math.PI * k / n
        val coeff = 2.0 * kotlin.math.cos(omega)
        
        var s0 = 0.0
        var s1 = 0.0
        var s2 = 0.0
        
        signal.forEach { sample ->
            s0 = sample + coeff * s1 - s2
            s2 = s1
            s1 = s0
        }
        
        val real = s1 - s2 * kotlin.math.cos(omega)
        val imag = s2 * kotlin.math.sin(omega)
        
        return sqrt(real * real + imag * imag)
    }
    
    // 计算信号能量
    private fun calculateSignalEnergy(signal: DoubleArray): Double {
        return signal.sumOf { it * it }
    }
    
    // 计算相位偏移
    private fun calculatePhaseShift(
        original: DoubleArray,
        filtered: DoubleArray,
        startIdx: Int
    ): Double {
        // 简化的相位计算：找到过零点
        var originalZeroCrossing = -1
        var filteredZeroCrossing = -1
        
        for (i in startIdx until original.size - 1) {
            if (original[i] <= 0 && original[i + 1] > 0) {
                originalZeroCrossing = i
                break
            }
        }
        
        for (i in startIdx until filtered.size - 1) {
            if (filtered[i] <= 0 && filtered[i + 1] > 0) {
                filteredZeroCrossing = i
                break
            }
        }
        
        return if (originalZeroCrossing >= 0 && filteredZeroCrossing >= 0) {
            val sampleDelay = filteredZeroCrossing - originalZeroCrossing
            (sampleDelay.toDouble() / sampleRate) * 360.0 * 20.0 // 转换为度数（20Hz）
        } else {
            0.0
        }
    }
}