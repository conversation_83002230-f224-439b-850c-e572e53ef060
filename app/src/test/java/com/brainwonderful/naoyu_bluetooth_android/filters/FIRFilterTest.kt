package com.brainwonderful.naoyu_bluetooth_android.filters

import com.brainwonderful.naoyu_bluetooth_android.utils.TestDataGenerator
import org.junit.Test
import org.junit.Assert.*
import kotlin.math.abs
import kotlin.math.log10
import kotlin.math.pow
import kotlin.math.sqrt

class FIRFilterTest {
    
    private val sampleRate = 250.0 // 采样率250Hz
    
    @Test
    fun testFIRFilterLowPass() {
        val cutoffFreq = 30.0 // 30Hz低通
        val filter = FIRFilter.createLowpass(sampleRate, cutoffFreq)
        
        // 测试不同频率的信号
        val testFrequencies = doubleArrayOf(5.0, 10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 80.0, 100.0)
        val duration = 2.0
        
        testFrequencies.forEach { freq ->
            val signal = TestDataGenerator.generateSineWave(freq, sampleRate, duration)
            val filtered = filter.processBlock(signal)
            
            val attenuation = calculateFrequencyAttenuation(signal, filtered, freq, sampleRate)
            
            when {
                freq <= cutoffFreq * 0.8 -> {
                    // 通带：衰减应该很小
                    assertTrue("$freq Hz 通带衰减过大: $attenuation dB", attenuation < 3.0)
                }
                freq >= cutoffFreq * 1.5 -> {
                    // 阻带：应该有显著衰减
                    assertTrue("$freq Hz 阻带衰减不足: $attenuation dB", attenuation > 20.0)
                }
                else -> {
                    // 过渡带
                    assertTrue("$freq Hz 过渡带衰减异常", attenuation > 3.0)
                }
            }
        }
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterHighPass() {
        val cutoffFreq = 5.0 // 5Hz高通
        val filter = FIRFilter.createHighpass(sampleRate, cutoffFreq)
        
        // 测试不同频率的信号
        val testFrequencies = doubleArrayOf(0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 50.0, 80.0, 100.0)
        val duration = 2.0
        
        testFrequencies.forEach { freq ->
            val signal = TestDataGenerator.generateSineWave(freq, sampleRate, duration)
            val filtered = filter.processBlock(signal)
            
            val attenuation = calculateFrequencyAttenuation(signal, filtered, freq, sampleRate)
            
            when {
                freq >= cutoffFreq * 1.5 -> {
                    // 通带：衰减应该很小
                    assertTrue("$freq Hz 通带衰减过大: $attenuation dB", attenuation < 3.0)
                }
                freq <= cutoffFreq * 0.5 -> {
                    // 阻带：应该有显著衰减
                    assertTrue("$freq Hz 阻带衰减不足: $attenuation dB", attenuation > 15.0)
                }
                else -> {
                    // 过渡带
                    assertTrue("$freq Hz 过渡带衰减异常", attenuation > 0.0)
                }
            }
        }
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterBandPass() {
        val lowFreq = 8.0   // 8Hz
        val highFreq = 30.0 // 30Hz
        val filter = FIRFilter.createBandpass(sampleRate, lowFreq, highFreq)
        
        // 测试不同频率的信号
        val testFrequencies = doubleArrayOf(1.0, 5.0, 8.0, 10.0, 15.0, 20.0, 30.0, 40.0, 50.0, 80.0)
        val duration = 2.0
        
        testFrequencies.forEach { freq ->
            val signal = TestDataGenerator.generateSineWave(freq, sampleRate, duration)
            val filtered = filter.processBlock(signal)
            
            val attenuation = calculateFrequencyAttenuation(signal, filtered, freq, sampleRate)
            
            when {
                freq in (lowFreq * 1.2)..(highFreq * 0.8) -> {
                    // 通带中心：衰减应该很小
                    assertTrue("$freq Hz 通带衰减过大: $attenuation dB", attenuation < 3.0)
                }
                freq < lowFreq * 0.5 || freq > highFreq * 1.5 -> {
                    // 阻带：应该有显著衰减
                    assertTrue("$freq Hz 阻带衰减不足: $attenuation dB", attenuation > 15.0)
                }
                else -> {
                    // 过渡带
                    assertTrue("$freq Hz 过渡带衰减异常", attenuation > 0.0)
                }
            }
        }
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterWithEEGSignal() {
        // 典型的EEG带通滤波：1-35Hz
        val filter = FIRFilter.createBandpass(sampleRate, 1.0, 35.0)
        
        // 生成模拟EEG信号
        val duration = 4.0
        val eegSignal = TestDataGenerator.generateEEGLikeSignal(sampleRate, duration)
        
        // 应用滤波器
        val filtered = filter.processBlock(eegSignal)
        
        // 验证输出信号长度
        assertEquals(eegSignal.size, filtered.size)
        
        // 验证滤波后信号的能量分布
        // 1-35Hz范围内的能量应该被保留，50Hz应该被衰减
        val fftSize = 512
        val originalSpectrum = calculateSpectrum(eegSignal.take(fftSize).toDoubleArray())
        val filteredSpectrum = calculateSpectrum(filtered.take(fftSize).toDoubleArray())
        
        // 检查50Hz是否被衰减
        val freq50Index = (50.0 * fftSize / sampleRate).toInt()
        val attenuation50Hz = if (filteredSpectrum[freq50Index] > 0) {
            20 * log10(originalSpectrum[freq50Index] / filteredSpectrum[freq50Index])
        } else {
            Double.POSITIVE_INFINITY
        }
        
        assertTrue("50Hz衰减不足", attenuation50Hz > 10.0)
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterLinearPhase() {
        val filter = FIRFilter.createLowpass(sampleRate, 30.0)
        
        // FIR滤波器应该具有线性相位特性
        // 测试群延迟是否恒定
        val testFreq1 = 10.0
        val testFreq2 = 20.0
        val duration = 2.0
        
        val signal1 = TestDataGenerator.generateSineWave(testFreq1, sampleRate, duration)
        val signal2 = TestDataGenerator.generateSineWave(testFreq2, sampleRate, duration)
        
        val filtered1 = filter.processBlock(signal1)
        val filtered2 = filter.processBlock(signal2)
        
        // 计算延迟（通过互相关找峰值）
        val delay1 = findSignalDelay(signal1, filtered1)
        val delay2 = findSignalDelay(signal2, filtered2)
        
        // FIR滤波器的群延迟应该相同（线性相位）
        assertEquals("群延迟不一致", delay1.toDouble(), delay2.toDouble(), 2.0)
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterImpulseResponse() {
        val cutoffFreq = 30.0
        val filter = FIRFilter.createLowpass(sampleRate, cutoffFreq)
        
        // 生成单位脉冲
        val impulse = TestDataGenerator.generateImpulse(intArrayOf(0), 256, 1.0)
        
        // 脉冲响应
        val response = filter.processBlock(impulse)
        
        // 验证脉冲响应的对称性（FIR滤波器应该是对称的）
        val filterOrder = (sampleRate + 1).toInt()
        val center = filterOrder / 2
        
        // 检查前后的对称性（允许小误差）
        for (i in 0 until minOf(center, response.size)) {
            val mirrorIndex = filterOrder - i - 1
            if (mirrorIndex < response.size) {
                assertEquals(
                    "脉冲响应不对称",
                    response[i],
                    response[mirrorIndex],
                    1e-10
                )
            }
        }
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterDCRemoval() {
        // 高通滤波器用于去除DC
        val filter = FIRFilter.createHighpass(sampleRate, 1.0)
        
        // 生成带DC偏移的信号
        val duration = 2.0
        val signal = TestDataGenerator.generateSineWave(10.0, sampleRate, duration)
        val dcOffset = 3.0
        val signalWithDC = TestDataGenerator.addDCOffset(signal, dcOffset)
        
        // 应用高通滤波器
        val filtered = filter.processBlock(signalWithDC)
        
        // 跳过瞬态响应后计算平均值
        val skipSamples = (sampleRate * 0.5).toInt()
        val steadyStateDC = filtered.drop(skipSamples).average()
        
        // 高通滤波器应该去除DC分量
        assertTrue("DC分量未完全去除", abs(steadyStateDC) < 0.1)
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterStability() {
        val filter = FIRFilter.createBandpass(sampleRate, 8.0, 30.0)
        
        // 测试极端输入
        val duration = 1.0
        val extremeSignal = DoubleArray((sampleRate * duration).toInt()) { i ->
            when (i % 4) {
                0 -> 1000.0
                1 -> -1000.0
                2 -> 0.0
                else -> kotlin.random.Random.nextDouble(-1000.0, 1000.0)
            }
        }
        
        val filtered = filter.processBlock(extremeSignal)
        
        // 验证输出是有限的
        filtered.forEach { value ->
            assertTrue("输出包含非有限值", value.isFinite())
        }
        
        // 验证输出在合理范围内（FIR滤波器是稳定的）
        val maxOutput = filtered.maxOf { abs(it) }
        val maxInput = extremeSignal.maxOf { abs(it) }
        assertTrue("输出幅度异常", maxOutput <= maxInput * 2.0)
        
        filter.close()
    }
    
    @Test
    fun testFIRFilterParameterValidation() {
        // 测试无效参数
        assertThrows(IllegalArgumentException::class.java) {
            FIRFilter.createLowpass(sampleRate, 150.0) // 截止频率超过奈奎斯特频率
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            FIRFilter.createBandpass(sampleRate, 30.0, 10.0) // 低频高于高频
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            FIRFilter.createLowpass(sampleRate, -5.0) // 负频率
        }
    }
    
    @Test
    fun testFIRFilterProcessBlock() {
        val filter = FIRFilter.createLowpass(sampleRate, 30.0)
        
        // 测试processBlock方法
        val blockSize = 64
        val numBlocks = 10
        val signal = TestDataGenerator.generateMixedSineWave(
            doubleArrayOf(10.0, 50.0),
            doubleArrayOf(1.0, 0.5),
            sampleRate,
            blockSize * numBlocks / sampleRate
        )
        
        // 分块处理
        val blockProcessed = mutableListOf<Double>()
        for (i in 0 until numBlocks) {
            val block = signal.sliceArray(i * blockSize until (i + 1) * blockSize)
            val processed = filter.processBlock(block)
            blockProcessed.addAll(processed.toList())
        }
        
        // 整体处理（重新创建滤波器）
        val filter2 = FIRFilter.createLowpass(sampleRate, 30.0)
        val wholeProcessed = filter2.processBlock(signal)
        
        // 由于processBlock会去除DC，结果可能略有不同，但整体形状应该相似
        val correlation = calculateCorrelation(
            blockProcessed.toDoubleArray(),
            wholeProcessed
        )
        
        assertTrue("分块处理与整体处理结果差异过大", correlation > 0.95)
        
        filter.close()
        filter2.close()
    }
    
    @Test
    fun testFIRFilterProcessSingleSample() {
        val filter = FIRFilter.createLowpass(sampleRate, 30.0)
        
        // 生成测试信号
        val signal = TestDataGenerator.generateSineWave(10.0, sampleRate, 1.0)
        
        // 逐个样本处理
        val singleProcessed = DoubleArray(signal.size) { i ->
            filter.process(signal[i])
        }
        
        // 重新创建滤波器并批量处理
        val filter2 = FIRFilter.createLowpass(sampleRate, 30.0)
        val blockProcessed = filter2.processBlock(signal)
        
        // 验证两种处理方式结果一致
        for (i in signal.indices) {
            assertEquals(
                "单样本处理与批量处理结果不一致",
                blockProcessed[i],
                singleProcessed[i],
                1e-10
            )
        }
        
        filter.close()
        filter2.close()
    }
    
    @Test
    fun testFIRFilterAutoClose() {
        // 测试 AutoCloseable 接口
        val signal = TestDataGenerator.generateSineWave(10.0, sampleRate, 1.0)
        
        val result = FIRFilter.createLowpass(sampleRate, 30.0).use { filter ->
            filter.processBlock(signal)
        }
        
        // 验证结果有效
        assertNotNull(result)
        assertEquals(signal.size, result.size)
    }
    
    // 辅助函数：计算频率衰减
    private fun calculateFrequencyAttenuation(
        original: DoubleArray,
        filtered: DoubleArray,
        frequency: Double,
        sampleRate: Double
    ): Double {
        val skipSamples = (sampleRate * 0.2).toInt()
        
        val originalPower = calculateFrequencyPower(
            original.sliceArray(skipSamples until original.size),
            frequency,
            sampleRate
        )
        
        val filteredPower = calculateFrequencyPower(
            filtered.sliceArray(skipSamples until filtered.size),
            frequency,
            sampleRate
        )
        
        return if (filteredPower > 0 && originalPower > 0) {
            20 * log10(originalPower / filteredPower)
        } else {
            Double.POSITIVE_INFINITY
        }
    }
    
    // 使用Goertzel算法计算特定频率的功率
    private fun calculateFrequencyPower(
        signal: DoubleArray,
        frequency: Double,
        sampleRate: Double
    ): Double {
        val n = signal.size
        val k = (frequency * n / sampleRate).toInt()
        val omega = 2.0 * Math.PI * k / n
        val coeff = 2.0 * kotlin.math.cos(omega)
        
        var s0 = 0.0
        var s1 = 0.0
        var s2 = 0.0
        
        signal.forEach { sample ->
            s0 = sample + coeff * s1 - s2
            s2 = s1
            s1 = s0
        }
        
        val real = s1 - s2 * kotlin.math.cos(omega)
        val imag = s2 * kotlin.math.sin(omega)
        
        return sqrt(real * real + imag * imag)
    }
    
    // 简单的FFT实现用于频谱计算
    private fun calculateSpectrum(signal: DoubleArray): DoubleArray {
        val fft = FFT(signal.size)
        val result = fft.compute(signal, signal.size.toDouble())
        fft.close()
        
        // 将结果扩展到完整大小以兼容测试
        val fullSpectrum = DoubleArray(signal.size)
        for (i in result.magnitudes.indices) {
            fullSpectrum[i] = result.magnitudes[i]
        }
        return fullSpectrum
    }
    
    // 通过互相关找信号延迟
    private fun findSignalDelay(original: DoubleArray, filtered: DoubleArray): Int {
        val maxLag = 100
        var maxCorr = -Double.MAX_VALUE
        var bestLag = 0
        
        for (lag in -maxLag..maxLag) {
            val corr = calculateLaggedCorrelation(original, filtered, lag)
            if (corr > maxCorr) {
                maxCorr = corr
                bestLag = lag
            }
        }
        
        return bestLag
    }
    
    // 计算带延迟的相关性
    private fun calculateLaggedCorrelation(signal1: DoubleArray, signal2: DoubleArray, lag: Int): Double {
        val start = maxOf(0, -lag)
        val end = minOf(signal1.size, signal2.size - lag)
        
        if (end <= start) return 0.0
        
        var sum = 0.0
        var sum1Sq = 0.0
        var sum2Sq = 0.0
        
        for (i in start until end) {
            val v1 = signal1[i]
            val v2 = signal2[i + lag]
            sum += v1 * v2
            sum1Sq += v1 * v1
            sum2Sq += v2 * v2
        }
        
        return if (sum1Sq > 0 && sum2Sq > 0) {
            sum / sqrt(sum1Sq * sum2Sq)
        } else {
            0.0
        }
    }
    
    // 计算两个信号的相关性
    private fun calculateCorrelation(signal1: DoubleArray, signal2: DoubleArray): Double {
        val minLen = minOf(signal1.size, signal2.size)
        var sum = 0.0
        var sum1Sq = 0.0
        var sum2Sq = 0.0
        
        for (i in 0 until minLen) {
            sum += signal1[i] * signal2[i]
            sum1Sq += signal1[i] * signal1[i]
            sum2Sq += signal2[i] * signal2[i]
        }
        
        return if (sum1Sq > 0 && sum2Sq > 0) {
            sum / sqrt(sum1Sq * sum2Sq)
        } else {
            0.0
        }
    }
}