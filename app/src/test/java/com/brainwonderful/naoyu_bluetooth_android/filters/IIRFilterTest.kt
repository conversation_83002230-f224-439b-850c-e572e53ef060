package com.brainwonderful.naoyu_bluetooth_android.filters

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for IIRFilter (implemented using FIR backend)
 * Tests basic functionality and API compatibility
 */
class IIRFilterTest {
    
    @Test
    fun testLowpassFilterCreation() {
        val sampleRate = 250.0
        val cutoff = 35.0
        
        val filter = IIRFilter.createLowpassFilter(sampleRate, cutoff)
        assertNotNull(filter)
        
        // Test basic processing
        val input = 1.0
        val output = filter.process(input)
        assertTrue("Output should be finite", output.isFinite())
        
        filter.destroy()
    }
    
    @Test
    fun testHighpassFilterCreation() {
        val sampleRate = 250.0
        val cutoff = 1.0
        
        val filter = IIRFilter.createHighpassFilter(sampleRate, cutoff)
        assertNotNull(filter)
        
        // Test basic processing  
        val input = 1.0
        val output = filter.process(input)
        assertTrue("Output should be finite", output.isFinite())
        
        filter.destroy()
    }
    
    @Test
    fun testBandpassFilterCreation() {
        val sampleRate = 250.0
        val lowCut = 1.0
        val highCut = 35.0
        
        val filter = IIRFilter.createBandpassFilter(sampleRate, lowCut, highCut)
        assertNotNull(filter)
        
        // Test basic processing
        val input = 1.0
        val output = filter.process(input)
        assertTrue("Output should be finite", output.isFinite())
        
        filter.destroy()
    }
    
    @Test
    fun testBlockProcessing() {
        val sampleRate = 250.0
        val cutoff = 35.0
        
        val filter = IIRFilter.createLowpassFilter(sampleRate, cutoff)
        
        // Test block processing
        val input = doubleArrayOf(1.0, 0.5, -0.5, -1.0, 0.0)
        val output = filter.processBlock(input)
        
        assertEquals("Output size should match input size", input.size, output.size)
        assertTrue("All outputs should be finite", output.all { it.isFinite() })
        
        filter.destroy()
    }
    
    @Test(expected = IllegalArgumentException::class)
    fun testInvalidFrequency() {
        val sampleRate = 250.0
        val invalidCutoff = 300.0 // Above Nyquist frequency
        
        IIRFilter.createLowpassFilter(sampleRate, invalidCutoff)
    }
    
    @Test(expected = IllegalArgumentException::class)
    fun testInvalidBandpassFrequencies() {
        val sampleRate = 250.0
        val lowCut = 35.0
        val highCut = 1.0 // Lower than lowCut
        
        IIRFilter.createBandpassFilter(sampleRate, lowCut, highCut)
    }
}