package com.brainwonderful.naoyu_bluetooth_android.filters

import com.brainwonderful.naoyu_bluetooth_android.utils.TestDataGenerator
import org.junit.Test
import org.junit.Assert.*
import kotlin.math.*

class DCRemovalFilterTest {
    
    companion object {
        private const val SAMPLE_RATE = 250.0
        private const val TOLERANCE = 0.01 // 1% tolerance
        private const val SMALL_VALUE_THRESHOLD = 0.001 // Values smaller than this are considered effectively zero
    }
    
    @Test
    fun testFilterCreation() {
        // Test default creation
        val defaultFilter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        assertNotNull(defaultFilter)
        
        // Test cutoff frequency is reasonable for DC removal (~0.3Hz)
        val cutoffHz = defaultFilter.calculateCutoffFrequency()
        assertTrue("Cutoff frequency should be around 0.3Hz, got $cutoffHz", 
                   cutoffHz in 0.2..0.5)
        
        // Test creation with specific cutoff
        val customFilter = DCRemovalFilter.createWithCutoff(SAMPLE_RATE, 0.5)
        val customCutoff = customFilter.calculateCutoffFrequency()
        assertTrue("Custom cutoff should be around 0.5Hz, got $customCutoff", 
                   abs(customCutoff - 0.5) < 0.1)
    }
    
    @Test
    fun testDCRemoval() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Test signal with large DC offset
        val dcOffset = 1000.0
        val signalLength = 1000 // 4 seconds at 250Hz
        val inputSignal = DoubleArray(signalLength) { dcOffset }
        
        // Process the signal
        val output = filter.processBlock(inputSignal)
        
        // After processing, the mean should be much closer to zero
        val outputMean = output.average()
        assertTrue("Output mean should be close to zero after DC removal, got $outputMean", 
                   abs(outputMean) < dcOffset * 0.1) // Should be reduced by at least 90%
        
        // The final values should approach zero as the filter adapts
        val finalValues = output.takeLast(100) // Last 100 samples
        val finalMean = finalValues.average()
        assertTrue("Final values should be very close to zero, got $finalMean", 
                   abs(finalMean) < dcOffset * 0.01) // Should be reduced by at least 99%
    }
    
    @Test
    fun testSineWavePreservation() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Create sine wave at 10Hz with DC offset
        val frequency = 10.0
        val amplitude = 100.0
        val dcOffset = 500.0
        val duration = 4.0 // seconds
        val sineWave = TestDataGenerator.generateSineWave(
            frequency, amplitude, SAMPLE_RATE, duration, dcOffset
        )
        
        // Process the signal
        val output = filter.processBlock(sineWave)
        
        // Skip initial samples to allow filter to settle
        val settledOutput = output.drop((SAMPLE_RATE * 0.5).toInt()) // Skip first 0.5 seconds
        
        // Check that the amplitude is preserved (within tolerance)
        val outputMax = settledOutput.maxOrNull() ?: 0.0
        val outputMin = settledOutput.minOrNull() ?: 0.0
        val outputAmplitude = (outputMax - outputMin) / 2.0
        
        assertTrue("Sine wave amplitude should be preserved, expected ~$amplitude, got $outputAmplitude",
                   abs(outputAmplitude - amplitude) < amplitude * TOLERANCE)
        
        // Check that DC component is removed
        val outputMean = settledOutput.average()
        assertTrue("DC component should be removed, output mean should be close to zero, got $outputMean",
                   abs(outputMean) < amplitude * 0.05) // Mean should be < 5% of amplitude
    }
    
    @Test
    fun testLowFrequencyAttenuation() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Test very low frequency signal (0.1Hz) - should be attenuated
        val lowFreq = 0.1
        val amplitude = 100.0
        val duration = 20.0 // Long duration to capture several cycles
        val lowFreqSignal = TestDataGenerator.generateSineWave(
            lowFreq, amplitude, SAMPLE_RATE, duration
        )
        
        val output = filter.processBlock(lowFreqSignal)
        
        // Skip initial samples to allow filter to settle
        val settledOutput = output.drop((SAMPLE_RATE * 5.0).toInt()) // Skip first 5 seconds
        
        // Low frequency signal should be significantly attenuated
        val outputMax = settledOutput.maxOrNull() ?: 0.0
        val outputMin = settledOutput.minOrNull() ?: 0.0
        val outputAmplitude = (outputMax - outputMin) / 2.0
        
        assertTrue("Low frequency signal should be attenuated, output amplitude $outputAmplitude should be much less than input $amplitude",
                   outputAmplitude < amplitude * 0.5) // Should be attenuated by at least 50%
    }
    
    @Test
    fun testHighFrequencyPreservation() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Test high frequency signal (20Hz) - should be preserved
        val highFreq = 20.0
        val amplitude = 100.0
        val duration = 2.0
        val highFreqSignal = TestDataGenerator.generateSineWave(
            highFreq, amplitude, SAMPLE_RATE, duration
        )
        
        val output = filter.processBlock(highFreqSignal)
        
        // Skip initial samples to allow filter to settle
        val settledOutput = output.drop((SAMPLE_RATE * 0.2).toInt()) // Skip first 0.2 seconds
        
        // High frequency signal should be well preserved
        val outputMax = settledOutput.maxOrNull() ?: 0.0
        val outputMin = settledOutput.minOrNull() ?: 0.0
        val outputAmplitude = (outputMax - outputMin) / 2.0
        
        assertTrue("High frequency signal should be preserved, expected ~$amplitude, got $outputAmplitude",
                   abs(outputAmplitude - amplitude) < amplitude * TOLERANCE)
    }
    
    @Test
    fun testEEGSignalPreservation() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Test mixed EEG-like signal with multiple frequency components
        val duration = 4.0
        val dcOffset = 200.0
        
        // Create composite signal with different EEG frequency bands
        val deltaWave = TestDataGenerator.generateSineWave(2.0, 50.0, SAMPLE_RATE, duration) // Delta
        val thetaWave = TestDataGenerator.generateSineWave(6.0, 30.0, SAMPLE_RATE, duration) // Theta  
        val alphaWave = TestDataGenerator.generateSineWave(10.0, 40.0, SAMPLE_RATE, duration) // Alpha
        val betaWave = TestDataGenerator.generateSineWave(20.0, 20.0, SAMPLE_RATE, duration) // Beta
        
        val compositeSignal = DoubleArray(deltaWave.size) { i ->
            deltaWave[i] + thetaWave[i] + alphaWave[i] + betaWave[i] + dcOffset
        }
        
        val output = filter.processBlock(compositeSignal)
        
        // Skip initial samples to allow filter to settle
        val settledOutput = output.drop((SAMPLE_RATE * 1.0).toInt()) // Skip first 1 second
        
        // Check that DC is removed
        val outputMean = settledOutput.average()
        assertTrue("DC component should be removed from EEG signal, got mean $outputMean",
                   abs(outputMean) < 10.0) // Mean should be small
        
        // Check that signal energy is preserved (most of it)
        val inputRMS = sqrt(compositeSignal.map { (it - dcOffset) * (it - dcOffset) }.average())
        val outputRMS = sqrt(settledOutput.map { it * it }.average())
        
        assertTrue("Signal energy should be mostly preserved, input RMS: $inputRMS, output RMS: $outputRMS",
                   abs(outputRMS - inputRMS) < inputRMS * 0.2) // Within 20%
    }
    
    @Test
    fun testFilterReset() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Process some data with DC offset
        val dcSignal = DoubleArray(500) { 1000.0 }
        filter.processBlock(dcSignal)
        
        // Get current DC estimate
        val dcEstimateBeforeReset = filter.getCurrentDCEstimate()
        assertTrue("DC estimate should be significant after processing DC signal",
                   abs(dcEstimateBeforeReset) > 100.0)
        
        // Reset filter
        filter.reset()
        
        // DC estimate should be reset
        val dcEstimateAfterReset = filter.getCurrentDCEstimate()
        assertEquals("DC estimate should be reset to zero", 0.0, dcEstimateAfterReset, SMALL_VALUE_THRESHOLD)
    }
    
    @Test
    fun testSingleSampleProcessing() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Test processing single samples
        val dcOffset = 500.0
        var output = 0.0
        
        // Process multiple samples with same DC value
        repeat(1000) {
            output = filter.processSample(dcOffset)
        }
        
        // Final output should be close to zero
        assertTrue("Single sample processing should remove DC, final output: $output",
                   abs(output) < dcOffset * 0.01)
    }
    
    @Test
    fun testStatistics() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Process some data
        val testSignal = DoubleArray(100) { 100.0 }
        filter.processBlock(testSignal)
        
        // Get statistics
        val stats = filter.getStatistics()
        
        // Check that statistics contain expected keys
        assertTrue("Statistics should contain alpha", stats.containsKey("alpha"))
        assertTrue("Statistics should contain sampleRate", stats.containsKey("sampleRate"))
        assertTrue("Statistics should contain cutoffHz", stats.containsKey("cutoffHz"))
        assertTrue("Statistics should contain currentMean", stats.containsKey("currentMean"))
        assertTrue("Statistics should contain sampleCount", stats.containsKey("sampleCount"))
        
        // Check that sample count is correct
        assertEquals("Sample count should match processed samples", 100L, stats["sampleCount"])
    }
    
    @Test
    fun testInvalidParameters() {
        // Test invalid alpha values
        assertThrows(IllegalArgumentException::class.java) {
            DCRemovalFilter(-0.1, SAMPLE_RATE)
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            DCRemovalFilter(1.1, SAMPLE_RATE)
        }
        
        // Test invalid sample rate
        assertThrows(IllegalArgumentException::class.java) {
            DCRemovalFilter(0.01, -250.0)
        }
    }
    
    @Test
    fun testEmptyInput() {
        val filter = DCRemovalFilter.createDefault(SAMPLE_RATE)
        
        // Test empty array
        val emptyArray = doubleArrayOf()
        val output = filter.processBlock(emptyArray)
        
        assertTrue("Empty input should return empty output", output.isEmpty())
    }
}