#include <jni.h>
#include <string>
#include <vector>
#include <memory>
#include <android/log.h>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#include "filters/notch_filter.h"
#include "filters/fir_filter.h"
#include "filters/fft.h"
#include "filters/edge_detection.h"
#include "filters/douglas_peucker.h"

#define LOG_TAG "NaoyuFilters"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

using namespace naoyu;

// Window function implementation
void applyWindow(std::vector<double>& data, int windowType) {
    size_t N = data.size();
    switch(windowType) {
        case 0: // WINDOW_RECTANGULAR - no changes needed
            break;
        case 1: // WINDOW_HANNING
            for (size_t i = 0; i < N; ++i) {
                data[i] *= 0.5 * (1.0 - cos(2.0 * M_PI * i / (N - 1)));
            }
            break;
        case 2: // WINDOW_HAMMING
            for (size_t i = 0; i < N; ++i) {
                data[i] *= 0.54 - 0.46 * cos(2.0 * M_PI * i / (N - 1));
            }
            break;
        case 3: // WINDOW_BLACKMAN
            for (size_t i = 0; i < N; ++i) {
                data[i] *= 0.42 - 0.5 * cos(2.0 * M_PI * i / (N - 1)) + 0.08 * cos(4.0 * M_PI * i / (N - 1));
            }
            break;
        default:
            break;
    }
}

// Global filter instances
static std::unique_ptr<NotchFilter> g_notchFilter;
static std::unique_ptr<FIRFilter> g_firFilter;
static std::unique_ptr<FFT> g_fft;
static std::unique_ptr<EdgeDetection> g_edgeDetection;
static std::unique_ptr<DouglasPeucker> g_douglasPeucker;

extern "C" {

// Notch Filter JNI
JNIEXPORT jlong JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_createNotchFilter(
        JNIEnv* env, jobject /* this */, jdouble sampleRate, jdouble notchFreq, jdouble qualityFactor) {
    auto* filter = new NotchFilter(sampleRate, notchFreq, qualityFactor);
    return reinterpret_cast<jlong>(filter);
}

JNIEXPORT jlong JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_createNotchFilterWithBeta(
        JNIEnv* env, jobject /* this */, jdouble sampleRate, jdouble notchFreq, jdouble beta) {
    auto* filter = new NotchFilter(sampleRate, notchFreq, beta);
    return reinterpret_cast<jlong>(filter);
}

JNIEXPORT void JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_destroyNotchFilter(
        JNIEnv* env, jobject /* this */, jlong handle) {
    if (handle != 0) {
        delete reinterpret_cast<NotchFilter*>(handle);
    }
}

JNIEXPORT jdouble JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_processNotchFilter(
        JNIEnv* env, jobject /* this */, jlong handle, jdouble input) {
    if (handle == 0) return input;
    auto* filter = reinterpret_cast<NotchFilter*>(handle);
    return filter->process(input);
}

JNIEXPORT void JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_resetNotchFilter(
        JNIEnv* env, jobject /* this */, jlong handle) {
    if (handle != 0) {
        auto* filter = reinterpret_cast<NotchFilter*>(handle);
        filter->reset();
    }
}

// FIR Filter JNI
JNIEXPORT jlong JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_createFIRFilter(
        JNIEnv* env, jobject /* this */, jdouble sampleRate, jint filterType, 
        jdouble freq1, jdouble freq2) {
    auto* filter = new FIRFilter(sampleRate, static_cast<FilterType>(filterType), freq1, freq2);
    return reinterpret_cast<jlong>(filter);
}

JNIEXPORT void JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_destroyFIRFilter(
        JNIEnv* env, jobject /* this */, jlong handle) {
    if (handle != 0) {
        delete reinterpret_cast<FIRFilter*>(handle);
    }
}

JNIEXPORT jdouble JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_processFIRFilter(
        JNIEnv* env, jobject /* this */, jlong handle, jdouble input) {
    if (handle == 0) return input;
    auto* filter = reinterpret_cast<FIRFilter*>(handle);
    return filter->process(input);
}

JNIEXPORT jdoubleArray JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_processFIRFilterBlock(
        JNIEnv* env, jobject /* this */, jlong handle, jdoubleArray inputArray) {
    if (handle == 0) return inputArray;
    
    auto* filter = reinterpret_cast<FIRFilter*>(handle);
    
    jsize length = env->GetArrayLength(inputArray);
    jdouble* inputData = env->GetDoubleArrayElements(inputArray, nullptr);
    
    std::vector<double> input(inputData, inputData + length);
    env->ReleaseDoubleArrayElements(inputArray, inputData, JNI_ABORT);
    
    std::vector<double> output = filter->processBlock(input);
    
    jdoubleArray result = env->NewDoubleArray(output.size());
    env->SetDoubleArrayRegion(result, 0, output.size(), output.data());
    
    return result;
}

// FFT JNI
JNIEXPORT jlong JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_createFFT(
        JNIEnv* env, jobject /* this */, jint size) {
    try {
        auto* fft = new FFT(size);
        return reinterpret_cast<jlong>(fft);
    } catch (const std::exception& e) {
        jclass exClass = env->FindClass("java/lang/IllegalArgumentException");
        env->ThrowNew(exClass, e.what());
        return 0;
    }
}

JNIEXPORT void JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_destroyFFT(
        JNIEnv* env, jobject /* this */, jlong handle) {
    if (handle != 0) {
        delete reinterpret_cast<FFT*>(handle);
    }
}

JNIEXPORT jobject JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_computeFFT(
        JNIEnv* env, jobject /* this */, jlong handle, jdoubleArray inputArray, jdouble sampleRate) {
    if (handle == 0) return nullptr;
    
    auto* fft = reinterpret_cast<FFT*>(handle);
    
    jsize length = env->GetArrayLength(inputArray);
    jdouble* inputData = env->GetDoubleArrayElements(inputArray, nullptr);
    
    std::vector<double> input(inputData, inputData + length);
    env->ReleaseDoubleArrayElements(inputArray, inputData, JNI_ABORT);
    
    FFTResult result = fft->compute(input, sampleRate);
    
    // Create FFTResult object
    jclass fftResultClass = env->FindClass("com/brainwonderful/naoyu_bluetooth_android/filters/FFTResult");
    jmethodID constructor = env->GetMethodID(fftResultClass, "<init>", "([D[D)V");
    
    jdoubleArray freqArray = env->NewDoubleArray(result.frequencies.size());
    env->SetDoubleArrayRegion(freqArray, 0, result.frequencies.size(), result.frequencies.data());
    
    jdoubleArray magArray = env->NewDoubleArray(result.magnitudes.size());
    env->SetDoubleArrayRegion(magArray, 0, result.magnitudes.size(), result.magnitudes.data());
    
    return env->NewObject(fftResultClass, constructor, freqArray, magArray);
}

JNIEXPORT jobject JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_computeFFTWithWindow(
        JNIEnv* env, jobject /* this */, jlong handle, jdoubleArray inputArray, jdouble sampleRate, jint windowType) {
    if (handle == 0) return nullptr;
    
    auto* fft = reinterpret_cast<FFT*>(handle);
    
    jsize length = env->GetArrayLength(inputArray);
    jdouble* inputData = env->GetDoubleArrayElements(inputArray, nullptr);
    
    std::vector<double> input(inputData, inputData + length);
    env->ReleaseDoubleArrayElements(inputArray, inputData, JNI_ABORT);
    
    // Apply window function based on windowType
    applyWindow(input, windowType);
    
    FFTResult result = fft->compute(input, sampleRate);
    
    // Create FFTResult object
    jclass fftResultClass = env->FindClass("com/brainwonderful/naoyu_bluetooth_android/filters/FFTResult");
    jmethodID constructor = env->GetMethodID(fftResultClass, "<init>", "([D[D)V");
    
    jdoubleArray freqArray = env->NewDoubleArray(result.frequencies.size());
    env->SetDoubleArrayRegion(freqArray, 0, result.frequencies.size(), result.frequencies.data());
    
    jdoubleArray magArray = env->NewDoubleArray(result.magnitudes.size());
    env->SetDoubleArrayRegion(magArray, 0, result.magnitudes.size(), result.magnitudes.data());
    
    return env->NewObject(fftResultClass, constructor, freqArray, magArray);
}

// Edge Detection JNI
JNIEXPORT jintArray JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_detectEdges(
        JNIEnv* env, jobject /* this */, jdoubleArray signalArray, jint edgeType) {
    EdgeDetection detector;
    
    jsize length = env->GetArrayLength(signalArray);
    jdouble* signalData = env->GetDoubleArrayElements(signalArray, nullptr);
    
    std::vector<double> signal(signalData, signalData + length);
    env->ReleaseDoubleArrayElements(signalArray, signalData, JNI_ABORT);
    
    std::vector<int> edges = detector.detectEdges(signal, static_cast<EdgeType>(edgeType));
    
    jintArray result = env->NewIntArray(edges.size());
    env->SetIntArrayRegion(result, 0, edges.size(), edges.data());
    
    return result;
}

// Douglas-Peucker JNI
JNIEXPORT jintArray JNICALL
Java_com_brainwonderful_naoyu_1bluetooth_1android_filters_NativeFilters_simplifyDouglasPeucker(
        JNIEnv* env, jobject /* this */, jdoubleArray xArray, jdoubleArray yArray, jdouble epsilon) {
    DouglasPeucker simplifier(epsilon);
    
    jsize length = env->GetArrayLength(xArray);
    jdouble* xData = env->GetDoubleArrayElements(xArray, nullptr);
    jdouble* yData = env->GetDoubleArrayElements(yArray, nullptr);
    
    std::vector<Point> points;
    for (jsize i = 0; i < length; i++) {
        points.emplace_back(xData[i], yData[i]);
    }
    
    env->ReleaseDoubleArrayElements(xArray, xData, JNI_ABORT);
    env->ReleaseDoubleArrayElements(yArray, yData, JNI_ABORT);
    
    std::vector<int> indices = simplifier.simplifyIndices(points);
    
    jintArray result = env->NewIntArray(indices.size());
    env->SetIntArrayRegion(result, 0, indices.size(), indices.data());
    
    return result;
}

} // extern "C"