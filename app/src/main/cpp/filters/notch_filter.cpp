#include "notch_filter.h"

namespace naoyu {

NotchFilter::NotchFilter(double sampleRate, double notchFreq, double beta) 
    : fs(sampleRate), f0(notchFreq), beta(beta) {
    calculateCoefficients();
    reset();
}

void NotchFilter::calculateCoefficients() {
    // IIR Notch Filter design
    // Based on UniApp implementation
    double alpha = -2.0 * cos(2.0 * M_PI * f0 / fs);
    
    // Numerator coefficients
    b[0] = 1.0;
    b[1] = alpha;
    b[2] = 1.0;
    
    // Denominator coefficients
    a[0] = 1.0;
    a[1] = alpha * beta;
    a[2] = beta * beta;
}

double NotchFilter::process(double input) {
    // Direct Form II implementation
    // w[n] = x[n] - a1*w[n-1] - a2*w[n-2]
    double w = input - a[1] * w0[1] - a[2] * w0[2];
    
    // y[n] = b0*w[n] + b1*w[n-1] + b2*w[n-2]
    double output = b[0] * w + b[1] * w0[1] + b[2] * w0[2];
    
    // Update state variables
    w0[2] = w0[1];
    w0[1] = w;
    
    return output;
}

void NotchFilter::reset() {
    w0[0] = 0.0;
    w0[1] = 0.0;
    w0[2] = 0.0;
}

}