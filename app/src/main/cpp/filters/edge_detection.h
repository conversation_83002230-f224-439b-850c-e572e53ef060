#ifndef NAOYU_EDGE_DETECTION_H
#define NAOYU_EDGE_DETECTION_H

#include <vector>

namespace naoyu {

enum class EdgeType {
    RISING,     // Negative to positive zero-crossing
    DESCENDING  // Positive to negative zero-crossing
};

class EdgeDetection {
public:
    EdgeDetection() = default;
    ~EdgeDetection() = default;
    
    std::vector<int> detectEdges(const std::vector<double>& signal, EdgeType type);
    
private:
    bool isZeroCrossing(double prev, double curr, EdgeType type);
};

}

#endif // NAOYU_EDGE_DETECTION_H