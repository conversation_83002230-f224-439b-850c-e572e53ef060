#ifndef NAOYU_NOTCH_FILTER_H
#define NAOYU_NOTCH_FILTER_H

#include <vector>
#include <cmath>

namespace naoyu {

class NotchFilter {
public:
    NotchFilter(double sampleRate, double notchFreq = 50.0, double beta = 0.96);
    ~NotchFilter() = default;
    
    double process(double input);
    void reset();
    
private:
    double fs;      // Sample rate
    double f0;      // Notch frequency
    double beta;    // Bandwidth factor
    
    // Filter coefficients
    double b[3];    // Numerator coefficients
    double a[3];    // Denominator coefficients
    
    // State variables (Direct Form II)
    double w0[3];   // State variables
    
    void calculateCoefficients();
};

}

#endif // NAOYU_NOTCH_FILTER_H