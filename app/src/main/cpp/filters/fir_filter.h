#ifndef NAOYU_FIR_FILTER_H
#define NAOYU_FIR_FILTER_H

#include <vector>
#include <cmath>

namespace naoyu {

enum class FilterType {
    LOWPASS,
    HIGHPASS,
    BANDPASS
};

class FIRFilter {
public:
    FIRFilter(double sampleRate, FilterType type, double freq1, double freq2 = 0.0);
    ~FIRFilter() = default;
    
    double process(double input);
    void reset();
    std::vector<double> processBlock(const std::vector<double>& input);
    
private:
    double fs;                      // Sample rate
    FilterType filterType;          // Filter type
    double cutoffFreq1;            // First cutoff frequency
    double cutoffFreq2;            // Second cutoff frequency (for bandpass)
    
    std::vector<double> coefficients;  // FIR coefficients
    std::vector<double> buffer;        // Circular buffer for convolution
    int bufferIndex;                   // Current position in circular buffer
    
    void designFilter();
    void normalizeCoefficients(int M);
    double hammingWindow(int i, int N);
    double kaiserWindow(int i, int N, double beta);
    double besselI0(double x);
};

}

#endif // NAOYU_FIR_FILTER_H