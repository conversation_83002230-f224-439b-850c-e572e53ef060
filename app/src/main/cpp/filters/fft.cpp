#include "fft.h"
#include <algorithm>
#include <stdexcept>
#include <numeric>

namespace naoyu {

FFT::FFT(int size) : fftSize(size) {
    // Validate that size is power of 2
    if (size <= 0 || (size & (size - 1)) != 0) {
        throw std::invalid_argument("FFT size must be a positive power of 2");
    }
    computeTwiddle();
}

void FFT::computeTwiddle() {
    twiddle.resize(fftSize / 2);
    for (int i = 0; i < fftSize / 2; i++) {
        double angle = -2.0 * M_PI * i / fftSize;
        twiddle[i] = std::complex<double>(cos(angle), sin(angle));
    }
}

FFTResult FFT::compute(const std::vector<double>& input, double sampleRate, WindowType windowType) {
    FFTResult result;
    
    // Prepare input data - copy to avoid modifying original
    std::vector<double> processedInput(fftSize);
    for (int i = 0; i < fftSize && i < input.size(); i++) {
        processedInput[i] = input[i];
    }
    // Zero-pad if input is shorter than fftSize
    for (int i = input.size(); i < fftSize; i++) {
        processedInput[i] = 0.0;
    }
    
    // Step 1: Remove DC offset (mean removal) - matches Python preprocessing
    removeDCOffset(processedInput);
    
    // Step 2: Generate and apply window function
    std::vector<double> window = generateWindow(fftSize, windowType);
    applyWindow(processedInput, window);
    
    // Step 3: Calculate window correction factor (matches Python)
    double windowCorrection = calculateWindowCorrection(window);
    
    // Step 4: Prepare complex data for FFT
    std::vector<std::complex<double>> data(fftSize);
    for (int i = 0; i < fftSize; i++) {
        data[i] = std::complex<double>(processedInput[i], 0.0);
    }
    
    // Step 5: Perform FFT
    fft(data);
    
    // Step 6: Calculate frequencies and magnitudes with proper normalization
    double freqResolution = sampleRate / fftSize;
    int halfSize = fftSize / 2 + 1;  // Include Nyquist frequency
    
    result.frequencies.resize(halfSize);
    result.magnitudes.resize(halfSize);
    
    for (int i = 0; i < halfSize; i++) {
        result.frequencies[i] = i * freqResolution;
        
        // Python-compatible amplitude calculation:
        // amplitude = 2.0 * abs(spectrum) / len(data) * window_correction
        double rawMagnitude = std::abs(data[i]);
        double normalizedMagnitude = rawMagnitude / fftSize;  // Normalize by FFT size
        
        if (i == 0 || (fftSize % 2 == 0 && i == halfSize - 1)) {
            // DC and Nyquist components don't get the 2.0 factor
            result.magnitudes[i] = normalizedMagnitude * windowCorrection;
        } else {
            // All other frequencies get 2.0 factor for single-sided spectrum
            result.magnitudes[i] = 2.0 * normalizedMagnitude * windowCorrection;
        }
    }
    
    return result;
}

void FFT::removeDCOffset(std::vector<double>& data) {
    // Calculate mean
    double mean = std::accumulate(data.begin(), data.end(), 0.0) / data.size();
    
    // Remove mean from all samples
    for (double& sample : data) {
        sample -= mean;
    }
}

std::vector<double> FFT::generateWindow(int size, WindowType windowType) {
    std::vector<double> window(size);
    
    switch (windowType) {
        case WindowType::RECTANGULAR:
            std::fill(window.begin(), window.end(), 1.0);
            break;
            
        case WindowType::HANNING:
            for (int i = 0; i < size; i++) {
                window[i] = 0.5 * (1.0 - cos(2.0 * M_PI * i / (size - 1)));
            }
            break;
            
        case WindowType::HAMMING:
            for (int i = 0; i < size; i++) {
                window[i] = 0.54 - 0.46 * cos(2.0 * M_PI * i / (size - 1));
            }
            break;
            
        case WindowType::BLACKMAN:
            for (int i = 0; i < size; i++) {
                double factor = 2.0 * M_PI * i / (size - 1);
                window[i] = 0.42 - 0.5 * cos(factor) + 0.08 * cos(2.0 * factor);
            }
            break;
            
        default:
            std::fill(window.begin(), window.end(), 1.0);
            break;
    }
    
    return window;
}

double FFT::calculateWindowCorrection(const std::vector<double>& window) {
    // Calculate coherent gain (mean of window) - matches Python implementation
    double sum = std::accumulate(window.begin(), window.end(), 0.0);
    double mean = sum / window.size();
    
    // Window correction factor = 1.0 / mean(window)
    return (mean > 0.0) ? (1.0 / mean) : 1.0;
}

void FFT::applyWindow(std::vector<double>& data, const std::vector<double>& window) {
    for (size_t i = 0; i < data.size() && i < window.size(); i++) {
        data[i] *= window[i];
    }
}

void FFT::fft(std::vector<std::complex<double>>& data) {
    // Bit reversal
    bitReverse(data);
    
    // Cooley-Tukey FFT
    for (int stage = 1; stage < fftSize; stage *= 2) {
        int stageSize = stage * 2;
        
        for (int k = 0; k < fftSize; k += stageSize) {
            for (int j = 0; j < stage; j++) {
                int twiddleIndex = j * fftSize / stageSize;
                std::complex<double> t = twiddle[twiddleIndex] * data[k + j + stage];
                data[k + j + stage] = data[k + j] - t;
                data[k + j] = data[k + j] + t;
            }
        }
    }
}

void FFT::bitReverse(std::vector<std::complex<double>>& data) {
    int bits = 0;
    int n = fftSize;
    while (n > 1) {
        bits++;
        n >>= 1;
    }
    
    for (int i = 0; i < fftSize; i++) {
        int rev = reverseBits(i, bits);
        if (i < rev) {
            std::swap(data[i], data[rev]);
        }
    }
}

int FFT::reverseBits(int num, int bits) {
    int result = 0;
    for (int i = 0; i < bits; i++) {
        result = (result << 1) | (num & 1);
        num >>= 1;
    }
    return result;
}

}