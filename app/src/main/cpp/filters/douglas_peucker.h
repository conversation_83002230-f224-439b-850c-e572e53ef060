#ifndef NAOYU_DOUGLAS_PEUCKER_H
#define NAOYU_DOUGLAS_PEUCKER_H

#include <vector>

namespace naoyu {

struct Point {
    double x;
    double y;
    
    Point(double x, double y) : x(x), y(y) {}
};

class <PERSON><PERSON><PERSON>cker {
public:
    <PERSON><PERSON><PERSON><PERSON>(double epsilon = 10.0);
    ~<PERSON><PERSON><PERSON><PERSON>() = default;
    
    std::vector<Point> simplify(const std::vector<Point>& points);
    std::vector<int> simplifyIndices(const std::vector<Point>& points);
    
private:
    double epsilon;  // Maximum distance threshold
    
    double perpendicularDistance(const Point& point, const Point& lineStart, const Point& lineEnd);
    void douglasPeuckerRecursive(const std::vector<Point>& points, 
                                 int startIdx, int endIdx, 
                                 std::vector<bool>& keepPoints);
};

}

#endif // NAOYU_DOUGLAS_PEUCKER_H