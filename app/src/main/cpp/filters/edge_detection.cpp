#include "edge_detection.h"

namespace naoyu {

std::vector<int> EdgeDetection::detectEdges(const std::vector<double>& signal, EdgeType type) {
    std::vector<int> edges;
    
    if (signal.size() < 2) {
        return edges;
    }
    
    for (size_t i = 1; i < signal.size(); i++) {
        if (isZeroCrossing(signal[i - 1], signal[i], type)) {
            edges.push_back(static_cast<int>(i));
        }
    }
    
    return edges;
}

bool EdgeDetection::isZeroCrossing(double prev, double curr, EdgeType type) {
    switch (type) {
        case EdgeType::RISING:
            // Negative to positive
            return prev < 0.0 && curr >= 0.0;
            
        case EdgeType::DESCENDING:
            // Positive to negative
            return prev >= 0.0 && curr < 0.0;
            
        default:
            return false;
    }
}

}