#include "douglas_peucker.h"
#include <cmath>
#include <algorithm>

namespace naoyu {

DouglasPeucker::<PERSON><PERSON><PERSON><PERSON>(double epsilon) : epsilon(epsilon) {}

std::vector<Point> DouglasPeucker::simplify(const std::vector<Point>& points) {
    if (points.size() <= 2) {
        return points;
    }
    
    std::vector<bool> keepPoints(points.size(), false);
    keepPoints[0] = true;
    keepPoints[points.size() - 1] = true;
    
    douglasPeuckerRecursive(points, 0, points.size() - 1, keepPoints);
    
    std::vector<Point> result;
    for (size_t i = 0; i < points.size(); i++) {
        if (keepPoints[i]) {
            result.push_back(points[i]);
        }
    }
    
    return result;
}

std::vector<int> DouglasPeucker::simplifyIndices(const std::vector<Point>& points) {
    if (points.size() <= 2) {
        std::vector<int> indices;
        for (size_t i = 0; i < points.size(); i++) {
            indices.push_back(static_cast<int>(i));
        }
        return indices;
    }
    
    std::vector<bool> keepPoints(points.size(), false);
    keepPoints[0] = true;
    keepPoints[points.size() - 1] = true;
    
    douglasPeuckerRecursive(points, 0, points.size() - 1, keepPoints);
    
    std::vector<int> indices;
    for (size_t i = 0; i < points.size(); i++) {
        if (keepPoints[i]) {
            indices.push_back(static_cast<int>(i));
        }
    }
    
    return indices;
}

void DouglasPeucker::douglasPeuckerRecursive(const std::vector<Point>& points, 
                                             int startIdx, int endIdx, 
                                             std::vector<bool>& keepPoints) {
    if (endIdx <= startIdx + 1) {
        return;
    }
    
    double maxDistance = 0.0;
    int maxIndex = 0;
    
    // Find the point with maximum distance
    for (int i = startIdx + 1; i < endIdx; i++) {
        double distance = perpendicularDistance(points[i], points[startIdx], points[endIdx]);
        if (distance > maxDistance) {
            maxDistance = distance;
            maxIndex = i;
        }
    }
    
    // If max distance is greater than epsilon, recursively simplify
    if (maxDistance > epsilon) {
        keepPoints[maxIndex] = true;
        douglasPeuckerRecursive(points, startIdx, maxIndex, keepPoints);
        douglasPeuckerRecursive(points, maxIndex, endIdx, keepPoints);
    }
}

double DouglasPeucker::perpendicularDistance(const Point& point, 
                                            const Point& lineStart, 
                                            const Point& lineEnd) {
    double dx = lineEnd.x - lineStart.x;
    double dy = lineEnd.y - lineStart.y;
    
    if (dx == 0.0 && dy == 0.0) {
        // Line start and end are the same
        dx = point.x - lineStart.x;
        dy = point.y - lineStart.y;
        return sqrt(dx * dx + dy * dy);
    }
    
    double t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / (dx * dx + dy * dy);
    
    if (t < 0.0) {
        // Closest point is lineStart
        dx = point.x - lineStart.x;
        dy = point.y - lineStart.y;
    } else if (t > 1.0) {
        // Closest point is lineEnd
        dx = point.x - lineEnd.x;
        dy = point.y - lineEnd.y;
    } else {
        // Closest point is on the line
        double nearX = lineStart.x + t * dx;
        double nearY = lineStart.y + t * dy;
        dx = point.x - nearX;
        dy = point.y - nearY;
    }
    
    return sqrt(dx * dx + dy * dy);
}

}