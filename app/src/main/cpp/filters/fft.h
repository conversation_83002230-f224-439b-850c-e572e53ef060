#ifndef NAOYU_FFT_H
#define NAOYU_FFT_H

#include <vector>
#include <complex>
#include <cmath>

namespace naoyu {

/**
 * Window types for FFT analysis
 */
enum class WindowType {
    RECTANGULAR = 0,
    HANNING = 1,
    HAMMING = 2,
    BLACKMAN = 3
};

/**
 * FFT implementation for EEG signal analysis
 * 
 * This implementation matches Python eeg_spectrum.py behavior:
 * - Applies window functions with proper compensation
 * - Performs correct normalization (2.0 * abs(fft) / N * window_correction)
 * - Returns physically meaningful amplitude values
 */

struct FFTResult {
    std::vector<double> frequencies;
    std::vector<double> magnitudes;
};

class FFT {
public:
    FFT(int size = 512);
    ~FFT() = default;
    
    FFTResult compute(const std::vector<double>& input, double sampleRate, WindowType windowType = WindowType::HANNING);
    
private:
    int fftSize;
    std::vector<std::complex<double>> twiddle;
    
    void computeTwiddle();
    void fft(std::vector<std::complex<double>>& data);
    void bitReverse(std::vector<std::complex<double>>& data);
    int reverseBits(int num, int bits);
    
    // Window function methods
    std::vector<double> generateWindow(int size, WindowType windowType);
    double calculateWindowCorrection(const std::vector<double>& window);
    void applyWindow(std::vector<double>& data, const std::vector<double>& window);
    void removeDCOffset(std::vector<double>& data);
};

}

#endif // NAOYU_FFT_H