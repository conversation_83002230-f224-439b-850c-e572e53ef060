#include "fir_filter.h"
#include <algorithm>
#include <numeric>
#include <stdexcept>
#include <cmath>

namespace naoyu {

FIRFilter::FIRFilter(double sampleRate, FilterType type, double freq1, double freq2)
    : fs(sampleRate), filterType(type), cutoffFreq1(freq1), cutoffFreq2(freq2), bufferIndex(0) {
    
    // Validate parameters
    if (fs <= 0) {
        throw std::invalid_argument("Sample rate must be positive");
    }
    
    double nyquist = fs / 2.0;
    
    // Validate frequencies based on filter type
    switch (filterType) {
        case FilterType::LOWPASS:
            if (cutoffFreq1 <= 0 || cutoffFreq1 >= nyquist) {
                throw std::invalid_argument("Lowpass cutoff frequency must be between 0 and Nyquist frequency");
            }
            break;
        case FilterType::HIGHPASS:
            if (cutoffFreq1 <= 0 || cutoffFreq1 >= nyquist) {
                throw std::invalid_argument("Highpass cutoff frequency must be between 0 and Nyquist frequency");
            }
            break;
        case FilterType::BANDPASS:
            if (cutoffFreq1 <= 0 || cutoffFreq2 <= 0 || 
                cutoffFreq1 >= nyquist || cutoffFreq2 >= nyquist ||
                cutoffFreq1 >= cutoffFreq2) {
                throw std::invalid_argument("Bandpass frequencies must be valid and freq1 < freq2");
            }
            break;
    }
    
    designFilter();
    reset();
}

void FIRFilter::designFilter() {
    // Use higher filter order based on filter type for sharp transition
    int order;
    switch (filterType) {
        case FilterType::LOWPASS:
            // Lowpass needs extra sharp cutoff for good stopband rejection
            order = static_cast<int>(fs * 1.3) + 1;
            break;
        case FilterType::HIGHPASS:
            order = static_cast<int>(fs * 1.2) + 1;
            break;
        case FilterType::BANDPASS:
            // Bandpass needs highest order for dual cutoffs
            order = static_cast<int>(fs * 1.6) + 1;
            break;
    }
    if (order % 2 == 0) order++;  // Ensure odd order
    
    // Allow higher order for better performance
    if (order > 511) order = 511;
    
    coefficients.resize(order);
    int M = (order - 1) / 2;
    
    // Calculate normalized cutoff frequencies
    double wc1 = 2.0 * M_PI * cutoffFreq1 / fs;
    double wc2 = 2.0 * M_PI * cutoffFreq2 / fs;
    
    // Adjust for highpass filter design
    if (filterType == FilterType::HIGHPASS) {
        wc2 = wc1;  // Use cutoffFreq1 as the actual cutoff
        wc1 = 0.0;  // Start from DC
    }
    
    // Design the ideal impulse response
    for (int i = 0; i < order; i++) {
        int n = i - M;
        double h = 0.0;
        
        if (n == 0) {
            // Handle the n=0 case to avoid division by zero
            switch (filterType) {
                case FilterType::LOWPASS:
                    h = wc1 / M_PI;
                    break;
                case FilterType::HIGHPASS:
                    h = 1.0 - wc2 / M_PI;
                    break;
                case FilterType::BANDPASS:
                    h = (wc2 - wc1) / M_PI;
                    break;
            }
        } else {
            double s = static_cast<double>(n);
            switch (filterType) {
                case FilterType::LOWPASS:
                    h = sin(wc1 * s) / (M_PI * s);
                    break;
                case FilterType::HIGHPASS:
                    h = (sin(M_PI * s) - sin(wc2 * s)) / (M_PI * s);
                    break;
                case FilterType::BANDPASS:
                    h = (sin(wc2 * s) - sin(wc1 * s)) / (M_PI * s);
                    break;
            }
        }
        
        // Use Hamming window for better passband preservation
        double window = hammingWindow(i, order);
        coefficients[i] = h * window;
    }
    
    // Normalize the filter coefficients
    normalizeCoefficients(M);
    
    // Initialize buffer
    buffer.resize(order, 0.0);
}

void FIRFilter::normalizeCoefficients(int M) {
    double sum = 0.0;
    
    switch (filterType) {
        case FilterType::LOWPASS:
            // For lowpass, normalize for DC gain = 1
            for (double c : coefficients) {
                sum += c;
            }
            break;
            
        case FilterType::HIGHPASS:
            // For highpass, normalize for Nyquist frequency gain = 1
            for (size_t i = 0; i < coefficients.size(); i++) {
                sum += coefficients[i] * (i % 2 == 0 ? 1.0 : -1.0);
            }
            break;
            
        case FilterType::BANDPASS: {
            // For bandpass, normalize at center frequency
            double centerFreq = (cutoffFreq1 + cutoffFreq2) / 2.0;
            double wc = 2.0 * M_PI * centerFreq / fs;
            
            for (size_t i = 0; i < coefficients.size(); i++) {
                int n = static_cast<int>(i) - M;
                sum += coefficients[i] * cos(wc * n);
            }
            sum = fabs(sum);
            break;
        }
    }
    
    // Apply normalization if sum is valid
    if (fabs(sum) > 1e-12) {
        for (double& c : coefficients) {
            c /= sum;
        }
    }
}

double FIRFilter::hammingWindow(int i, int N) {
    return 0.54 - 0.46 * cos(2.0 * M_PI * i / (N - 1));
}

double FIRFilter::kaiserWindow(int i, int N, double beta) {
    double alpha = (N - 1) / 2.0;
    double x = (i - alpha) / alpha;
    return besselI0(beta * sqrt(1.0 - x * x)) / besselI0(beta);
}

double FIRFilter::besselI0(double x) {
    // Modified Bessel function of the first kind, order 0
    double sum = 1.0;
    double term = 1.0;
    double x_half_sq = (x / 2.0) * (x / 2.0);
    
    for (int k = 1; k <= 20; k++) {
        term *= x_half_sq / (k * k);
        sum += term;
        if (term < 1e-10) break;
    }
    
    return sum;
}

double FIRFilter::process(double input) {
    // Check for valid input
    if (!std::isfinite(input)) {
        return 0.0;
    }
    
    // Check if filter is properly initialized
    if (coefficients.empty() || buffer.empty()) {
        return input;
    }
    
    // Update circular buffer
    buffer[bufferIndex] = input;
    
    // Convolution
    double output = 0.0;
    int N = static_cast<int>(coefficients.size());
    
    for (int i = 0; i < N; i++) {
        int idx = (bufferIndex - i + N) % N;
        output += coefficients[i] * buffer[idx];
    }
    
    // Update buffer index
    bufferIndex = (bufferIndex + 1) % N;
    
    // Check for valid output
    if (!std::isfinite(output)) {
        return 0.0;
    }
    
    return output;
}

std::vector<double> FIRFilter::processBlock(const std::vector<double>& input) {
    if (input.empty()) {
        return input;
    }
    
    std::vector<double> output(input.size());
    
    // Check if filter is properly initialized
    if (coefficients.empty() || buffer.empty()) {
        return input;
    }
    
    try {
        for (size_t i = 0; i < input.size(); i++) {
            if (std::isfinite(input[i])) {
                output[i] = process(input[i]);
            } else {
                output[i] = 0.0;
            }
        }
    } catch (const std::exception& e) {
        // Return original input on error
        return input;
    }
    
    return output;
}

void FIRFilter::reset() {
    std::fill(buffer.begin(), buffer.end(), 0.0);
    bufferIndex = 0;
}

}