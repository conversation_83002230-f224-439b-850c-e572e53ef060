/**
 *
 * "A Collection of Useful C++ Classes for Digital Signal Processing"
 * By <PERSON> and <PERSON><PERSON>
 * 
 * Official project location:
 * https://github.com/berndporr/iir1
 * 
 * --------------------------------------------------------------------------
 * 
 * License: MIT License (http://www.opensource.org/licenses/mit-license.php)
 * Copyright (c) 2009 by <PERSON>
 * Copyright (c) 2011 by <PERSON><PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 **/

#ifndef IIR_H
#define IIR_H

//
// Include this file in your application to get everything
//

//#include "iir/Common.h"
//
//#include "iir/Biquad.h"
//#include "iir/Cascade.h"
//#include "iir/PoleFilter.h"
//#include "iir/State.h"
//
//#include "iir/Butterworth.h"
//#include "iir/ChebyshevI.h"
//#include "iir/ChebyshevII.h"
//#include "iir/Custom.h"
//#include "iir/RBJ.h"

#include "filters2/iir/Common.h"

#include "filters2/iir/Biquad.h"
#include "filters2/iir/Cascade.h"
#include "filters2/iir/PoleFilter.h"
#include "filters2/iir/State.h"

#include "filters2/iir/Butterworth.h"
#include "filters2/iir/ChebyshevI.h"
#include "filters2/iir/ChebyshevII.h"
#include "filters2/iir/Custom.h"
#include "filters2/iir/RBJ.h"

#endif
