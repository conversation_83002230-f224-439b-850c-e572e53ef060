//
// Created by 许江涛 on 2022/10/25.
//

#include "Filter.h"

#include "Iir.h"
extern "C" {
    #include "filters2/Moving_average/moving_average.h"
}

const int order = 4;    // 4th order (=2 biquads)
const int fs = 1283;    //采样率
const double mains = 50; //陷波频率
const double emg_max_f   = 500;  //低通截止频率
const double emg_min_f   = 20;   //高通截止频率
const float Q = 10;         //Q因子 10 ~ 20之间
const float _Range = 0.1;   //Moving Average Range

const float ratio = 10.0;

class Filter {
private:
    Filter();
    static Filter* instance;
    //滤波器
    Iir::Butterworth::LowPass<order> RLP;
    Iir::Butterworth::HighPass<order> RHP;
    Iir::RBJ::IIRNotch RNotch;
    Moving_Average *RMA;

    Iir::Butterworth::LowPass<order> LLP;
    Iir::Butterworth::HighPass<order> LHP;
    Iir::RBJ::IIRNotch LNotch;
    Moving_Average *LMA;

public:
    static Filter* getInstance();
    float updateRight(int data);
    float updateLeft(int data);

    void freeMovingAverage();
};

Filter* Filter::instance = 0;

Filter* Filter::getInstance(){
    if(!instance){
        instance = new Filter();
    }
    return instance;
}

float Filter::updateLeft(int data) {
    float a = LHP.filter(data);
    a = LLP.filter(a);
    a = LNotch.filter(a);

    a = get_moving_average(LMA, abs(a));

    a = a / ratio;

    return a;
}

float Filter::updateRight(int data) {
    float a = RHP.filter(data);
    a = RLP.filter(a);
    a = RNotch.filter(a);

    //moving average
    a = get_moving_average(RMA, abs(a));
    a = a / ratio;

    return a;
}

Filter::Filter() {
    //初始化滤波器
    RNotch.setup(fs, mains, Q);
    RLP.setup(fs, emg_max_f);
    RHP.setup(fs, emg_min_f);
    //初始化Moving Average
    RMA = new_moving_average(fs * _Range);

    LNotch.setup(fs, mains, Q);
    LLP.setup(fs, emg_max_f);
    LHP.setup(fs, emg_min_f);

    LMA = new_moving_average(fs * _Range);
}

void Filter::freeMovingAverage() {
    free_moving_average(RMA);
    RMA = new_moving_average(fs * _Range);
    free_moving_average(LMA);
    LMA = new_moving_average(fs * _Range);
}


float updateLeft_interface(int data){
    return Filter::getInstance()->updateLeft(data);
}

float updateRight_interface(int data) {
    return Filter::getInstance()->updateRight(data);
}

void freeMovingAverage() {
    Filter::getInstance()->freeMovingAverage();
}

