//
// Created by 许江涛 on 2022/10/24.
//
#include "jni.h"
#include "Filter.h"

extern "C"
JNIEXPORT jfloat JNICALL
Java_com_shanlomed_medical_reposity_XL_1Filter_updateLeft(JNIEnv *env, jobject thiz, jint data) {
    return updateLeft_interface(data);
}
extern "C"
JNIEXPORT jfloat JNICALL
Java_com_shanlomed_medical_reposity_XL_1Filter_updateRight(JNIEnv *env, jobject thiz, jint data) {
    return updateRight_interface(data);
}