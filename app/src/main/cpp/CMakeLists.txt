cmake_minimum_required(VERSION 3.22.1)
project("naoyu_filters")

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set linker flags for 16KB page size alignment
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,-z,max-page-size=16384")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-z,max-page-size=16384")

# Add include directories
include_directories(../../../../../../算法/cpp-2025-0605)

# Source files
set(SOURCES
        naoyu_filters.cpp
        filters/notch_filter.cpp
        filters/fir_filter.cpp
        filters/fft.cpp
        filters/edge_detection.cpp
        filters/douglas_peucker.cpp
)

# Create shared library
add_library(naoyu_filters SHARED ${SOURCES})

# Link libraries
find_library(log-lib log)
target_link_libraries(naoyu_filters ${log-lib})