package com.brainwonderful.naoyu_bluetooth_android.filters

data class Point(val x: Double, val y: Double)

class DouglasPeucker {
    private val nativeFilters = NativeFilters()
    
    fun simplify(points: List<Point>, epsilon: Double = 10.0): List<Point> {
        if (points.size <= 2) return points
        
        val xArray = DoubleArray(points.size) { points[it].x }
        val yArray = DoubleArray(points.size) { points[it].y }
        
        val indices = nativeFilters.simplifyDouglasPeucker(xArray, yArray, epsilon)
        
        return indices.map { points[it] }
    }
    
    fun simplifyIndices(points: List<Point>, epsilon: Double = 10.0): IntArray {
        if (points.size <= 2) return IntArray(points.size) { it }
        
        val xArray = DoubleArray(points.size) { points[it].x }
        val yArray = DoubleArray(points.size) { points[it].y }
        
        return nativeFilters.simplifyDoug<PERSON><PERSON><PERSON><PERSON>(xArray, yArray, epsilon)
    }
}