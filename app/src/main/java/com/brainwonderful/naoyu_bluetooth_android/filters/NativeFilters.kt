package com.brainwonderful.naoyu_bluetooth_android.filters

class NativeFilters {
    companion object {
        init {
            System.loadLibrary("naoyu_filters")
        }
        
        // Filter type constants (FIR)
        const val FILTER_TYPE_LOWPASS = 0
        const val FILTER_TYPE_HIGHPASS = 1
        const val FILTER_TYPE_BANDPASS = 2
        
        // IIR Filter type constants
        const val IIR_FILTER_TYPE_LOWPASS = 0
        const val IIR_FILTER_TYPE_HIGHPASS = 1
        const val IIR_FILTER_TYPE_BANDPASS = 2
        
        // Edge type constants
        const val EDGE_TYPE_RISING = 0
        const val EDGE_TYPE_DESCENDING = 1
        
        // Window type constants for FFT
        const val WINDOW_RECTANGULAR = 0
        const val WINDOW_HANNING = 1
        const val WINDOW_HAMMING = 2
        const val WINDOW_BLACKMAN = 3
        
    }
    
    // Notch Filter native methods 
    external fun createNotchFilter(sampleRate: Double, notchFreq: Double = 50.0, qualityFactor: Double = 30.0): Long
    external fun createNotchFilterWithBeta(sampleRate: Double, notchFreq: Double, beta: Double): Long
    external fun destroyNotchFilter(handle: Long)
    external fun processNotchFilter(handle: Long, input: Double): Double
    external fun resetNotchFilter(handle: Long)
    
    // FIR Filter native methods
    external fun createFIRFilter(sampleRate: Double, filterType: Int, freq1: Double, freq2: Double = 0.0): Long
    external fun destroyFIRFilter(handle: Long)
    external fun processFIRFilter(handle: Long, input: Double): Double
    external fun processFIRFilterBlock(handle: Long, input: DoubleArray): DoubleArray
    
    // IIR Filter native methods (temporarily disabled - to be implemented)
    // external fun createIIRFilter(sampleRate: Double, filterType: Int, freq1: Double, freq2: Double = 0.0, order: Int = 4): Long
    // external fun destroyIIRFilter(handle: Long)
    // external fun processIIRFilter(handle: Long, input: Double): Double
    // external fun processIIRFilterBlock(handle: Long, input: DoubleArray): DoubleArray
    
    // FFT native methods
    external fun createFFT(size: Int = 512): Long
    external fun destroyFFT(handle: Long)
    external fun computeFFT(handle: Long, input: DoubleArray, sampleRate: Double): FFTResult
    external fun computeFFTWithWindow(handle: Long, input: DoubleArray, sampleRate: Double, windowType: Int): FFTResult
    
    // Edge Detection native methods
    external fun detectEdges(signal: DoubleArray, edgeType: Int): IntArray
    
    // Douglas-Peucker native methods
    external fun simplifyDouglasPeucker(xArray: DoubleArray, yArray: DoubleArray, epsilon: Double = 10.0): IntArray
}

data class FFTResult(
    val frequencies: DoubleArray,
    val magnitudes: DoubleArray
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FFTResult

        if (!frequencies.contentEquals(other.frequencies)) return false
        if (!magnitudes.contentEquals(other.magnitudes)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = frequencies.contentHashCode()
        result = 31 * result + magnitudes.contentHashCode()
        return result
    }
}