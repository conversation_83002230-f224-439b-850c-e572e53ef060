package com.brainwonderful.naoyu_bluetooth_android.filters

/**
 * Window types for FFT analysis matching Python eeg_spectrum.py
 */
enum class WindowType(val value: Int) {
    RECTANGULAR(NativeFilters.WINDOW_RECTANGULAR),
    HANNING(NativeFilters.WINDOW_HANNING),
    HAMMING(NativeFilters.WINDOW_HAMMING),
    BLACKMAN(NativeFilters.WINDOW_BLACKMAN)
}

class FFT(
    size: Int = 512
) : AutoCloseable {
    init {
        // Validate that size is power of 2
        require(size > 0 && (size and (size - 1)) == 0) {
            "FFT size must be a positive power of 2"
        }
    }
    
    private val nativeFilters = NativeFilters()
    private val handle: Long = nativeFilters.createFFT(size)
    
    /**
     * Compute FFT with default Hanning window (matches Python default)
     * Now returns physically meaningful amplitude values that match Python eeg_spectrum.py
     */
    fun compute(input: DoubleArray, sampleRate: Double): FFTResult {
        return computeWithWindow(input, sampleRate, WindowType.HANNING)
    }
    
    /**
     * Compute FFT with specified window function
     * 
     * This implementation now matches Python eeg_spectrum.py behavior:
     * - Removes DC offset (mean removal)
     * - Applies window function with proper compensation
     * - Returns normalized amplitude values with physical meaning
     * 
     * The amplitude values are now directly comparable to Python results.
     */
    fun computeWithWindow(input: DoubleArray, sampleRate: Double, window: WindowType): FFTResult {
        return nativeFilters.computeFFTWithWindow(handle, input, sampleRate, window.value)
    }
    
    override fun close() {
        nativeFilters.destroyFFT(handle)
    }
}