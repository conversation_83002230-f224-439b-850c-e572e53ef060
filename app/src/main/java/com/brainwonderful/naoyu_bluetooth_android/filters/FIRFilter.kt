package com.brainwonderful.naoyu_bluetooth_android.filters

enum class FilterType(val value: Int) {
    LOWPASS(NativeFilters.FILTER_TYPE_LOWPASS),
    HIGHPASS(NativeFilters.FILTER_TYPE_HIGHPASS),
    BANDPASS(NativeFilters.FILTER_TYPE_BANDPASS)
}

class FIRFilter(
    sampleRate: Double,
    filterType: FilterType,
    freq1: Double,
    freq2: Double = 0.0
) : AutoCloseable {
    private val nativeFilters = NativeFilters()
    private val handle: Long = nativeFilters.createFIRFilter(sampleRate, filterType.value, freq1, freq2)
    
    fun process(input: Double): Double {
        return nativeFilters.processFIRFilter(handle, input)
    }
    
    fun processBlock(input: DoubleArray): DoubleArray {
        return nativeFilters.processFIRFilterBlock(handle, input)
    }
    
    override fun close() {
        nativeFilters.destroyFIRFilter(handle)
    }
    
    companion object {
        fun createBandpass(sampleRate: Double, lowFreq: Double, highFreq: Double): FIRFilter {
            return FIRFilter(sampleRate, FilterType.BANDPASS, lowFreq, highFreq)
        }
        
        fun createLowpass(sampleRate: Double, cutoffFreq: Double): FIRFilter {
            return FIRFilter(sampleRate, FilterType.LOWPASS, cutoffFreq)
        }
        
        fun createHighpass(sampleRate: Double, cutoffFreq: Double): FIRFilter {
            return FIRFilter(sampleRate, FilterType.HIGHPASS, cutoffFreq)
        }
    }
}