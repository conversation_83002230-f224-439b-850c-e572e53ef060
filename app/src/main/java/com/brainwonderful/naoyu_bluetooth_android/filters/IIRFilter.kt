package com.brainwonderful.naoyu_bluetooth_android.filters

/**
 * IIR (Infinite Impulse Response) Filter implemented using FIR filters
 * This is a temporary implementation using FIR filters until native IIR is ready
 * 
 * Features:
 * - Uses FIR filters as backend implementation
 * - Support for lowpass, highpass, and bandpass filters
 * - Compatible API with intended IIR interface
 * - Parameter validation for frequency ranges
 */
class IIRFilter(
    private val sampleRate: Double,
    private val filterType: FilterType,
    private val freq1: Double,
    private val freq2: Double = 0.0,
    private val order: Int = 4  // Currently unused, kept for API compatibility
) {
    enum class FilterType(val value: Int) {
        LOWPASS(0),
        HIGHPASS(1), 
        BANDPASS(2)
    }
    
    // Use FIR filter as backend implementation
    private val firFilter: FIRFilter
    
    init {
        // Validate parameters
        validateParameters()
        
        // Create corresponding FIR filter
        firFilter = when (filterType) {
            FilterType.LOWPASS -> FIRFilter.createLowpass(sampleRate, freq1)
            FilterType.HIGHPASS -> FIRFilter.createHighpass(sampleRate, freq1)
            FilterType.BANDPASS -> FIRFilter.createBandpass(sampleRate, freq1, freq2)
        }
    }
    
    /**
     * Validate filter parameters to match Python scipy.signal behavior exactly
     */
    private fun validateParameters() {
        if (sampleRate <= 0) {
            throw IllegalArgumentException("Sample rate must be positive")
        }
        
        if (order <= 0) {
            throw IllegalArgumentException("Filter order must be positive")
        }
        
        val nyquist = sampleRate / 2.0
        
        when (filterType) {
            FilterType.LOWPASS, FilterType.HIGHPASS -> {
                if (freq1 <= 0 || freq1 >= nyquist) {
                    throw IllegalArgumentException("Cutoff frequency must be between 0 and Nyquist frequency ($nyquist Hz)")
                }
            }
            FilterType.BANDPASS -> {
                if (freq1 <= 0 || freq2 <= 0 || freq1 >= nyquist || freq2 >= nyquist) {
                    throw IllegalArgumentException("Both frequencies must be between 0 and Nyquist frequency ($nyquist Hz)")
                }
                if (freq1 >= freq2) {
                    throw IllegalArgumentException("Lower frequency ($freq1) must be less than upper frequency ($freq2)")
                }
            }
        }
    }
    
    /**
     * Process a single sample through the filter
     * Note: This provides single-pass filtering using FIR implementation
     * For block processing, use processBlock() instead
     */
    fun process(input: Double): Double {
        return firFilter.process(input)
    }
    
    /**
     * Process a block of samples using FIR filtering
     * Note: This uses FIR filtering, not true zero-phase filtering (filtfilt)
     * True filtfilt will be implemented when native IIR is available
     */
    fun processBlock(input: DoubleArray): DoubleArray {
        return firFilter.processBlock(input)
    }
    
    /**
     * Clean up filter resources
     */
    fun destroy() {
        firFilter.close()
    }
    
    protected fun finalize() {
        destroy()
    }
    
    companion object {
        /**
         * Create a bandpass IIR filter matching Python's scipy.signal.butter exactly
         * Equivalent to: scipy.signal.butter(order, [lowCut, highCut], btype='bandpass', fs=sampleRate)
         * @param sampleRate Sampling rate in Hz (equivalent to Python's fs parameter)
         * @param lowCut Low cutoff frequency in Hz
         * @param highCut High cutoff frequency in Hz  
         * @param order Filter order (default: 4, same as scipy default)
         */
        fun createBandpassFilter(sampleRate: Double, lowCut: Double, highCut: Double, order: Int = 4): IIRFilter {
            return IIRFilter(sampleRate, FilterType.BANDPASS, lowCut, highCut, order)
        }
        
        /**
         * Create a highpass IIR filter matching Python's scipy.signal.butter exactly
         * Equivalent to: scipy.signal.butter(order, cutoff, btype='highpass', fs=sampleRate)
         * @param sampleRate Sampling rate in Hz (equivalent to Python's fs parameter)
         * @param cutoff Cutoff frequency in Hz
         * @param order Filter order (default: 4, same as scipy default)
         */
        fun createHighpassFilter(sampleRate: Double, cutoff: Double, order: Int = 4): IIRFilter {
            return IIRFilter(sampleRate, FilterType.HIGHPASS, cutoff, 0.0, order)
        }
        
        /**
         * Create a lowpass IIR filter matching Python's scipy.signal.butter exactly
         * Equivalent to: scipy.signal.butter(order, cutoff, btype='lowpass', fs=sampleRate)
         * @param sampleRate Sampling rate in Hz (equivalent to Python's fs parameter)
         * @param cutoff Cutoff frequency in Hz
         * @param order Filter order (default: 4, same as scipy default)
         */
        fun createLowpassFilter(sampleRate: Double, cutoff: Double, order: Int = 4): IIRFilter {
            return IIRFilter(sampleRate, FilterType.LOWPASS, cutoff, 0.0, order)
        }
        
        /**
         * Create Butterworth filter using Python-style normalized frequencies
         * This method accepts normalized frequencies [0, 1] where 1 corresponds to Nyquist frequency
         * Equivalent to: scipy.signal.butter(order, Wn, btype=btype) (without fs parameter)
         * @param sampleRate Sampling rate in Hz
         * @param normalizedFreq1 First normalized frequency [0, 1]
         * @param normalizedFreq2 Second normalized frequency [0, 1] (for bandpass only)
         * @param filterType Filter type
         * @param order Filter order
         */
        fun createFromNormalizedFrequencies(
            sampleRate: Double, 
            normalizedFreq1: Double, 
            normalizedFreq2: Double = 0.0,
            filterType: FilterType,
            order: Int = 4
        ): IIRFilter {
            val nyquist = sampleRate / 2.0
            val freq1 = normalizedFreq1 * nyquist
            val freq2 = normalizedFreq2 * nyquist
            
            return IIRFilter(sampleRate, filterType, freq1, freq2, order)
        }
    }
}