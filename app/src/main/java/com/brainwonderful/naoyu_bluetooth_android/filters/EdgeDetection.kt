package com.brainwonderful.naoyu_bluetooth_android.filters

enum class EdgeType(val value: Int) {
    RISING(NativeFilters.EDGE_TYPE_RISING),
    DESCENDING(NativeFilters.EDGE_TYPE_DESCENDING)
}

class EdgeDetection {
    private val nativeFilters = NativeFilters()
    
    fun detectEdges(signal: DoubleArray, edgeType: EdgeType): IntArray {
        return nativeFilters.detectEdges(signal, edgeType.value)
    }
}