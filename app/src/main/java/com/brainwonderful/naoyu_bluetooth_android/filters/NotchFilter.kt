package com.brainwonderful.naoyu_bluetooth_android.filters

/**
 * Notch Filter for removing specific frequency interference (e.g., 50Hz power line noise)
 * Updated to match Python's scipy.signal.iirnotch implementation using quality factor
 */
class NotchFilter : AutoCloseable {
    private val nativeFilters = NativeFilters()
    private val handle: Long
    
    // 主构造函数：使用质量因子（与Python实现兼容）
    constructor(
        sampleRate: Double,
        notchFreq: Double = 50.0,
        qualityFactor: Double = 30.0  // Changed from beta to qualityFactor to match Python
    ) {
        handle = nativeFilters.createNotchFilter(sampleRate, notchFreq, qualityFactor)
    }
    
    // 兼容构造函数：直接使用beta参数（为测试代码兼容性）
    @Deprecated("Use quality factor constructor instead", ReplaceWith("NotchFilter(sampleRate, notchFreq, qualityFactor)"))
    constructor(
        sampleRate: Double,
        notchFreq: Double,
        beta: Double,
        @Suppress("UNUSED_PARAMETER") useBeta: Boolean  // 区分构造函数的标记参数
    ) {
        handle = nativeFilters.createNotchFilterWithBeta(sampleRate, notchFreq, beta)
    }
    
    fun process(input: Double): Double {
        return nativeFilters.processNotchFilter(handle, input)
    }
    
    fun processArray(input: DoubleArray): DoubleArray {
        return DoubleArray(input.size) { i ->
            process(input[i])
        }
    }
    
    fun reset() {
        nativeFilters.resetNotchFilter(handle)
    }
    
    override fun close() {
        nativeFilters.destroyNotchFilter(handle)
    }
}