package com.brainwonderful.naoyu_bluetooth_android.filters

import kotlin.math.exp

/**
 * Real-time DC offset removal filter using recursive mean (EWMA)
 * 
 * This filter removes DC offset and very low frequency drift from EEG signals using
 * an exponentially weighted moving average. It's equivalent to a first-order IIR 
 * high-pass filter with very low cutoff frequency (~0.2-0.4Hz).
 * 
 * The filter is designed to:
 * - Remove hardware DC offset and electrode drift
 * - Preserve all physiological EEG frequencies (>0.5Hz including Delta waves)
 * - Operate in real-time with minimal computational and memory overhead
 * 
 * @param alpha Smoothing factor (0 < alpha < 1). Smaller values = lower cutoff frequency.
 *              Typical range: 0.005-0.01 for DC removal
 * @param sampleRate Sample rate in Hz, used for cutoff frequency calculation
 */
class DCRemovalFilter(
    private val alpha: Double,
    private val sampleRate: Double = 250.0
) {
    
    companion object {
        private const val TAG = "DCRemovalFilter"
        
        /**
         * Create DC removal filter with specified cutoff frequency
         * @param sampleRate Sample rate in Hz
         * @param cutoffHz Desired cutoff frequency in Hz (typically 0.2-0.5Hz)
         * @return DCRemovalFilter instance
         */
        fun createWithCutoff(sampleRate: Double, cutoffHz: Double): DCRemovalFilter {
            // Convert cutoff frequency to alpha using: alpha ≈ 1 - exp(-2π * fc * Δt)
            // where Δt = 1/sampleRate
            val deltaT = 1.0 / sampleRate
            val alpha = 1.0 - exp(-2.0 * kotlin.math.PI * cutoffHz * deltaT)
            return DCRemovalFilter(alpha, sampleRate)
        }
        
        /**
         * Create DC removal filter with default settings optimized for EEG
         * Cutoff frequency: ~0.05Hz (much lower to preserve EEG signals)
         */
        fun createDefault(sampleRate: Double = 250.0): DCRemovalFilter {
            return createWithCutoff(sampleRate, 0.05)
        }
    }
    
    // Filter state
    private var lastMean: Double = 0.0
    private var initialized: Boolean = false
    
    // Statistics for debugging
    private var sampleCount: Long = 0
    private var lastLogTime: Long = 0
    
    init {
        require(alpha > 0.0 && alpha < 1.0) { 
            "Alpha must be between 0 and 1, got: $alpha" 
        }
        require(sampleRate > 0.0) { 
            "Sample rate must be positive, got: $sampleRate" 
        }
        
        val cutoffHz = calculateCutoffFrequency()
        android.util.Log.d(TAG, "DC removal filter created: alpha=$alpha, cutoff=${String.format("%.3f", cutoffHz)}Hz @ ${sampleRate}Hz")
    }
    
    /**
     * Calculate the approximate cutoff frequency for this filter
     * @return Cutoff frequency in Hz
     */
    fun calculateCutoffFrequency(): Double {
        // Approximate formula: fc ≈ alpha * fs / (2π)
        return alpha * sampleRate / (2.0 * kotlin.math.PI)
    }
    
    /**
     * Process a block of data and remove DC offset
     * Uses recursive mean: mean_t = α * sample_t + (1-α) * mean_{t-1}
     * Output: output_t = sample_t - mean_t
     * 
     * @param data Input data array
     * @return New array with DC offset removed
     */
    fun processBlock(data: DoubleArray): DoubleArray {
        if (data.isEmpty()) return data
        
        // Initialize with the first sample to avoid startup transients
        if (!initialized) {
            lastMean = data[0]
            initialized = true
            android.util.Log.d(TAG, "Filter initialized with first sample: ${String.format("%.3f", lastMean)}")
        }
        
        val output = DoubleArray(data.size)
        var minOutput = Double.MAX_VALUE
        var maxOutput = Double.MIN_VALUE
        var meanBefore = 0.0
        var meanAfter = 0.0
        
        // Calculate mean before processing for debugging
        if (data.isNotEmpty()) {
            meanBefore = data.average()
        }
        
        // Process each sample
        for (i in data.indices) {
            // Update recursive mean: mean_t = α * sample_t + (1-α) * mean_{t-1}
            lastMean = alpha * data[i] + (1.0 - alpha) * lastMean
            
            // Remove DC: output_t = sample_t - mean_t
            output[i] = data[i] - lastMean
            
            // Track output range
            if (output[i] < minOutput) minOutput = output[i]
            if (output[i] > maxOutput) maxOutput = output[i]
            
            sampleCount++
        }
        
        // Calculate mean after processing
        if (output.isNotEmpty()) {
            meanAfter = output.average()
        }
        
        // Periodic logging for debugging (every 10 seconds)
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLogTime > 10000) {
            lastLogTime = currentTime
            android.util.Log.d(TAG, 
                "DC removal stats: samples=$sampleCount, " +
                "currentMean=${String.format("%.3f", lastMean)}, " +
                "before=${String.format("%.3f", meanBefore)}, " +
                "after=${String.format("%.3f", meanAfter)}, " +
                "range=[${String.format("%.3f", minOutput)}, ${String.format("%.3f", maxOutput)}]"
            )
        }
        
        return output
    }
    
    /**
     * Process a single sample
     * @param sample Input sample
     * @return Sample with DC offset removed
     */
    fun processSample(sample: Double): Double {
        if (!initialized) {
            lastMean = sample
            initialized = true
        }
        
        lastMean = alpha * sample + (1.0 - alpha) * lastMean
        sampleCount++
        return sample - lastMean
    }
    
    /**
     * Get current DC estimate (the running mean)
     * @return Current estimated DC offset
     */
    fun getCurrentDCEstimate(): Double = lastMean
    
    /**
     * Get processing statistics
     * @return Map containing filter statistics
     */
    fun getStatistics(): Map<String, Any> {
        return mapOf(
            "alpha" to alpha,
            "sampleRate" to sampleRate,
            "cutoffHz" to calculateCutoffFrequency(),
            "currentMean" to lastMean,
            "sampleCount" to sampleCount,
            "initialized" to initialized
        )
    }
    
    /**
     * Reset filter state
     */
    fun reset() {
        lastMean = 0.0
        initialized = false
        sampleCount = 0
        android.util.Log.d(TAG, "DC removal filter reset")
    }
    
    /**
     * Get filter information string for debugging
     */
    override fun toString(): String {
        val cutoffHz = calculateCutoffFrequency()
        return "DCRemovalFilter(alpha=$alpha, cutoff=${String.format("%.3f", cutoffHz)}Hz, " +
               "mean=${String.format("%.3f", lastMean)}, samples=$sampleCount)"
    }
}