package com.brainwonderful.naoyu_bluetooth_android.data

import com.brainwonderful.naoyu_bluetooth_android.config.EEGProcessingConfig

/**
 * EEG data packet representing 244 bytes of raw data from the device
 * Format: 4 bytes signal + 120 bytes AF7 + 120 bytes AF8
 */
data class EEGPacket(
    val bagIndex: Int,           // Packet sequence number
    val signalStatus: Int,       // 0 = good signal, 1 = poor signal
    val reserved: Int,           // Reserved byte
    val batteryLevel: Int,       // Battery level (0-100)
    val af7Data: DoubleArray,    // AF7 channel data (40 points in microvolts)
    val af8Data: DoubleArray,    // AF8 channel data (40 points in microvolts)
    val timestamp: Long = System.currentTimeMillis()
) {
    companion object {
        const val PACKET_SIZE = EEGProcessingConfig.DeviceConfig.DBAY_PACKET_SIZE
        const val SIGNAL_BYTES = EEGProcessingConfig.DeviceConfig.DBAY_SIGNAL_SIZE
        const val CHANNEL_BYTES = EEGProcessingConfig.DeviceConfig.DBAY_CHANNEL_SIZE
        const val POINTS_PER_CHANNEL = 40 // 固定40个数据点
        const val BYTES_PER_POINT = 3 // 固定3字节每数据点（24位ADC）
        
        // ADC conversion constants
        private const val ADC_REFERENCE = 4.84
        private const val ADC_BITS = 24
        private const val ADC_SCALE = 256.0
        private const val GAIN_FACTOR = 6.0
        private const val MICROVOLT_SCALE = 1_000_000.0
    }
    
    val isSignalGood: Boolean
        get() = signalStatus == 0
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EEGPacket

        if (bagIndex != other.bagIndex) return false
        if (signalStatus != other.signalStatus) return false
        if (reserved != other.reserved) return false
        if (batteryLevel != other.batteryLevel) return false
        if (!af7Data.contentEquals(other.af7Data)) return false
        if (!af8Data.contentEquals(other.af8Data)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = bagIndex
        result = 31 * result + signalStatus
        result = 31 * result + reserved
        result = 31 * result + batteryLevel
        result = 31 * result + af7Data.contentHashCode()
        result = 31 * result + af8Data.contentHashCode()
        return result
    }
}