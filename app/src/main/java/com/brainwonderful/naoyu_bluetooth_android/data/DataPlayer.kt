package com.brainwonderful.naoyu_bluetooth_android.data

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.*
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * Data player for replaying recorded EEG data
 * Simulates Bluetooth data stream from saved files
 */
class DataPlayer {
    
    companion object {
        private const val FILE_MAGIC = "NEEG"
        private const val HEADER_SIZE = 64
        private const val PACKET_SIZE = 244
        private const val PACKET_INTERVAL_MS = 160L // 40 samples at 250Hz
    }
    
    // Playback state
    private val _playbackState = MutableStateFlow(PlaybackState.STOPPED)
    val playbackState: StateFlow<PlaybackState> = _playbackState.asStateFlow()
    
    // Data output flows
    // For NEEG files: raw bytes (simulates Bluetooth data)
    private val _dataFlow = MutableSharedFlow<ByteArray>(replay = 1)
    val dataFlow: SharedFlow<ByteArray> = _dataFlow.asSharedFlow()
    
    // For TXT files: direct microvolt data
    private val _directDataFlow = MutableSharedFlow<Pair<Double, Double>>(replay = 1)
    val directDataFlow: SharedFlow<Pair<Double, Double>> = _directDataFlow.asSharedFlow()
    
    // Playback progress
    private val _progress = MutableStateFlow(PlaybackProgress())
    val progress: StateFlow<PlaybackProgress> = _progress.asStateFlow()
    
    // File info
    private val _fileInfo = MutableStateFlow<FileInfo?>(null)
    val fileInfo: StateFlow<FileInfo?> = _fileInfo.asStateFlow()
    
    private var playbackJob: Job? = null
    private var currentFile: File? = null
    private var inputStream: BufferedInputStream? = null
    
    // Playback speed control
    private var playbackSpeed = 1.0f
    
    // Manual sampling rate override for TXT files
    private var manualSampleRate: Int? = null
    
    enum class PlaybackState {
        STOPPED,
        LOADING,
        PLAYING,
        PAUSED,
        FINISHED,
        ERROR
    }
    
    data class PlaybackProgress(
        val currentPacket: Long = 0,
        val totalPackets: Long = 0,
        val currentTime: Long = 0,
        val totalTime: Long = 0,
        val percentage: Float = 0f
    )
    
    data class FileInfo(
        val fileName: String,
        val deviceName: String,
        val recordTime: Long,
        val sampleRate: Int,
        val channelCount: Int,
        val totalPackets: Long,
        val duration: Long
    )
    
    /**
     * Load a recording file (supports both .neeg and .txt formats)
     * For TXT files, sampleRateOverride can be used to manually set sampling rate
     */
    suspend fun loadFile(file: File, sampleRateOverride: Int? = null): Boolean {
        manualSampleRate = sampleRateOverride
        if (!file.exists() || !file.canRead()) {
            _playbackState.value = PlaybackState.ERROR
            return false
        }
        
        _playbackState.value = PlaybackState.LOADING
        
        return withContext(Dispatchers.IO) {
            try {
                currentFile = file
                inputStream?.close()
                
                val header = when (file.extension.lowercase()) {
                    "neeg" -> {
                        inputStream = BufferedInputStream(FileInputStream(file))
                        readNeegHeader()
                    }
                    "txt" -> {
                        readTxtFileInfo(file)
                    }
                    else -> null
                }
                
                if (header != null) {
                    _fileInfo.value = header
                    _playbackState.value = PlaybackState.STOPPED
                    _progress.value = PlaybackProgress(
                        totalPackets = header.totalPackets,
                        totalTime = header.duration
                    )
                    true
                } else {
                    _playbackState.value = PlaybackState.ERROR
                    false
                }
            } catch (e: Exception) {
                e.printStackTrace()
                _playbackState.value = PlaybackState.ERROR
                false
            }
        }
    }
    
    /**
     * Start or resume playback
     */
    fun play() {
        synchronized(this) {
            if (_playbackState.value != PlaybackState.STOPPED && 
                _playbackState.value != PlaybackState.PAUSED) {
                return
            }
            
            val file = currentFile ?: return
            val fileInfo = _fileInfo.value ?: return
            
            // Cancel any existing job before starting new one
            playbackJob?.cancel()
            
            playbackJob = CoroutineScope(Dispatchers.IO).launch {
                try {
                    _playbackState.value = PlaybackState.PLAYING
                    
                    when (file.extension.lowercase()) {
                        "neeg" -> playNeegFile(fileInfo)
                        "txt" -> playTxtFile(file, fileInfo)
                    }
                } catch (e: CancellationException) {
                    // 正常取消，不需要设置为ERROR状态
                    // 状态已经在pause()方法中设置为PAUSED
                } catch (e: Exception) {
                    e.printStackTrace()
                    _playbackState.value = PlaybackState.ERROR
                }
            }
        }
    }
    
    /**
     * Play .neeg format file
     */
    private suspend fun playNeegFile(fileInfo: FileInfo) {
        val stream = inputStream ?: return
        var packetCount = 0L
        val startTime = System.currentTimeMillis()
        
        // Skip to current position if resuming
        val currentPacket = _progress.value.currentPacket
        if (currentPacket > 0) {
            stream.skip(currentPacket * (8 + PACKET_SIZE))
        }
        
        while (currentCoroutineContext().isActive && _playbackState.value == PlaybackState.PLAYING) {
            val timestampBuffer = ByteArray(8)
            val packetBuffer = ByteArray(PACKET_SIZE)
            
            val timestampRead = stream.read(timestampBuffer)
            if (timestampRead != 8) {
                // 只有在当前状态仍然是PLAYING时才设置为FINISHED
                if (_playbackState.value == PlaybackState.PLAYING) {
                    _playbackState.value = PlaybackState.FINISHED
                }
                break
            }
            
            val packetRead = stream.read(packetBuffer)
            if (packetRead != PACKET_SIZE) {
                // 只有在当前状态仍然是PLAYING时才设置为ERROR
                if (_playbackState.value == PlaybackState.PLAYING) {
                    _playbackState.value = PlaybackState.ERROR
                }
                break
            }
            
            _dataFlow.emit(packetBuffer)
            packetCount++
            
            updateProgress(currentPacket + packetCount, fileInfo, startTime)
            // Calculate delay based on actual sample rate (40 samples per packet)
            val delayMs = (40 * 1000L / fileInfo.sampleRate / playbackSpeed).toLong()
            delay(delayMs)
        }
    }
    
    /**
     * Play .txt format file - direct microvolt data stream
     */
    private suspend fun playTxtFile(file: File, fileInfo: FileInfo) {
        var lineCount = 0L
        val startTime = System.currentTimeMillis()
        val currentPacket = _progress.value.currentPacket
        
        // Use efficient file positioning for large files
        val reader = file.bufferedReader()
        
        // Skip to current position if resuming
        if (currentPacket > 0) {
            // For large files, use more efficient skipping
            var skipped = 0L
            while (skipped < currentPacket && reader.readLine() != null) {
                skipped++
            }
            lineCount = skipped
        }
        
        while (currentCoroutineContext().isActive && _playbackState.value == PlaybackState.PLAYING) {
            val line = reader.readLine()
            if (line == null) {
                // 只有在当前状态仍然是PLAYING时才设置为FINISHED
                if (_playbackState.value == PlaybackState.PLAYING) {
                    _playbackState.value = PlaybackState.FINISHED
                }
                break
            }
            
            // Parse line directly to microvolt values
            val values = line.trim().split("\\s+".toRegex())
            if (values.size >= 2) {
                try {
                    val af7 = values[0].toDouble()
                    val af8 = values[1].toDouble()
                    
                    // Emit direct microvolt data (AF7, AF8)
                    _directDataFlow.emit(Pair(af7, af8))
                    lineCount++
                    
                    updateProgress(lineCount, fileInfo, startTime)
                    // Calculate delay based on actual sample rate
                    val delayMs = (1000L / fileInfo.sampleRate / playbackSpeed).toLong()
                    delay(delayMs)
                } catch (e: NumberFormatException) {
                    // Skip invalid lines
                    continue
                }
            }
        }
        
        reader.close()
    }
    
    /**
     * Update playback progress
     */
    private fun updateProgress(currentPacket: Long, fileInfo: FileInfo, startTime: Long) {
        val elapsedTime = System.currentTimeMillis() - startTime
        _progress.value = PlaybackProgress(
            currentPacket = currentPacket,
            totalPackets = fileInfo.totalPackets,
            currentTime = elapsedTime,
            totalTime = fileInfo.duration,
            percentage = (currentPacket.toFloat() / fileInfo.totalPackets * 100)
        )
    }
    
    /**
     * Pause playback
     */
    fun pause() {
        synchronized(this) {
            if (_playbackState.value == PlaybackState.PLAYING) {
                _playbackState.value = PlaybackState.PAUSED
                playbackJob?.cancel()
            }
        }
    }
    
    /**
     * Stop playback and reset
     */
    fun stop() {
        _playbackState.value = PlaybackState.STOPPED
        playbackJob?.cancel()
        _progress.value = PlaybackProgress(
            totalPackets = _fileInfo.value?.totalPackets ?: 0,
            totalTime = _fileInfo.value?.duration ?: 0
        )
        
        // Reset file position
        try {
            inputStream?.close()
            currentFile?.let { file ->
                inputStream = BufferedInputStream(FileInputStream(file))
                if (file.extension.lowercase() == "neeg") {
                    // Skip header for NEEG files to maintain consistency
                    inputStream?.skip(HEADER_SIZE.toLong())
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Seek to specific position (0.0 to 1.0)
     */
    suspend fun seekTo(position: Float) {
        if (position < 0f || position > 1f) return
        
        val wasPlaying = _playbackState.value == PlaybackState.PLAYING
        pause()
        
        withContext(Dispatchers.IO) {
            try {
                val fileInfo = _fileInfo.value ?: return@withContext
                val targetPacket = (fileInfo.totalPackets * position).toLong()
                
                // Reset stream and skip to target position
                inputStream?.close()
                currentFile?.let { file ->
                    inputStream = BufferedInputStream(FileInputStream(file))
                    // Skip header and packets
                    inputStream?.skip(HEADER_SIZE + targetPacket * (8 + PACKET_SIZE))
                }
                
                _progress.value = _progress.value.copy(
                    currentPacket = targetPacket,
                    currentTime = (fileInfo.duration * position).toLong(),
                    percentage = position * 100
                )
                
                if (wasPlaying) {
                    play()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                _playbackState.value = PlaybackState.ERROR
            }
        }
    }
    
    /**
     * Set playback speed (0.5x - 2.0x)
     */
    fun setPlaybackSpeed(speed: Float) {
        playbackSpeed = speed.coerceIn(0.5f, 2.0f)
    }
    
    /**
     * Read .neeg file header
     */
    private fun readNeegHeader(): FileInfo? {
        return try {
            val stream = inputStream ?: return null
            val headerBuffer = ByteArray(HEADER_SIZE)
            
            if (stream.read(headerBuffer) != HEADER_SIZE) {
                return null
            }
            
            val buffer = ByteBuffer.wrap(headerBuffer)
            buffer.order(ByteOrder.LITTLE_ENDIAN)
            
            // Validate magic number
            val magic = ByteArray(4)
            buffer.get(magic)
            if (String(magic) != FILE_MAGIC) {
                return null
            }
            
            // Read header fields
            val version = buffer.getShort()
            val sampleRate = buffer.getShort().toInt()
            val channelCount = buffer.get().toInt()
            
            // Read device name (32 bytes)
            val deviceNameBytes = ByteArray(32)
            buffer.get(deviceNameBytes)
            val deviceName = String(deviceNameBytes).trim('\u0000')
            
            // Read timestamp
            val recordTime = buffer.getLong()
            
            // Calculate file info
            val file = currentFile ?: return null
            val dataSize = file.length() - HEADER_SIZE
            val packetSize = 8 + PACKET_SIZE // timestamp + data
            val totalPackets = dataSize / packetSize
            // Calculate duration based on actual sample rate (40 samples per packet)
            val duration = (totalPackets * 40 * 1000L) / sampleRate
            
            FileInfo(
                fileName = file.name,
                deviceName = deviceName,
                recordTime = recordTime,
                sampleRate = sampleRate,
                channelCount = channelCount,
                totalPackets = totalPackets,
                duration = duration
            )
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Read .txt file information (EEGDataAnalysis format)
     * Supports manual sampling rate override
     */
    private fun readTxtFileInfo(file: File): FileInfo? {
        return try {
            val lineCount = file.bufferedReader().use { reader ->
                reader.lineSequence().count()
            }
            
            if (lineCount == 0) return null
            
            // Use manual override or detect from filename, default to 500Hz
            val sampleRate = manualSampleRate ?: when {
                file.name.contains("500", ignoreCase = true) || file.name.contains("CT10", ignoreCase = true) -> 500
                file.name.contains("250", ignoreCase = true) || file.name.contains("Dbay", ignoreCase = true) -> 250
                else -> 500 // Default to 500Hz as requested
            }
            
            // Calculate duration based on actual sample rate
            val durationMs = (lineCount * 1000L) / sampleRate
            
            FileInfo(
                fileName = file.name,
                deviceName = "TXT File (${sampleRate}Hz)",
                recordTime = file.lastModified(),
                sampleRate = sampleRate,
                channelCount = 2,
                totalPackets = lineCount.toLong(),
                duration = durationMs
            )
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Check if current file is TXT format
     */
    fun isTxtFile(): Boolean {
        return currentFile?.extension?.lowercase() == "txt"
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        stop()
        try {
            inputStream?.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        inputStream = null
        currentFile = null
        _fileInfo.value = null
    }
}