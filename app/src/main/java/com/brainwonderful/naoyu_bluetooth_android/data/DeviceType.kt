package com.brainwonderful.naoyu_bluetooth_android.data

/**
 * EEG设备类型枚举
 * 根据设备名称前缀识别设备类型，不同设备有不同的采样率
 */
enum class DeviceType(
    val namePrefix: String,
    val sampleRate: Double,
    val filterType: Int,  // 滤波器类型参数：1 for Dbay, 2 for CT10
    val displayName: String
) {
    DBAY("Dbay", 250.0, 1, "深湾设备"),
    CT10("CT10", 500.0, 2, "自研设备");
    
    companion object {
        /**
         * 根据设备名称获取设备类型
         * @param deviceName 蓝牙设备名称
         * @return 设备类型，默认返回DBAY
         */
        fun fromDeviceName(deviceName: String?): DeviceType {
            if (deviceName == null) return DBAY
            
            return when {
                deviceName.startsWith(CT10.namePrefix) -> CT10
                deviceName.startsWith(DBAY.namePrefix) -> DBAY
                else -> DBAY // 默认使用深湾设备配置
            }
        }
    }
}