package com.brainwonderful.naoyu_bluetooth_android.data

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*
import java.io.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 原始蓝牙数据包记录器
 * 负责保存接收到的原始蓝牙数据包，每个字节用十六进制空格隔开，每个包一行
 */
class RawDataRecorder(private val context: Context) {
    private var recordingJob: Job? = null
    private var dataChannel: Channel<ByteArray>? = null
    private var currentFile: File? = null
    private var writer: PrintWriter? = null
    private var startTime: Long = 0
    private var endTime: Long = 0
    private var packetCount: Long = 0
    
    // 序号统计相关（简化版）
    private var lastPacketIndex: Int = -1
    private var firstPacketIndex: Int = -1
    private var missedPacketsCount: Long = 0
    
    private val _recordingState = MutableStateFlow(false)
    val recordingState: StateFlow<Boolean> = _recordingState.asStateFlow()
    
    private val _recordingStats = MutableStateFlow(RawRecordingStats())
    val recordingStats: StateFlow<RawRecordingStats> = _recordingStats.asStateFlow()
    
    data class RawRecordingStats(
        val duration: Long = 0,
        val packetCount: Long = 0,
        val fileSize: Long = 0,
        val expectedPackets: Long = 0,
        val missedPackets: Long = 0,
        val packetLossRate: Double = 0.0,
        val actualSampleRate: Double = 0.0,
        val theoreticalSampleRate: Double = 0.0
    )
    
    data class RawRecordingMetadata(
        val fileName: String,
        val deviceName: String,
        val startTime: Long,
        val endTime: Long,
        val packetCount: Long,
        val duration: Long,
        val fileSize: Long,
        val expectedPackets: Long = 0,
        val missedPackets: Long = 0,
        val packetLossRate: Double = 0.0,
        val actualSampleRate: Double = 0.0,
        val theoreticalSampleRate: Double = 0.0
    )
    
    /**
     * 开始记录原始数据包
     */
    fun startRecording(deviceName: String) {
        if (_recordingState.value) return
        
        // 创建新的数据通道
        dataChannel = Channel<ByteArray>(Channel.UNLIMITED)
        
        recordingJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                startTime = System.currentTimeMillis()
                packetCount = 0
                
                // 重置序号统计
                lastPacketIndex = -1
                firstPacketIndex = -1
                missedPacketsCount = 0
                
                // 创建临时文件
                val tempDir = File(context.filesDir, "recordings/temp")
                tempDir.mkdirs()
                
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date(startTime))
                currentFile = File(tempDir, "raw_${timestamp}.txt")
                
                writer = PrintWriter(BufferedWriter(FileWriter(currentFile), 8192))
                
                // 写入文件头部信息
                writeHeader(deviceName)
                
                _recordingState.value = true
                
                // 处理数据通道中的数据
                val channel = dataChannel ?: return@launch
                for (data in channel) {
                    if (!_recordingState.value) break
                    
                    // 解析序号（第一个字节）
                    if (data.isNotEmpty()) {
                        val currentPacketIndex = data[0].toInt() and 0xFF
                        analyzePacketSequence(currentPacketIndex)
                    }
                    
                    // 将每个字节转换为十六进制格式，用空格分隔
                    val hexString = data.joinToString(" ") { byte ->
                        "%02X".format(byte.toInt() and 0xFF)
                    }
                    
                    // 每个包一行
                    writer?.println(hexString)
                    writer?.flush() // 立即写入磁盘
                    
                    packetCount++
                    
                    // 更新统计信息
                    updateStats()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                closeWriter()
            }
        }
    }
    
    /**
     * 停止记录
     */
    fun stopRecording() {
        endTime = System.currentTimeMillis()
        _recordingState.value = false
        dataChannel?.close()
        dataChannel = null
        recordingJob?.cancel()
        
        // 在停止记录时更新文件头部信息
        updateFileHeaderWithEndTime()
    }
    
    /**
     * 添加原始数据包
     */
    suspend fun addData(data: ByteArray) {
        if (_recordingState.value) {
            dataChannel?.send(data.copyOf())
        }
    }
    
    /**
     * 保存记录文件
     */
    fun saveRecording(fileName: String): RawRecordingMetadata? {
        val tempFile = currentFile ?: return null
        if (!tempFile.exists()) return null
        
        return try {
            // 创建永久目录
            val recordingsDir = File(context.filesDir, "recordings")
            recordingsDir.mkdirs()
            
            // 移动文件到永久位置
            val permanentFile = File(recordingsDir, "${fileName}_raw.txt")
            tempFile.renameTo(permanentFile)
            currentFile = null
            
            // 创建元数据
            val actualEndTime = if (endTime > 0) endTime else System.currentTimeMillis()
            val expectedPackets = calculateExpectedPackets()
            
            // 正确的丢包率计算
            val packetLossRate = if (expectedPackets > 0) {
                (missedPacketsCount.toDouble() / expectedPackets.toDouble()) * 100.0
            } else 0.0
            
            val duration = actualEndTime - startTime
            val durationSeconds = duration / 1000.0
            val samplesPerPacket = 80
            val actualSamples = packetCount * samplesPerPacket
            val theoreticalSamples = expectedPackets * samplesPerPacket
            val actualSampleRate = if (durationSeconds > 0) actualSamples / durationSeconds else 0.0
            val theoreticalSampleRate = if (durationSeconds > 0) theoreticalSamples / durationSeconds else 0.0
            
            RawRecordingMetadata(
                fileName = "${fileName}_raw",
                deviceName = "", // 从文件头读取
                startTime = startTime,
                endTime = actualEndTime,
                packetCount = packetCount,
                duration = duration,
                fileSize = permanentFile.length(),
                expectedPackets = expectedPackets,
                missedPackets = missedPacketsCount,
                packetLossRate = packetLossRate,
                actualSampleRate = actualSampleRate,
                theoreticalSampleRate = theoreticalSampleRate
            ).also {
                // 保存元数据
                saveMetadata(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 丢弃当前记录
     */
    fun discardRecording() {
        currentFile?.delete()
        currentFile = null
    }
    
    /**
     * 写入文件头部信息
     */
    private fun writeHeader(deviceName: String) {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
        val startTimeStr = dateFormat.format(Date(startTime))
        
        writer?.apply {
            println("# 原始蓝牙数据包记录文件")
            println("# 设备名称: $deviceName")
            println("# 开始时间: $startTimeStr")
            println("# 结束时间: [记录中...]")
            println("# 记录时长: [记录中...]")
            println("# 接收数据包数: [记录中...]")
            println("# 期望数据包数: [记录中...]")
            println("# 丢包数量: [记录中...]")
            println("# 丢包率: [记录中...]")
            println("# 理论采样率: [记录中...]")
            println("# 实际采样率: [记录中...]")
            println("# 数据格式: 每个字节用十六进制表示，用空格分隔，每个数据包一行")
            println("# 数据结构: 244字节/包 = 4字节信号数据(序号+状态+保留+电量) + 120字节AF7 + 120字节AF8")
            println("# 序号说明: 第一个字节为包序号(0x00-0xFF循环)，用于丢包检测")
            println("# 通道信息: AF7通道(40点) + AF8通道(40点) = 80个数据点/包")
            println("# ========================================")
            println()
            flush()
        }
    }
    
    /**
     * 分析数据包序号（简化版：只检测丢包）
     */
    private fun analyzePacketSequence(currentIndex: Int) {
        if (firstPacketIndex == -1) {
            // 第一个包
            firstPacketIndex = currentIndex
            lastPacketIndex = currentIndex
            return
        }
        
        // 检查是否有丢包
        val expectedNext = (lastPacketIndex + 1) and 0xFF
        if (currentIndex != expectedNext) {
            // 计算丢包数量（考虑0x00-0xFF循环）
            val missed = calculateMissedPackets(lastPacketIndex, currentIndex)
            if (missed > 0) {
                missedPacketsCount += missed
            }
        }
        
        lastPacketIndex = currentIndex
    }
    
    /**
     * 计算丢包数量（考虑0x00-0xFF循环）
     */
    private fun calculateMissedPackets(lastIndex: Int, currentIndex: Int): Int {
        return if (currentIndex > lastIndex) {
            // 正常情况：如 0x10 -> 0x13，丢了 0x11, 0x12
            currentIndex - lastIndex - 1
        } else if (currentIndex < lastIndex) {
            // 跨越0xFF的情况：如 0xFE -> 0x02，丢了 0xFF, 0x00, 0x01
            (0xFF - lastIndex) + currentIndex
        } else {
            // 相同的情况（不应该发生，因为重复包已经被过滤）
            0
        }
    }
    
    /**
     * 计算期望的数据包数量（正确逻辑：实际包数 + 丢包数）
     */
    private fun calculateExpectedPackets(): Long {
        // 期望包数 = 实际接收到的包数 + 传输过程中丢失的包数
        return packetCount + missedPacketsCount
    }
    
    /**
     * 更新统计信息
     */
    private fun updateStats() {
        val duration = System.currentTimeMillis() - startTime
        val fileSize = currentFile?.length() ?: 0
        val expectedPackets = calculateExpectedPackets()
        
        // 正确的丢包率计算：丢包率 = 丢包数 / 期望包数
        val packetLossRate = if (expectedPackets > 0) {
            (missedPacketsCount.toDouble() / expectedPackets.toDouble()) * 100.0
        } else 0.0
        
        // 计算采样率
        val durationSeconds = duration / 1000.0
        val samplesPerPacket = 80 // 2个通道，每通道40个数据点
        val actualSamples = packetCount * samplesPerPacket
        val theoreticalSamples = expectedPackets * samplesPerPacket
        val actualSampleRate = if (durationSeconds > 0) actualSamples / durationSeconds else 0.0
        val theoreticalSampleRate = if (durationSeconds > 0) theoreticalSamples / durationSeconds else 0.0
        
        _recordingStats.value = RawRecordingStats(
            duration = duration,
            packetCount = packetCount,
            fileSize = fileSize,
            expectedPackets = expectedPackets,
            missedPackets = missedPacketsCount,
            packetLossRate = packetLossRate,
            actualSampleRate = actualSampleRate,
            theoreticalSampleRate = theoreticalSampleRate
        )
    }
    
    /**
     * 更新文件头部信息（在停止记录时调用）
     */
    private fun updateFileHeaderWithEndTime() {
        val tempFile = currentFile ?: return
        if (!tempFile.exists()) return
        
        try {
            // 读取原文件内容
            val lines = tempFile.readLines().toMutableList()
            
            // 计算时间和采样率信息
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
            val endTimeStr = dateFormat.format(Date(endTime))
            val duration = endTime - startTime
            val durationSeconds = duration / 1000.0
            
            // 计算丢包统计
            val expectedPackets = calculateExpectedPackets()
            val packetLossRate = if (expectedPackets > 0) {
                (missedPacketsCount.toDouble() / expectedPackets.toDouble()) * 100.0
            } else 0.0
            
            // 计算采样率：每包80个数据点（AF7通道40个 + AF8通道40个）
            val samplesPerPacket = 80  // 2个通道，每通道40个数据点
            val actualSamples = packetCount * samplesPerPacket
            val theoreticalSamples = expectedPackets * samplesPerPacket
            val actualSamplingRate = if (durationSeconds > 0) actualSamples / durationSeconds else 0.0
            val theoreticalSamplingRate = if (durationSeconds > 0) theoreticalSamples / durationSeconds else 0.0
            val actualPerChannelRate = actualSamplingRate / 2
            val theoreticalPerChannelRate = theoreticalSamplingRate / 2
            
            // 更新头部信息
            for (i in lines.indices) {
                when {
                    lines[i].startsWith("# 结束时间:") -> {
                        lines[i] = "# 结束时间: $endTimeStr"
                    }
                    lines[i].startsWith("# 记录时长:") -> {
                        lines[i] = "# 记录时长: ${String.format("%.3f", durationSeconds)}秒 (${duration}ms)"
                    }
                    lines[i].startsWith("# 接收数据包数:") -> {
                        lines[i] = "# 接收数据包数: $packetCount"
                    }
                    lines[i].startsWith("# 期望数据包数:") -> {
                        lines[i] = "# 期望数据包数: $expectedPackets (序号范围: 0x${String.format("%02X", firstPacketIndex)}-0x${String.format("%02X", lastPacketIndex)})"
                    }
                    lines[i].startsWith("# 丢包数量:") -> {
                        lines[i] = "# 丢包数量: $missedPacketsCount"
                    }
                    lines[i].startsWith("# 丢包率:") -> {
                        lines[i] = "# 丢包率: ${String.format("%.2f", packetLossRate)}%"
                    }
                    lines[i].startsWith("# 理论采样率:") -> {
                        lines[i] = "# 理论采样率: 每通道${String.format("%.2f", theoreticalPerChannelRate)}Hz，总计${String.format("%.2f", theoreticalSamplingRate)}Hz (${theoreticalSamples}个样本点)"
                    }
                    lines[i].startsWith("# 实际采样率:") -> {
                        lines[i] = "# 实际采样率: 每通道${String.format("%.2f", actualPerChannelRate)}Hz，总计${String.format("%.2f", actualSamplingRate)}Hz (${actualSamples}个样本点)"
                    }
                }
            }
            
            // 重新写入文件
            tempFile.writeText(lines.joinToString("\n"))
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 关闭写入器
     */
    private fun closeWriter() {
        try {
            writer?.flush()
            writer?.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        writer = null
    }
    
    /**
     * 保存元数据
     */
    private fun saveMetadata(metadata: RawRecordingMetadata) {
        try {
            val metadataDir = File(context.filesDir, "recordings/metadata")
            metadataDir.mkdirs()
            
            val metadataFile = File(metadataDir, "${metadata.fileName}.json")
            val json = """
                {
                    "fileName": "${metadata.fileName}",
                    "deviceName": "${metadata.deviceName}",
                    "startTime": ${metadata.startTime},
                    "endTime": ${metadata.endTime},
                    "packetCount": ${metadata.packetCount},
                    "duration": ${metadata.duration},
                    "fileSize": ${metadata.fileSize},
                    "type": "raw"
                }
            """.trimIndent()
            
            metadataFile.writeText(json)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 列出原始数据记录
     */
    fun listRawRecordings(): List<RawRecordingMetadata> {
        val recordings = mutableListOf<RawRecordingMetadata>()
        val recordingsDir = File(context.filesDir, "recordings")
        
        if (recordingsDir.exists()) {
            recordingsDir.listFiles { file -> 
                file.name.endsWith("_raw.txt") 
            }?.forEach { file ->
                try {
                    recordings.add(
                        RawRecordingMetadata(
                            fileName = file.nameWithoutExtension,
                            deviceName = "Unknown",
                            startTime = file.lastModified(),
                            endTime = file.lastModified(),
                            packetCount = 0, // 可以从文件计算
                            duration = 0, // 可以从文件计算
                            fileSize = file.length()
                        )
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        
        return recordings
    }
}
