package com.brainwonderful.naoyu_bluetooth_android.data

/**
 * 频谱分析数据
 * 包含FFT分析后的频率和幅值信息
 * 
 * 注意：现在的幅值计算已与Python eeg_spectrum.py保持一致：
 * - 自动去除DC偏移（均值移除）
 * - 应用Hanning窗函数并进行补偿
 * - 正确的归一化处理（2.0 * abs(fft) / N * window_correction）
 * - 幅值具有物理意义，单位与输入信号一致（如μV）
 * - 结果可与Python版本直接对比
 */
data class SpectrumData(
    val frequencies: DoubleArray,    // 频率数组 (Hz)
    val magnitudes: DoubleArray,     // 幅值数组 (μV) - 现在具有物理意义
    val powers: DoubleArray,         // 功率数组 (μV²)
    val dominantFrequency: Double,   // 主频率 (Hz)
    val totalPower: Double,          // 总功率 (μV²)
    val timestamp: Long,             // 时间戳
    val fftSize: Int,               // FFT点数
    val sampleRate: Double          // 采样率
) {
    init {
        require(frequencies.size == magnitudes.size && magnitudes.size == powers.size) {
            "频率、幅值和功率数组长度必须相同"
        }
    }
    
    /**
     * 获取指定频率范围内的数据
     */
    fun getDataInRange(minFreq: Double, maxFreq: Double): Triple<DoubleArray, DoubleArray, DoubleArray> {
        val indices = frequencies.indices.filter { 
            frequencies[it] >= minFreq && frequencies[it] <= maxFreq 
        }
        
        return Triple(
            indices.map { frequencies[it] }.toDoubleArray(),
            indices.map { magnitudes[it] }.toDoubleArray(),
            indices.map { powers[it] }.toDoubleArray()
        )
    }
    
    /**
     * 获取频带功率
     * Delta: 1-4 Hz
     * Theta: 4-8 Hz
     * Alpha: 8-13 Hz
     * Beta: 13-30 Hz
     */
    fun getBandPowers(): Map<String, Double> {
        return mapOf(
            "Delta" to calculateBandPower(1.0, 4.0),
            "Theta" to calculateBandPower(4.0, 8.0),
            "Alpha" to calculateBandPower(8.0, 13.0),
            "Beta" to calculateBandPower(13.0, 30.0)
        )
    }
    
    /**
     * 获取频带幅值（使用幅值平方和，与Python版本一致）
     * Delta: 1-4 Hz
     * Theta: 4-8 Hz
     * Alpha: 8-13 Hz
     * Beta: 13-30 Hz
     */
    fun getBandMagnitudes(): Map<String, Double> {
        return mapOf(
            "Delta" to calculateBandMagnitude(1.0, 4.0),
            "Theta" to calculateBandMagnitude(4.0, 8.0),
            "Alpha" to calculateBandMagnitude(8.0, 13.0),
            "Beta" to calculateBandMagnitude(13.0, 30.0)
        )
    }
    
    private fun calculateBandPower(minFreq: Double, maxFreq: Double): Double {
        return frequencies.indices
            .filter { frequencies[it] >= minFreq && frequencies[it] <= maxFreq }
            .sumOf { powers[it] }
    }
    
    private fun calculateBandMagnitude(minFreq: Double, maxFreq: Double): Double {
        return frequencies.indices
            .filter { frequencies[it] >= minFreq && frequencies[it] <= maxFreq }
            .sumOf { magnitudes[it] * magnitudes[it] }
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as SpectrumData
        
        if (!frequencies.contentEquals(other.frequencies)) return false
        if (!magnitudes.contentEquals(other.magnitudes)) return false
        if (!powers.contentEquals(other.powers)) return false
        if (dominantFrequency != other.dominantFrequency) return false
        if (totalPower != other.totalPower) return false
        if (timestamp != other.timestamp) return false
        if (fftSize != other.fftSize) return false
        if (sampleRate != other.sampleRate) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = frequencies.contentHashCode()
        result = 31 * result + magnitudes.contentHashCode()
        result = 31 * result + powers.contentHashCode()
        result = 31 * result + dominantFrequency.hashCode()
        result = 31 * result + totalPower.hashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + fftSize
        result = 31 * result + sampleRate.hashCode()
        return result
    }
}