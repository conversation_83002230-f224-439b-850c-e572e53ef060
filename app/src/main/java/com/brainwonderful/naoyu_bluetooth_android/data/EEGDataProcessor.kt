package com.brainwonderful.naoyu_bluetooth_android.data

import com.brainwonderful.naoyu_bluetooth_android.filters.*
import com.brainwonderful.naoyu_bluetooth_android.config.EEGProcessingConfig
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write
import java.util.Locale

/**
 * Real-time EEG data processor with configurable filters
 * Processes incoming EEG packets and applies filters based on settings
 */
class EEGDataProcessor(
    private var sampleRate: Double = 250.0
) {
    
    // Thread-safe filter access
    private val filterLock = ReentrantReadWriteLock()
    
    // Filter instances
    private var notchFilterAF7: NotchFilter? = null
    private var notchFilterAF8: NotchFilter? = null
    private var firFilterAF7: FIRFilter? = null
    private var firFilterAF8: FIRFilter? = null
    private var fftProcessor: FFT? = null
    
    // DC偏移去除滤波器 - 强制预处理，始终启用
    private var dcRemovalFilterAF7: DCRemovalFilter? = null
    private var dcRemovalFilterAF8: DCRemovalFilter? = null
    
    // 时间常数滤波器 - 按照UniApp版本实现
    private var timeConstantFilterAF7: FIRFilter? = null
    private var timeConstantFilterAF8: FIRFilter? = null
    
    // Processing settings
    data class FilterSettings(
        val notchEnabled: Boolean = true,
        val notchFreq: Double = 50.0,
        val firEnabled: Boolean = true,
        val firType: FilterType = FilterType.BANDPASS,
        val firFreq1: Double = 1.0,
        val firFreq2: Double = 35.0,
        val fftEnabled: Boolean = false,
        val fftSize: Int = 512,
        // 时间常数设置 - 使用配置的默认值
        val timeConstantEnabled: Boolean = false,
        val timeConstantValue: Double = EEGProcessingConfig.FilterConfig.TIME_CONSTANT_DEFAULT_VALUE // 使用配置的默认时间常数
    ) {
        /**
         * 计算时间常数对应的截止频率 - 按照UniApp版本的公式
         * 公式: f = 1 / (2 * π * τ)
         */
        fun getTimeConstantCutoffFreq(): Double {
            return if (timeConstantEnabled) {
                1.0 / (2.0 * kotlin.math.PI * timeConstantValue)
            } else {
                0.0
            }
        }
    }
    
    private var currentSettings = FilterSettings()
    
    // Data buffers for continuous processing
    private val af7Buffer = ConcurrentLinkedQueue<Double>()
    private val af8Buffer = ConcurrentLinkedQueue<Double>()
    // 缓冲区大小根据采样率动态计算，使用配置的缓冲区时间窗口
    private val maxBufferSize = (sampleRate * EEGProcessingConfig.BufferConfig.PROCESSING_BUFFER_TIME_SECONDS).toInt()
    
    // Processed data flows
    private val _processedData = MutableSharedFlow<ProcessedEEGData>(replay = 1)
    val processedData: SharedFlow<ProcessedEEGData> = _processedData.asSharedFlow()
    
    // Asynchronous FFT processing flow
    private val _spectrumData = MutableSharedFlow<Pair<SpectrumData?, SpectrumData?>>(replay = 1)
    val spectrumData: SharedFlow<Pair<SpectrumData?, SpectrumData?>> = _spectrumData.asSharedFlow()
    
    private var fftJob: Job? = null
    
    // FFT throttling - only compute FFT every N packets when enabled
    private var packetCounter = 0
    private val fftUpdateInterval = EEGProcessingConfig.SpectrumConfig.FFT_UPDATE_INTERVAL // 使用配置的FFT更新间隔
    private var lastSpectrumAF7: SpectrumData? = null
    private var lastSpectrumAF8: SpectrumData? = null
    
    /**
     * Update filter settings and reinitialize filters only when necessary
     */
    fun updateSettings(settings: FilterSettings) {
        // Validate settings before applying
        val validatedSettings = validateSettings(settings)
        
        filterLock.write {
            val oldSettings = currentSettings
            currentSettings = validatedSettings
            
            // Only reinitialize filters if settings actually changed
            val needsFilterReinit = oldSettings.notchEnabled != validatedSettings.notchEnabled ||
                    oldSettings.notchFreq != validatedSettings.notchFreq ||
                    oldSettings.firEnabled != validatedSettings.firEnabled ||
                    oldSettings.firType != validatedSettings.firType ||
                    oldSettings.firFreq1 != validatedSettings.firFreq1 ||
                    oldSettings.firFreq2 != validatedSettings.firFreq2 ||
                    oldSettings.timeConstantEnabled != validatedSettings.timeConstantEnabled ||
                    oldSettings.timeConstantValue != validatedSettings.timeConstantValue
            
            // Only reinitialize FFT processor if FFT settings changed
            val needsFFTReinit = oldSettings.fftEnabled != validatedSettings.fftEnabled ||
                    oldSettings.fftSize != validatedSettings.fftSize
            
            if (needsFilterReinit) {
                // Reset FFT throttling when filter settings change
                packetCounter = 0
                lastSpectrumAF7 = null
                lastSpectrumAF8 = null
                initializeFilters()
            } else if (needsFFTReinit) {
                // Only reinitialize FFT processor
                initializeFFTProcessor()
            }
        }
    }
    
    /**
     * Update sampling rate and reinitialize filters
     */
    fun updateSampleRate(newSampleRate: Double) {
        if (newSampleRate > 0 && newSampleRate != sampleRate) {
            filterLock.write {
                sampleRate = newSampleRate
                // Clear and resize buffers
                af7Buffer.clear()
                af8Buffer.clear()
                // Reinitialize filters with new sample rate
                initializeFilters()
            }
        }
    }
    
    /**
     * Validate filter settings to prevent crashes
     */
    private fun validateSettings(settings: FilterSettings): FilterSettings {
        var validatedSettings = settings.copy()
        
        // Validate notch frequency
        if (validatedSettings.notchFreq <= 0 || validatedSettings.notchFreq >= sampleRate / 2) {
            validatedSettings = validatedSettings.copy(notchFreq = 50.0)
        }
        
        // Validate FIR filter frequencies
        when (validatedSettings.firType) {
            FilterType.LOWPASS -> {
                if (validatedSettings.firFreq1 <= 0 || validatedSettings.firFreq1 >= sampleRate / 2) {
                    validatedSettings = validatedSettings.copy(firFreq1 = 35.0)
                }
            }
            FilterType.HIGHPASS -> {
                if (validatedSettings.firFreq1 <= 0 || validatedSettings.firFreq1 >= sampleRate / 2) {
                    validatedSettings = validatedSettings.copy(firFreq1 = 1.0)
                }
            }
            FilterType.BANDPASS -> {
                var freq1 = validatedSettings.firFreq1
                var freq2 = validatedSettings.firFreq2
                
                // Ensure frequencies are in valid range
                if (freq1 <= 0) freq1 = 1.0
                if (freq2 >= sampleRate / 2) freq2 = sampleRate / 2 - 1.0
                if (freq1 >= freq2) {
                    freq1 = 1.0
                    freq2 = 35.0
                }
                
                validatedSettings = validatedSettings.copy(firFreq1 = freq1, firFreq2 = freq2)
            }
        }
        
        // Validate FFT size
        if (validatedSettings.fftSize <= 0 || validatedSettings.fftSize > 2048) {
            validatedSettings = validatedSettings.copy(fftSize = 512)
        }
        
        return validatedSettings
    }
    
    /**
     * Process incoming EEG packet
     */
    suspend fun processPacket(packet: EEGPacket) {
        withContext(Dispatchers.Default) {
            try {
                val processedAF7 = processChannel(packet.af7Data, Channel.AF7)
                val processedAF8 = processChannel(packet.af8Data, Channel.AF8)
                
                // Update buffers for continuous processing
                updateBuffers(processedAF7, processedAF8)
                
                // 使用缓存的频谱数据，避免阻塞实时数据处理
                val af7Spectrum: SpectrumData? = lastSpectrumAF7
                val af8Spectrum: SpectrumData? = lastSpectrumAF8
                
                // 触发异步FFT计算（如果启用）
                if (currentSettings.fftEnabled) {
                    packetCounter++
                    if (packetCounter >= fftUpdateInterval) {
                        packetCounter = 0
                        // 异步计算FFT，不阻塞当前数据流
                        triggerAsyncFFT()
                    }
                }
                
                // Create processed data with cached spectrum data
                val processedData = ProcessedEEGData(
                    originalPacket = packet,
                    processedAF7 = processedAF7,
                    processedAF8 = processedAF8,
                    af7Buffer = af7Buffer.toList(),
                    af8Buffer = af8Buffer.toList(),
                    fftResults = null, // FFT结果通过异步流提供
                    af7Spectrum = af7Spectrum,
                    af8Spectrum = af8Spectrum
                )
                
                _processedData.emit(processedData)
            } catch (e: Exception) {
                // Log error but don't crash
                android.util.Log.e("EEGDataProcessor", "Error processing packet", e)
                
                // Emit original data without processing if filters fail
                try {
                    val fallbackData = ProcessedEEGData(
                        originalPacket = packet,
                        processedAF7 = packet.af7Data,
                        processedAF8 = packet.af8Data,
                        af7Buffer = af7Buffer.toList(),
                        af8Buffer = af8Buffer.toList(),
                        fftResults = null,
                        af7Spectrum = null,
                        af8Spectrum = null
                    )
                    _processedData.emit(fallbackData)
                } catch (e2: Exception) {
                    android.util.Log.e("EEGDataProcessor", "Error emitting fallback data", e2)
                }
            }
        }
    }
    
    /**
     * Process single channel data with thread safety
     * Enhanced filter order for better signal quality:
     * 0. DC removal filter (recursive mean, ~0.3Hz) - FORCED PREPROCESSING
     * 1. Time constant filter (high-pass) - OPTIONAL
     * 2. Main filter (bandpass/high-pass/low-pass) - OPTIONAL  
     * 3. Notch filter (50Hz interference removal) - OPTIONAL
     */
    private fun processChannel(data: DoubleArray, channel: Channel): DoubleArray {
        if (data.isEmpty()) return data
        
        return filterLock.read {
            try {
                var processed = data.copyOf()
                var filtersApplied = mutableListOf<String>()
                
                // 记录原始数据范围用于调试
                val originalRange = if (data.isNotEmpty()) {
                    val min = data.minOrNull() ?: 0.0
                    val max = data.maxOrNull() ?: 0.0
                    "原始: ${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                } else "原始: 空数据"
                
                // 0. Apply DC removal filter - FORCED PREPROCESSING (always enabled)
                val dcRemovalFilter = when (channel) {
                    Channel.AF7 -> dcRemovalFilterAF7
                    Channel.AF8 -> dcRemovalFilterAF8
                }
                
                dcRemovalFilter?.let { filter ->
                    try {
                        val beforeRange = processed.let { arr -> 
                            val min = arr.minOrNull() ?: 0.0
                            val max = arr.maxOrNull() ?: 0.0
                            "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                        }
                        processed = filter.processBlock(processed)
                        val afterRange = processed.let { arr -> 
                            val min = arr.minOrNull() ?: 0.0
                            val max = arr.maxOrNull() ?: 0.0
                            "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                        }
                        filtersApplied.add("DC去除(${String.format(Locale.US, "%.3f", filter.calculateCutoffFrequency())}Hz): $beforeRange -> $afterRange")
                    } catch (e: Exception) {
                        android.util.Log.w("EEGDataProcessor", "DC removal filter error, skipping", e)
                    }
                }
                
                // 1. Apply time constant filter if enabled - OPTIONAL
                if (currentSettings.timeConstantEnabled) {
                    val timeConstantFilter = when (channel) {
                        Channel.AF7 -> timeConstantFilterAF7
                        Channel.AF8 -> timeConstantFilterAF8
                    }
                    
                    timeConstantFilter?.let { filter ->
                        try {
                            val beforeRange = processed.let { arr -> 
                                val min = arr.minOrNull() ?: 0.0
                                val max = arr.maxOrNull() ?: 0.0
                                "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                            }
                            processed = filter.processBlock(processed)
                            val afterRange = processed.let { arr -> 
                                val min = arr.minOrNull() ?: 0.0
                                val max = arr.maxOrNull() ?: 0.0
                                "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                            }
                            filtersApplied.add("时间常数(${currentSettings.timeConstantValue}s): $beforeRange -> $afterRange")
                        } catch (e: Exception) {
                            android.util.Log.w("EEGDataProcessor", "Time constant filter error, skipping", e)
                        }
                    }
                }
                
                // 2. Apply main filter (bandpass/high-pass/low-pass) if enabled - SECOND (matches Python order)
                if (currentSettings.firEnabled) {
                    val firFilter = when (channel) {
                        Channel.AF7 -> firFilterAF7
                        Channel.AF8 -> firFilterAF8
                    }
                    
                    firFilter?.let { filter ->
                        try {
                            val beforeRange = processed.let { arr -> 
                                val min = arr.minOrNull() ?: 0.0
                                val max = arr.maxOrNull() ?: 0.0
                                "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                            }
                            processed = filter.processBlock(processed)
                            val afterRange = processed.let { arr -> 
                                val min = arr.minOrNull() ?: 0.0
                                val max = arr.maxOrNull() ?: 0.0
                                "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                            }
                            val filterDesc = when (currentSettings.firType) {
                                FilterType.BANDPASS -> "带通(${currentSettings.firFreq1}-${currentSettings.firFreq2}Hz)"
                                FilterType.LOWPASS -> "低通(${currentSettings.firFreq1}Hz)"
                                FilterType.HIGHPASS -> "高通(${currentSettings.firFreq1}Hz)"
                            }
                            filtersApplied.add("$filterDesc: $beforeRange -> $afterRange")
                        } catch (e: Exception) {
                            android.util.Log.w("EEGDataProcessor", "FIR filter error, skipping", e)
                        }
                    }
                }
                
                // 3. Apply notch filter if enabled - LAST (matches Python order)
                if (currentSettings.notchEnabled) {
                    val notchFilter = when (channel) {
                        Channel.AF7 -> notchFilterAF7
                        Channel.AF8 -> notchFilterAF8
                    }
                    
                    notchFilter?.let { filter ->
                        try {
                            val beforeRange = processed.let { arr -> 
                                val min = arr.minOrNull() ?: 0.0
                                val max = arr.maxOrNull() ?: 0.0
                                "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                            }
                            processed = filter.processArray(processed)
                            val afterRange = processed.let { arr -> 
                                val min = arr.minOrNull() ?: 0.0
                                val max = arr.maxOrNull() ?: 0.0
                                "${String.format(Locale.US, "%.2f", min)}~${String.format(Locale.US, "%.2f", max)}"
                            }
                            filtersApplied.add("陷波(${currentSettings.notchFreq}Hz): $beforeRange -> $afterRange")
                        } catch (e: Exception) {
                            android.util.Log.w("EEGDataProcessor", "Notch filter error, skipping", e)
                        }
                    }
                }
                
                // 每10个数据包输出一次调试信息，避免日志过多
//                if ((System.currentTimeMillis() / 1000) % 10 == 0L && filtersApplied.isNotEmpty()) {
//                    android.util.Log.d("EEGDataProcessor", "$channel 滤波链: $originalRange | ${filtersApplied.joinToString(" | ")}")
//                }
                
                processed
            } catch (e: Exception) {
                android.util.Log.w("EEGDataProcessor", "Channel processing error, returning original data", e)
                data.copyOf()
            }
        }
    }
    
    /**
     * Update circular buffers with new data
     */
    private fun updateBuffers(af7Data: DoubleArray, af8Data: DoubleArray) {
        // Add new data
        af7Data.forEach { af7Buffer.offer(it) }
        af8Data.forEach { af8Buffer.offer(it) }
        
        // Trim buffers to max size
        while (af7Buffer.size > maxBufferSize) af7Buffer.poll()
        while (af8Buffer.size > maxBufferSize) af8Buffer.poll()
    }
    
    /**
     * Compute FFT for current buffer data
     */
    private fun computeFFT(): FFTResults? {
        if (!currentSettings.fftEnabled || fftProcessor == null) return null
        
        val af7List = af7Buffer.toList()
        val af8List = af8Buffer.toList()
        
        if (af7List.size < currentSettings.fftSize || af8List.size < currentSettings.fftSize) {
            return null
        }
        
        // Take last N samples for FFT
        val af7Segment = af7List.takeLast(currentSettings.fftSize).toDoubleArray()
        val af8Segment = af8List.takeLast(currentSettings.fftSize).toDoubleArray()
        
        // FFT implementation now matches Python eeg_spectrum.py:
        // - Automatically removes DC offset (mean removal)
        // - Applies Hanning window with proper compensation
        // - Returns normalized amplitude values with physical meaning
        // - Results are directly comparable to Python version
        
        return try {
            FFTResults(
                af7Result = fftProcessor!!.compute(af7Segment, sampleRate), // Uses Hanning window by default
                af8Result = fftProcessor!!.compute(af8Segment, sampleRate)  // Proper normalization applied
            )
        } catch (e: Exception) {
            android.util.Log.e("EEGDataProcessor", "FFT computation error", e)
            null
        }
    }
    
    /**
     * Convert FFT results to spectrum data
     */
    private fun computeSpectrum(fftResults: FFTResults): Pair<SpectrumData?, SpectrumData?> {
        return try {
            val af7Spectrum = createSpectrumData(fftResults.af7Result)
            val af8Spectrum = createSpectrumData(fftResults.af8Result)
            Pair(af7Spectrum, af8Spectrum)
        } catch (e: Exception) {
            android.util.Log.e("EEGDataProcessor", "Error computing spectrum", e)
            Pair(null, null)
        }
    }
    
    /**
     * Create SpectrumData from FFTResult
     */
    private fun createSpectrumData(fftResult: FFTResult): SpectrumData {
        val frequencies = fftResult.frequencies
        val magnitudes = fftResult.magnitudes
        
        // 计算功率 (幅值的平方)
        val powers = magnitudes.map { it * it }.toDoubleArray()
        
        // 找到主频率（最大功率对应的频率）
        val maxPowerIndex = powers.indices.maxByOrNull { powers[it] } ?: 0
        val dominantFrequency = frequencies[maxPowerIndex]
        
        // 计算总功率
        val totalPower = powers.sum()
        
        return SpectrumData(
            frequencies = frequencies,
            magnitudes = magnitudes,
            powers = powers,
            dominantFrequency = dominantFrequency,
            totalPower = totalPower,
            timestamp = System.currentTimeMillis(),
            fftSize = currentSettings.fftSize,
            sampleRate = sampleRate
        )
    }
    
    /**
     * Initialize only FFT processor without affecting other filters
     */
    private fun initializeFFTProcessor() {
        try {
            // Clean up existing FFT processor
            try { fftProcessor?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing FFT processor", e) 
            }
            fftProcessor = null
            
            // Create new FFT processor if enabled
            if (currentSettings.fftEnabled) {
                try {
                    fftProcessor = FFT(currentSettings.fftSize)
                    android.util.Log.d("EEGDataProcessor", "FFT processor initialized: size=${currentSettings.fftSize}, Python-compatible mode enabled")
                } catch (e: Exception) {
                    android.util.Log.e("EEGDataProcessor", "Error creating FFT processor", e)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("EEGDataProcessor", "Error in initializeFFTProcessor", e)
        }
    }
    
    /**
     * Initialize filters based on current settings with error handling
     */
    private fun initializeFilters() {
        try {
            // Clean up existing filters safely
            try { notchFilterAF7?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing notch filter AF7", e) 
            }
            try { notchFilterAF8?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing notch filter AF8", e) 
            }
            try { firFilterAF7?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing FIR filter AF7", e) 
            }
            try { firFilterAF8?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing FIR filter AF8", e) 
            }
            try { fftProcessor?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing FFT processor", e) 
            }
            try { timeConstantFilterAF7?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing time constant filter AF7", e) 
            }
            try { timeConstantFilterAF8?.close() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error closing time constant filter AF8", e) 
            }
            try { dcRemovalFilterAF7?.reset() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error resetting DC removal filter AF7", e) 
            }
            try { dcRemovalFilterAF8?.reset() } catch (e: Exception) { 
                android.util.Log.w("EEGDataProcessor", "Error resetting DC removal filter AF8", e) 
            }
            
            // Reset filter references
            notchFilterAF7 = null
            notchFilterAF8 = null
            firFilterAF7 = null
            firFilterAF8 = null
            fftProcessor = null
            timeConstantFilterAF7 = null
            timeConstantFilterAF8 = null
            dcRemovalFilterAF7 = null
            dcRemovalFilterAF8 = null
            
            // Create new filters if enabled
            if (currentSettings.notchEnabled) {
                try {
                    // 使用配置的Beta参数，确保与UniApp版本兼容
                    notchFilterAF7 = NotchFilter(sampleRate, currentSettings.notchFreq, 
                        EEGProcessingConfig.FilterConfig.NOTCH_FILTER_BETA, 
                        EEGProcessingConfig.FilterConfig.NOTCH_FILTER_USE_BETA)
                    notchFilterAF8 = NotchFilter(sampleRate, currentSettings.notchFreq, 
                        EEGProcessingConfig.FilterConfig.NOTCH_FILTER_BETA, 
                        EEGProcessingConfig.FilterConfig.NOTCH_FILTER_USE_BETA)
                    android.util.Log.d("EEGDataProcessor", "Notch filters initialized: ${currentSettings.notchFreq}Hz, beta=${EEGProcessingConfig.FilterConfig.NOTCH_FILTER_BETA}")
                } catch (e: Exception) {
                    android.util.Log.e("EEGDataProcessor", "Error creating notch filters", e)
                }
            }
            
            if (currentSettings.firEnabled) {
                try {
                    firFilterAF7 = when (currentSettings.firType) {
                        FilterType.BANDPASS -> FIRFilter.createBandpass(sampleRate, currentSettings.firFreq1, currentSettings.firFreq2)
                        FilterType.LOWPASS -> FIRFilter.createLowpass(sampleRate, currentSettings.firFreq1)
                        FilterType.HIGHPASS -> FIRFilter.createHighpass(sampleRate, currentSettings.firFreq1)
                    }
                    firFilterAF8 = when (currentSettings.firType) {
                        FilterType.BANDPASS -> FIRFilter.createBandpass(sampleRate, currentSettings.firFreq1, currentSettings.firFreq2)
                        FilterType.LOWPASS -> FIRFilter.createLowpass(sampleRate, currentSettings.firFreq1)
                        FilterType.HIGHPASS -> FIRFilter.createHighpass(sampleRate, currentSettings.firFreq1)
                    }
                    android.util.Log.d("EEGDataProcessor", "FIR filters initialized: ${currentSettings.firType} ${currentSettings.firFreq1}-${currentSettings.firFreq2}Hz")
                } catch (e: Exception) {
                    android.util.Log.e("EEGDataProcessor", "Error creating FIR filters", e)
                    // Reset FIR filter references on error
                    firFilterAF7 = null
                    firFilterAF8 = null
                }
            }
            
            if (currentSettings.fftEnabled) {
                try {
                    fftProcessor = FFT(currentSettings.fftSize)
                    android.util.Log.d("EEGDataProcessor", "FFT processor created: size=${currentSettings.fftSize}, Python-compatible normalization enabled")
                } catch (e: Exception) {
                    android.util.Log.e("EEGDataProcessor", "Error creating FFT processor", e)
                }
            }
            
            // 初始化时间常数滤波器 - 按照UniApp版本实现
            if (currentSettings.timeConstantEnabled) {
                try {
                    val cutoffFreq = currentSettings.getTimeConstantCutoffFreq()
                    if (cutoffFreq > 0 && cutoffFreq < sampleRate / 2) {
                        // 使用FIR滤波器实现时间常数效果，因为IIR实现目前有bug
                        timeConstantFilterAF7 = FIRFilter.createHighpass(sampleRate, cutoffFreq)
                        timeConstantFilterAF8 = FIRFilter.createHighpass(sampleRate, cutoffFreq)
                        android.util.Log.d("EEGDataProcessor", "Time constant filters (FIR) initialized: ${currentSettings.timeConstantValue}s -> ${String.format(Locale.US, "%.3f", cutoffFreq)}Hz")
                    } else {
                        android.util.Log.w("EEGDataProcessor", "Invalid time constant cutoff frequency: $cutoffFreq Hz")
                    }
                } catch (e: Exception) {
                    android.util.Log.e("EEGDataProcessor", "Error creating time constant filters", e)
                    timeConstantFilterAF7 = null
                    timeConstantFilterAF8 = null
                }
            }
            
            // 创建DC去除滤波器 - 始终启用，作为强制预处理步骤
            try {
                // 使用默认设置，截止频率约0.05Hz，专门去除DC偏移和极低频漂移
                dcRemovalFilterAF7 = DCRemovalFilter.createDefault(sampleRate)
                dcRemovalFilterAF8 = DCRemovalFilter.createDefault(sampleRate)
                android.util.Log.d("EEGDataProcessor", "DC removal filters initialized: ${dcRemovalFilterAF7?.calculateCutoffFrequency()?.let { "%.3f".format(it) }}Hz cutoff")
            } catch (e: Exception) {
                android.util.Log.e("EEGDataProcessor", "Error creating DC removal filters", e)
                dcRemovalFilterAF7 = null
                dcRemovalFilterAF8 = null
            }
        } catch (e: Exception) {
            android.util.Log.e("EEGDataProcessor", "Error in initializeFilters", e)
        }
    }
    
    /**
     * Start asynchronous FFT processing
     * This runs in a separate coroutine to avoid blocking the main data flow
     */
    fun startAsyncFFT(scope: CoroutineScope) {
        if (!currentSettings.fftEnabled) return
        
        stopAsyncFFT() // Stop any existing FFT job
        
        fftJob = scope.launch(Dispatchers.Default) {
            while (isActive) {
                try {
                    val fftResults = computeFFT()
                    if (fftResults != null) {
                        val spectrumPair = computeSpectrum(fftResults)
                        _spectrumData.emit(spectrumPair)
                    }
                    
                    // Control FFT computation frequency (10Hz update rate)
                    // This reduces CPU load compared to computing FFT for every packet
                    delay(100)
                    
                } catch (e: Exception) {
                    android.util.Log.e("EEGDataProcessor", "Error in async FFT processing", e)
                    delay(500) // Longer delay on error
                }
            }
        }
    }
    
    /**
     * Stop asynchronous FFT processing
     */
    fun stopAsyncFFT() {
        fftJob?.cancel()
        fftJob = null
    }
    
    /**
     * Trigger immediate FFT computation asynchronously
     */
    private fun triggerAsyncFFT() {
        // Launch a single FFT computation without creating a persistent job
        GlobalScope.launch(Dispatchers.Default) {
            try {
                val fftResults = computeFFT()
                if (fftResults != null) {
                    val spectrumPair = computeSpectrum(fftResults)
                    // Update cached spectrum data
                    lastSpectrumAF7 = spectrumPair.first
                    lastSpectrumAF8 = spectrumPair.second
                    // Emit to spectrum data flow if there are subscribers
                    _spectrumData.emit(spectrumPair)
                }
            } catch (e: Exception) {
                android.util.Log.e("EEGDataProcessor", "Error in triggered FFT computation", e)
            }
        }
    }
    
    /**
     * Reset all filters and clear buffers
     */
    fun reset() {
        notchFilterAF7?.reset()
        notchFilterAF8?.reset()
        dcRemovalFilterAF7?.reset()
        dcRemovalFilterAF8?.reset()
        // FIR滤波器没有reset方法，需要重新创建
        af7Buffer.clear()
        af8Buffer.clear()
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        stopAsyncFFT()
        notchFilterAF7?.close()
        notchFilterAF8?.close()
        firFilterAF7?.close()
        firFilterAF8?.close()
        fftProcessor?.close()
        timeConstantFilterAF7?.close()
        timeConstantFilterAF8?.close()
        // DC removal filters只需要reset，没有需要释放的资源
        dcRemovalFilterAF7?.reset()
        dcRemovalFilterAF8?.reset()
        af7Buffer.clear()
        af8Buffer.clear()
    }
    
    init {
        initializeFilters()
    }
}

enum class Channel {
    AF7, AF8
}

data class ProcessedEEGData(
    val originalPacket: EEGPacket,
    val processedAF7: DoubleArray,
    val processedAF8: DoubleArray,
    val af7Buffer: List<Double>,
    val af8Buffer: List<Double>,
    val fftResults: FFTResults?,
    val af7Spectrum: SpectrumData? = null,
    val af8Spectrum: SpectrumData? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ProcessedEEGData

        if (originalPacket != other.originalPacket) return false
        if (!processedAF7.contentEquals(other.processedAF7)) return false
        if (!processedAF8.contentEquals(other.processedAF8)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = originalPacket.hashCode()
        result = 31 * result + processedAF7.contentHashCode()
        result = 31 * result + processedAF8.contentHashCode()
        return result
    }
}

data class FFTResults(
    val af7Result: FFTResult,
    val af8Result: FFTResult
)