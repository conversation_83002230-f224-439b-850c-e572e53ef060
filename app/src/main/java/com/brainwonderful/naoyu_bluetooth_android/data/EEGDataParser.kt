package com.brainwonderful.naoyu_bluetooth_android.data

import android.util.Log
import kotlin.math.pow

/**
 * Parser for EEG data packets following the UniApp format:
 * 244 bytes total: 4 signal bytes + 120 AF7 bytes + 120 AF8 bytes
 * Each channel has 40 data points, 3 bytes per point (24-bit ADC)
 */
class EEGDataParser(
    private val deviceType: DeviceType = DeviceType.DBAY
) {
    companion object {
        private const val TAG = "EEGDataParser"
        
        // ADC conversion constants (from UniApp implementation)
        private const val ADC_REFERENCE = 4.84
        private const val ADC_BITS = 24
        private const val GAIN_FACTOR = 6.0
        private const val MICROVOLT_SCALE = 1_000_000.0
        
        // Conversion factor: (4.84 / 2^24) * 10^6 / 6
        private val CONVERSION_FACTOR = (ADC_REFERENCE / 2.0.pow(ADC_BITS)) * MICROVOLT_SCALE / GAIN_FACTOR
    }
    
    /**
     * Parse raw bytes into EEG packet
     * @param data Raw byte array (must be 244 bytes)
     * @return Parsed EEG packet or null if invalid
     */
    fun parsePacket(data: ByteArray): EEGPacket? {
        if (data.size != EEGPacket.PACKET_SIZE) {
            Log.w(TAG, "Invalid packet size: ${data.size}, expected ${EEGPacket.PACKET_SIZE}")
            return null
        }
        
        try {
            // Parse signal bytes (first 4 bytes)
            val bagIndex = data[0].toInt() and 0xFF
            val signalStatus = data[1].toInt() and 0xFF
            val reserved = data[2].toInt() and 0xFF
            val batteryLevel = data[3].toInt() and 0xFF
            
            // 详细日志：显示原始信号字节
//            Log.d(TAG, "Raw signal bytes: [${data[0]}, ${data[1]}, ${data[2]}, ${data[3]}]")
//            Log.d(TAG, "Parsed signal data - bagIndex: $bagIndex, signalStatus: $signalStatus, reserved: $reserved, batteryLevel: $batteryLevel")
            
            // Parse AF7 data (bytes 4-123)
            val af7Data = parseChannelData(data, 4)
            
            // Parse AF8 data (bytes 124-243)
            val af8Data = parseChannelData(data, 124)
            
            return EEGPacket(
                bagIndex = bagIndex,
                signalStatus = signalStatus,
                reserved = reserved,
                batteryLevel = batteryLevel,
                af7Data = af7Data,
                af8Data = af8Data
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing EEG packet", e)
            return null
        }
    }
    
    /**
     * Parse channel data from raw bytes
     * @param data Full packet data
     * @param startIndex Starting byte index for this channel
     * @return Array of 40 data points in microvolts
     */
    private fun parseChannelData(data: ByteArray, startIndex: Int): DoubleArray {
        val channelData = DoubleArray(EEGPacket.POINTS_PER_CHANNEL)
        
        for (i in 0 until EEGPacket.POINTS_PER_CHANNEL) {
            val byteIndex = startIndex + i * EEGPacket.BYTES_PER_POINT
            
            // Extract 3 bytes and convert to 24-bit signed integer (big-endian)
            val rawValue = extract24BitValue(data, byteIndex)
            
            // Convert to microvolts using the formula from UniApp
            channelData[i] = convertToMicrovolts(rawValue)
        }
        
        return channelData
    }
    
    /**
     * Extract 24-bit signed value from 3 bytes using UniApp method
     * @param data Byte array
     * @param startIndex Starting index
     * @return 24-bit signed integer
     */
    private fun extract24BitValue(data: ByteArray, startIndex: Int): Int {
        val byte1 = data[startIndex].toInt() and 0xFF
        val byte2 = data[startIndex + 1].toInt() and 0xFF
        val byte3 = data[startIndex + 2].toInt() and 0xFF
        
        // UniApp方式：dataView2A7[i] = (viewA7[i * 3] * 0X1000000 + viewA7[i * 3 + 1] * 0X10000 + viewA7[i * 3 + 2] * 0X100)
        // 然后 dataViewA7[i] = (dataViewA7[i] / 0x100)
        val rawValue = (byte1 * 0x1000000 + byte2 * 0x10000 + byte3 * 0x100)
        val value = rawValue / 0x100
        
        return value
    }
    
    /**
     * Convert raw ADC value to microvolts
     * Formula from UniApp: rawValue * (4.84 / 2^24) * 10^6 / 6
     * Note: rawValue has already been divided by 256 in extract24BitValue
     * @param rawValue 24-bit ADC value (already divided by 256)
     * @return Value in microvolts
     */
    private fun convertToMicrovolts(rawValue: Int): Double {
        return rawValue * CONVERSION_FACTOR
    }
    
    /**
     * Validate packet integrity - 修复为与uniapp版本一致的逻辑
     * @param packet Parsed EEG packet
     * @return true if packet appears valid
     */
    fun validatePacket(packet: EEGPacket): Boolean {
//        Log.d(TAG, "Validating packet - bagIndex: ${packet.bagIndex}, signalStatus: ${packet.signalStatus}, batteryLevel: ${packet.batteryLevel}")
        
        // 基本数据完整性检查
        if (packet.af7Data.size != EEGPacket.POINTS_PER_CHANNEL || 
            packet.af8Data.size != EEGPacket.POINTS_PER_CHANNEL) {
            Log.w(TAG, "Invalid channel data size - AF7: ${packet.af7Data.size}, AF8: ${packet.af8Data.size}, expected: ${EEGPacket.POINTS_PER_CHANNEL}")
            return false
        }
        
        // 放宽电池电量验证 - 允许更广泛的值范围
        if (packet.batteryLevel < 0 || packet.batteryLevel > 255) {
            Log.w(TAG, "Invalid battery level range: ${packet.batteryLevel}")
            return false
        }
        
        // 修复信号状态验证逻辑，与uniapp版本保持一致
        // uniapp: let signal = viewSig[1] === 0 ? 0 : 1
        // 任何非0值都被认为是信号脱落（1），0表示信号良好（0）
        val normalizedSignalStatus = if (packet.signalStatus == 0) 0 else 1
//        Log.d(TAG, "Signal status: raw=${packet.signalStatus}, normalized=$normalizedSignalStatus (${if (normalizedSignalStatus == 0) "良好" else "脱落"})")
        
        // 检查EEG数据是否全为0（可能表示无效数据）
        val af7AllZero = packet.af7Data.all { it == 0.0 }
        val af8AllZero = packet.af8Data.all { it == 0.0 }
        
        if (af7AllZero && af8AllZero) {
            Log.w(TAG, "All EEG data points are zero - possible invalid data")
            // 允许全零数据通过，但记录警告
        }
        
        // 检查EEG数据范围（更宽松的检查）
//        val maxValue = 5000.0 // 增加到5000 μV threshold，更宽松
//        val af7OutOfRange = packet.af7Data.count { kotlin.math.abs(it) > maxValue }
//        val af8OutOfRange = packet.af8Data.count { kotlin.math.abs(it) > maxValue }
        
//        if (af7OutOfRange >= 20 || af8OutOfRange >= 20) { // 超过或等于一半的点超范围才报警（40个点的一半是20）
//            Log.w(TAG, "Too many EEG values exceed threshold - AF7: $af7OutOfRange points, AF8: $af8OutOfRange points out of range")
//            // 仍然允许通过，只是警告
//        }
        
        // 显示数据包摘要
//        val af7Range = "AF7: [${packet.af7Data.minOrNull()?.let { "%.2f".format(it) }}, ${packet.af7Data.maxOrNull()?.let { "%.2f".format(it) }}]"
//        val af8Range = "AF8: [${packet.af8Data.minOrNull()?.let { "%.2f".format(it) }}, ${packet.af8Data.maxOrNull()?.let { "%.2f".format(it) }}]"
//        Log.d(TAG, "EEG data ranges - $af7Range, $af8Range")
        
//        Log.d(TAG, "Packet validation passed")
        return true
    }
    
    /**
     * 获取标准化的信号状态（与uniapp版本一致）
     * @param rawSignalStatus 原始信号状态值
     * @return 0表示信号良好，1表示信号脱落
     */
    fun getNormalizedSignalStatus(rawSignalStatus: Int): Int {
        return if (rawSignalStatus == 0) 0 else 1
    }
    
    /**
     * 获取电池状态描述（与uniapp版本一致）
     * @param batteryLevel 电池电量值
     * @return 电池状态描述
     */
    fun getBatteryStatusDescription(batteryLevel: Int): String {
        return when {
            batteryLevel <= 100 -> batteryLevel.toString()
            batteryLevel == 128 -> "充电中"  // 0x80
            batteryLevel == 204 -> "充电完成" // 0xCC
            else -> "未知($batteryLevel)"
        }
    }
}