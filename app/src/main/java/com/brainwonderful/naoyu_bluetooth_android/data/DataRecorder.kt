package com.brainwonderful.naoyu_bluetooth_android.data

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*
import java.io.*
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.*

class DataRecorder(private val context: Context) {
    private var recordingJob: Job? = null
    private var dataChannel: Channel<ByteArray>? = null
    private var currentFile: File? = null
    private var outputStream: BufferedOutputStream? = null
    private var startTime: Long = 0
    private var packetCount: Long = 0
    private var currentDeviceType: DeviceType = DeviceType.DBAY // Default device type
    
    private val _recordingState = MutableStateFlow(false)
    val recordingState: StateFlow<Boolean> = _recordingState.asStateFlow()
    
    private val _recordingStats = MutableStateFlow(RecordingStats())
    val recordingStats: StateFlow<RecordingStats> = _recordingStats.asStateFlow()
    
    companion object {
        private const val FILE_MAGIC = "NEEG"
        private const val FILE_VERSION: Short = 1
        private const val HEADER_SIZE = 64
        private const val PACKET_SIZE = 244
        private const val BUFFER_SIZE = 8192
    }
    
    data class RecordingStats(
        val duration: Long = 0,
        val packetCount: Long = 0,
        val fileSize: Long = 0
    )
    
    data class RecordingMetadata(
        val fileName: String,
        val deviceName: String,
        val startTime: Long,
        val endTime: Long,
        val packetCount: Long,
        val duration: Long,
        val fileSize: Long
    )
    
    fun startRecording(deviceName: String) {
        if (_recordingState.value) return
        
        // Determine device type from device name
        currentDeviceType = DeviceType.fromDeviceName(deviceName)
        
        // Create new channel for this recording session
        dataChannel = Channel<ByteArray>(Channel.UNLIMITED)
        
        recordingJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                startTime = System.currentTimeMillis()
                packetCount = 0
                
                // Create temporary file
                val tempDir = File(context.filesDir, "recordings/temp")
                tempDir.mkdirs()
                currentFile = File(tempDir, "recording_${startTime}.neeg")
                
                outputStream = BufferedOutputStream(FileOutputStream(currentFile), BUFFER_SIZE)
                
                // Write file header
                writeHeader(deviceName)
                
                _recordingState.value = true
                
                // Process data from channel
                val channel = dataChannel ?: return@launch
                for (data in channel) {
                    if (!_recordingState.value) break
                    
                    // Write timestamp + data
                    val buffer = ByteBuffer.allocate(8 + data.size)
                    buffer.order(ByteOrder.LITTLE_ENDIAN)
                    buffer.putLong(System.currentTimeMillis())
                    buffer.put(data)
                    
                    outputStream?.write(buffer.array())
                    packetCount++
                    
                    // Update stats
                    updateStats()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                closeOutputStream()
            }
        }
    }
    
    fun stopRecording() {
        _recordingState.value = false
        dataChannel?.close()
        dataChannel = null
        recordingJob?.cancel()
    }
    
    suspend fun addData(data: ByteArray) {
        if (_recordingState.value && data.size == PACKET_SIZE) {
            dataChannel?.send(data)
        }
    }
    
    fun saveRecording(fileName: String, exportTxt: Boolean = false, onlyTxt: Boolean = false): RecordingMetadata? {
        val tempFile = currentFile ?: return null
        if (!tempFile.exists()) return null
        
        return try {
            // Create permanent directory
            val recordingsDir = File(context.filesDir, "recordings")
            recordingsDir.mkdirs()
            
            if (onlyTxt) {
                // Only save as txt format
                exportToTxtFormat(tempFile, fileName)
                
                // Delete the temporary neeg file
                tempFile.delete()
                currentFile = null
                
                // Create metadata for txt file
                val txtFile = File(recordingsDir, "$fileName.txt")
                RecordingMetadata(
                    fileName = fileName,
                    deviceName = "", // TODO: Read from header
                    startTime = startTime,
                    endTime = System.currentTimeMillis(),
                    packetCount = packetCount,
                    duration = System.currentTimeMillis() - startTime,
                    fileSize = txtFile.length()
                ).also {
                    // Save metadata JSON
                    saveMetadata(it)
                }
            } else {
                // Move file with new name (neeg format)
                val permanentFile = File(recordingsDir, "$fileName.neeg")
                tempFile.renameTo(permanentFile)
                
                // Export to txt format if requested (both formats)
                if (exportTxt) {
                    exportToTxtFormat(permanentFile, fileName)
                }
                
                // Create metadata
                RecordingMetadata(
                    fileName = fileName,
                    deviceName = "", // TODO: Read from header
                    startTime = startTime,
                    endTime = System.currentTimeMillis(),
                    packetCount = packetCount,
                    duration = System.currentTimeMillis() - startTime,
                    fileSize = permanentFile.length()
                ).also {
                    // Save metadata JSON
                    saveMetadata(it)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    fun discardRecording() {
        currentFile?.delete()
        currentFile = null
    }
    
    private fun writeHeader(deviceName: String) {
        val buffer = ByteBuffer.allocate(HEADER_SIZE)
        buffer.order(ByteOrder.LITTLE_ENDIAN)
        
        // Magic number
        buffer.put(FILE_MAGIC.toByteArray())
        
        // Version
        buffer.putShort(FILE_VERSION)
        
        // Sample rate - use device-specific sample rate
        buffer.putShort(currentDeviceType.sampleRate.toInt().toShort())
        
        // Channel count
        buffer.put(2)
        
        // Device name (32 bytes)
        val deviceBytes = deviceName.toByteArray()
        val deviceBuffer = ByteArray(32)
        System.arraycopy(deviceBytes, 0, deviceBuffer, 0, minOf(deviceBytes.size, 32))
        buffer.put(deviceBuffer)
        
        // Start timestamp
        buffer.putLong(startTime)
        
        // Reserved (15 bytes)
        buffer.put(ByteArray(15))
        
        outputStream?.write(buffer.array())
    }
    
    private fun updateStats() {
        val duration = System.currentTimeMillis() - startTime
        val fileSize = currentFile?.length() ?: 0
        
        _recordingStats.value = RecordingStats(
            duration = duration,
            packetCount = packetCount,
            fileSize = fileSize
        )
    }
    
    private fun closeOutputStream() {
        try {
            outputStream?.flush()
            outputStream?.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        outputStream = null
    }
    
    private fun saveMetadata(metadata: RecordingMetadata) {
        try {
            val metadataDir = File(context.filesDir, "recordings/metadata")
            metadataDir.mkdirs()
            
            val metadataFile = File(metadataDir, "${metadata.fileName}.json")
            val json = """
                {
                    "fileName": "${metadata.fileName}",
                    "deviceName": "${metadata.deviceName}",
                    "startTime": ${metadata.startTime},
                    "endTime": ${metadata.endTime},
                    "packetCount": ${metadata.packetCount},
                    "duration": ${metadata.duration},
                    "fileSize": ${metadata.fileSize}
                }
            """.trimIndent()
            
            metadataFile.writeText(json)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Export .neeg file to txt format compatible with EEGDataAnalysis
     */
    private fun exportToTxtFormat(neegFile: File, fileName: String) {
        try {
            // Always save txt files to recordings directory
            val recordingsDir = File(context.filesDir, "recordings")
            recordingsDir.mkdirs()
            val txtFile = File(recordingsDir, "$fileName.txt")
            
            val parser = EEGDataParser()
            
            BufferedInputStream(FileInputStream(neegFile)).use { inputStream ->
                PrintWriter(FileWriter(txtFile)).use { writer ->
                    // Skip header
                    inputStream.skip(HEADER_SIZE.toLong())
                    
                    val timestampBuffer = ByteArray(8)
                    val packetBuffer = ByteArray(PACKET_SIZE)
                    
                    while (true) {
                        // Read timestamp (8 bytes)
                        val timestampRead = inputStream.read(timestampBuffer)
                        if (timestampRead != 8) break
                        
                        // Read packet data (244 bytes)
                        val packetRead = inputStream.read(packetBuffer)
                        if (packetRead != PACKET_SIZE) break
                        
                        // Parse EEG packet
                        val packet = parser.parsePacket(packetBuffer)
                        if (packet != null) {
                            // Use StringBuilder for batch writing to improve performance
                            val batchBuilder = StringBuilder(EEGPacket.POINTS_PER_CHANNEL * 20) // Pre-allocate capacity
                            
                            // Build all lines for this packet in one go
                            for (i in 0 until EEGPacket.POINTS_PER_CHANNEL) {
                                val af7 = packet.af7Data[i]
                                val af8 = packet.af8Data[i]
                                batchBuilder.append(String.format("%.6f %.6f\n", af7, af8))
                            }
                            
                            // Write batch to file at once
                            writer.print(batchBuilder.toString())
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    fun listRecordings(): List<RecordingMetadata> {
        val recordings = mutableListOf<RecordingMetadata>()
        val recordingsDir = File(context.filesDir, "recordings")
        
        if (recordingsDir.exists()) {
            recordingsDir.listFiles { file -> file.extension == "neeg" }?.forEach { file ->
                try {
                    recordings.add(
                        RecordingMetadata(
                            fileName = file.nameWithoutExtension,
                            deviceName = "Unknown", // Could read from file header
                            startTime = file.lastModified(),
                            endTime = file.lastModified(),
                            packetCount = 0, // Could calculate from file size
                            duration = 0, // Could calculate from file size
                            fileSize = file.length()
                        )
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        
        return recordings
    }
}