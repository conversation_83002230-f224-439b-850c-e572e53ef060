package com.brainwonderful.naoyu_bluetooth_android

import android.app.Application
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.BluetoothManager

class NaoyuApplication : Application() {
    
    companion object {
        lateinit var instance: NaoyuApplication
            private set
    }
    
    lateinit var bluetoothManager: BluetoothManager
        private set
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        bluetoothManager = BluetoothManager(this)
    }
}