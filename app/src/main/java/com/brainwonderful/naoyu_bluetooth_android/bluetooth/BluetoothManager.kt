package com.brainwonderful.naoyu_bluetooth_android.bluetooth

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.bluetooth.le.ScanFilter
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.util.UUID
import com.brainwonderful.naoyu_bluetooth_android.data.EEGDataParser
import com.brainwonderful.naoyu_bluetooth_android.data.EEGDataProcessor
import com.brainwonderful.naoyu_bluetooth_android.data.EEGPacket
import com.brainwonderful.naoyu_bluetooth_android.data.ProcessedEEGData
import com.brainwonderful.naoyu_bluetooth_android.data.DeviceType

/**
 * 蓝牙管理器
 * 负责蓝牙设备的扫描、连接、数据传输等功能
 */
class BluetoothManager(private val context: Context) {
    
    companion object {
        private const val TAG = "BluetoothManager"
        
        // 服务和特征UUID（根据UniApp版本的配置）
        private const val SERVICE_UUID = "6E40FF01-B5A3-F393-E0A9-E50E24DCCA9E"
        private const val WRITE_CHARACTERISTIC_UUID = "6E40FF02-B5A3-F393-E0A9-E50E24DCCA9E"
        private const val NOTIFY_CHARACTERISTIC_UUID = "6E40FF03-B5A3-F393-E0A9-E50E24DCCA9E"
        
        // 设备信息服务
        private const val DEVICE_INFO_SERVICE_UUID = "0000180A-0000-1000-8000-00805F9B34FB"
        private const val MANUFACTURER_NAME_UUID = "00002A29-0000-1000-8000-00805F9B34FB"
        private const val FIRMWARE_REVISION_UUID = "00002A26-0000-1000-8000-00805F9B34FB"
        
        // 通知描述符
        private const val CLIENT_CHARACTERISTIC_CONFIG = "00002902-0000-1000-8000-00805f9b34fb"
        
        // 扫描超时时间设置
        private const val SCAN_TIMEOUT_MS = 30000L // 30秒，避免接近系统30分钟限制
        private const val MAX_SCAN_DURATION_MS = 1800000L // 30分钟，系统硬限制
    }
    
    private val bluetoothManager: BluetoothManager by lazy {
        context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    }
    
    private val bluetoothAdapter: BluetoothAdapter? by lazy {
        bluetoothManager.adapter
    }
    
    private var bluetoothLeScanner: BluetoothLeScanner? = null
    private var bluetoothGatt: BluetoothGatt? = null
    
    // 状态流
    private val _scanState = MutableStateFlow<ScanState>(ScanState.IDLE)
    val scanState: StateFlow<ScanState> = _scanState.asStateFlow()
    
    private val _connectionState = MutableStateFlow<ConnectionState>(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    private val _discoveredDevices = MutableStateFlow<List<DiscoveredDevice>>(emptyList())
    val discoveredDevices: StateFlow<List<DiscoveredDevice>> = _discoveredDevices.asStateFlow()
    
    private val _receivedData = MutableStateFlow<ReceivedData?>(null)
    val receivedData: StateFlow<ReceivedData?> = _receivedData.asStateFlow()
    
    private val _bluetoothStatus = MutableStateFlow(BluetoothStatus())
    val bluetoothStatus: StateFlow<BluetoothStatus> = _bluetoothStatus.asStateFlow()
    
    private val _connectedDevice = MutableStateFlow<ConnectedDeviceInfo?>(null)
    val connectedDevice: StateFlow<ConnectedDeviceInfo?> = _connectedDevice.asStateFlow()
    
    // EEG数据处理
    private var deviceType: DeviceType = DeviceType.DBAY
    private var eegDataParser = EEGDataParser(deviceType)
    private var eegDataProcessor = EEGDataProcessor(deviceType.sampleRate)
    
    // 包序跟踪
    private var lastBagIndex: Int? = null
    private var totalPackets = 0
    private var lostPackets = 0
    private var firstPacketTime: Long = 0
    private var lastPacketTime: Long = 0
    
    // 处理后的EEG数据流 - 使用MutableSharedFlow转发，避免引用过期
    private val _processedEEGData = MutableSharedFlow<ProcessedEEGData>(replay = 1)
    val processedEEGData: SharedFlow<ProcessedEEGData> = _processedEEGData.asSharedFlow()
    
    // 原始数据流，用于记录
    private val _rawEEGData = MutableSharedFlow<ByteArray>(replay = 1)
    val rawEEGData: SharedFlow<ByteArray> = _rawEEGData.asSharedFlow()
    
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    // 数据流连接任务
    private var dataFlowJob: kotlinx.coroutines.Job? = null
    
    init {
        // 初始化时建立数据流连接
        setupEEGDataFlow()
    }
    
    // 扫描超时处理
    private val scanTimeoutHandler = Handler(Looper.getMainLooper())
    private var scanTimeoutRunnable: Runnable? = null
    private var scanStartTime: Long = 0
    
    // 设备扫描回调
    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            Log.d(TAG, "扫描到设备: ${result.device.address}")
            handleScanResult(result)
        }
        
        override fun onBatchScanResults(results: MutableList<ScanResult>) {
            super.onBatchScanResults(results)
            Log.d(TAG, "批量扫描结果: ${results.size} 个设备")
            results.forEach { handleScanResult(it) }
        }
        
        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            val errorMessage = when (errorCode) {
                SCAN_FAILED_ALREADY_STARTED -> "扫描已经开始"
                SCAN_FAILED_APPLICATION_REGISTRATION_FAILED -> "应用注册失败"
                SCAN_FAILED_INTERNAL_ERROR -> "内部错误"
                SCAN_FAILED_FEATURE_UNSUPPORTED -> "功能不支持"
                else -> "未知错误: $errorCode"
            }
            Log.e(TAG, "扫描失败: $errorMessage")
            _scanState.value = ScanState.ERROR(errorMessage)
        }
    }
    
    // GATT回调
    private val gattCallback = object : BluetoothGattCallback() {
        @SuppressLint("MissingPermission")
        override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
            Log.d(TAG, "连接状态改变 - 状态: $status, 新状态: $newState")
            when (newState) {
                BluetoothProfile.STATE_CONNECTED -> {
                    Log.i(TAG, "设备连接成功")
                    _connectionState.value = ConnectionState.CONNECTED
                    // 发现服务
                    val discoverResult = bluetoothGatt?.discoverServices()
                    Log.d(TAG, "开始发现服务，结果: $discoverResult")
                    // 确保已连接设备在设备列表中正确显示
                    updateConnectedDeviceInList()
                }
                BluetoothProfile.STATE_DISCONNECTED -> {
                    Log.i(TAG, "设备断开连接")
                    _connectionState.value = ConnectionState.DISCONNECTED
                    // 清除连接设备信息
                    val disconnectedAddress = _connectedDevice.value?.address
                    _connectedDevice.value = null
                    bluetoothGatt?.close()
                    bluetoothGatt = null
                    // 更新设备列表中断开设备的状态
                    updateDisconnectedDeviceInList(disconnectedAddress)
                }
            }
        }
        
        @SuppressLint("MissingPermission")
        override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
            Log.d(TAG, "服务发现回调 - 状态: $status")
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.i(TAG, "服务发现成功")
                // 重置包序跟踪
                resetPacketSequence()
                // 先启用通知开始接收数据
                enableNotifications()
                // 延迟读取固件版本，避免与通知设置操作冲突
                Handler(Looper.getMainLooper()).postDelayed({
                    readFirmwareVersion()
                }, 1000) // 延迟1秒
            } else {
                Log.e(TAG, "服务发现失败，状态码: $status")
            }
        }
        
        override fun onCharacteristicChanged(
            gatt: BluetoothGatt,
            characteristic: BluetoothGattCharacteristic,
            value: ByteArray
        ) {
            // 处理接收到的数据
            handleReceivedData(value)
        }
        
        override fun onCharacteristicWrite(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic?,
            status: Int
        ) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.i(TAG, "数据写入成功")
            }
        }
        
        override fun onCharacteristicRead(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic?,
            status: Int
        ) {
            Log.d(TAG, "onCharacteristicRead 状态: $status, UUID: ${characteristic?.uuid}")
            
            if (status == BluetoothGatt.GATT_SUCCESS && characteristic != null) {
                when (characteristic.uuid.toString().uppercase()) {
                    MANUFACTURER_NAME_UUID.uppercase() -> {
                        // 根据题目要求，从0x2A29特征值读取固件版本信息
                        val firmwareVersion = String(characteristic.value)
                        Log.i(TAG, "从0x2A29读取固件版本成功: $firmwareVersion")
                        // 更新连接设备信息
                        _connectedDevice.value?.let { currentDevice ->
                            _connectedDevice.value = currentDevice.copy(firmwareVersion = firmwareVersion)
                            Log.d(TAG, "已更新连接设备的固件版本信息")
                        }
                    }
                    FIRMWARE_REVISION_UUID.uppercase() -> {
                        val firmwareRevision = String(characteristic.value)
                        Log.i(TAG, "固件修订版本: $firmwareRevision")
                    }
                }
            } else {
                Log.w(TAG, "读取特征值失败，状态码: $status, UUID: ${characteristic?.uuid}")
                // 如果是固件版本读取失败，设置默认版本
                if (characteristic?.uuid.toString().uppercase() == MANUFACTURER_NAME_UUID.uppercase()) {
                    Log.w(TAG, "固件版本读取失败，设置默认版本")
                    setDefaultFirmwareVersion()
                }
            }
        }
    }
    
    /**
     * 检查蓝牙是否可用
     */
    fun isBluetoothEnabled(): Boolean {
        return bluetoothAdapter?.isEnabled == true
    }
    
    /**
     * 检查是否有必要的权限
     */
    fun hasRequiredPermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12及以上 - 使用了neverForLocation标志，只需要蓝牙权限
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 11及以下 - 需要位置权限
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 开始扫描设备
     */
    @SuppressLint("MissingPermission")
    fun startScan() {
        if (!isBluetoothEnabled()) {
            _scanState.value = ScanState.ERROR("蓝牙未开启")
            return
        }
        
        if (!hasRequiredPermissions()) {
            _scanState.value = ScanState.ERROR("缺少必要权限")
            return
        }
        
        bluetoothLeScanner = bluetoothAdapter?.bluetoothLeScanner
        
        // 设置扫描参数
        val scanSettings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY) // 低延迟模式
            .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
            .build()
        
        // 不使用过滤器，扫描所有设备
        val scanFilters = emptyList<ScanFilter>()
        
        // 保留已连接设备，重新初始化设备列表时保持连接状态
        initializeDeviceListForScan()
        
        Log.d(TAG, "开始扫描蓝牙设备...")
        bluetoothLeScanner?.startScan(scanFilters, scanSettings, scanCallback)
        _scanState.value = ScanState.SCANNING
        
        // 记录扫描开始时间
        scanStartTime = System.currentTimeMillis()
        
        // 设置扫描超时
        setupScanTimeout()
    }
    
    /**
     * 初始化扫描时的设备列表，保留已连接设备状态
     */
    @SuppressLint("MissingPermission")
    private fun initializeDeviceListForScan() {
        val currentDevices = _discoveredDevices.value.toMutableList()
        val connectedDeviceAddress = _connectedDevice.value?.address
        
        // 清空设备列表，但保留已连接的设备
        val devicesToKeep = if (connectedDeviceAddress != null) {
            currentDevices.filter { it.address == connectedDeviceAddress }
        } else {
            emptyList()
        }
        
        // 重新设置设备列表，只保留已连接的设备
        _discoveredDevices.value = devicesToKeep.toMutableList()
        
        // 添加已配对的设备（排除已连接的设备以避免重复）
        addBondedDevices(excludeConnected = true)
    }
    
    /**
     * 添加已配对的设备到列表
     * @param excludeConnected 是否排除已连接的设备
     */
    @SuppressLint("MissingPermission")
    private fun addBondedDevices(excludeConnected: Boolean = false) {
        val bondedDevices = bluetoothAdapter?.bondedDevices
        val connectedDeviceAddress = _connectedDevice.value?.address
        
        if (bondedDevices != null) {
            Log.d(TAG, "已配对设备数量: ${bondedDevices.size}")
            val devices = bondedDevices.mapNotNull { device ->
                val deviceName = device.name
                
                // 如果需要排除已连接设备，且当前设备是已连接设备，则跳过
                if (excludeConnected && device.address == connectedDeviceAddress) {
                    Log.d(TAG, "跳过已连接设备: $deviceName (${device.address})")
                    return@mapNotNull null
                }
                
                // 按照UniApp版本的过滤规则：过滤无名称和"未知设备"
                if (deviceName.isNullOrEmpty() || deviceName == "未知设备") {
                    Log.d(TAG, "过滤已配对设备 - 地址: ${device.address}, 原因: 无名称或未知设备")
                    null
                } else {
                    // 检查是否是Dbay设备
                    if (deviceName.startsWith("Dbay", ignoreCase = true)) {
                        Log.d(TAG, "发现已配对的脑电设备: $deviceName")
                    }
                    
                    DiscoveredDevice(
                        device = device,
                        name = deviceName,
                        address = device.address,
                        rssi = -50 // 已配对设备没有RSSI，使用默认值
                    )
                }
            }
            
            // 合并到现有设备列表
            val currentDevices = _discoveredDevices.value.toMutableList()
            devices.forEach { newDevice ->
                val existingIndex = currentDevices.indexOfFirst { it.address == newDevice.address }
                if (existingIndex >= 0) {
                    // 如果设备已存在，更新信息但保持原有状态
                    currentDevices[existingIndex] = newDevice
                } else {
                    // 新设备，直接添加
                    currentDevices.add(newDevice)
                }
            }
            _discoveredDevices.value = currentDevices
        }
    }
    
    /**
     * 停止扫描
     */
    @SuppressLint("MissingPermission")
    fun stopScan() {
        bluetoothLeScanner?.stopScan(scanCallback)
        _scanState.value = ScanState.IDLE
        
        // 清理扫描超时
        clearScanTimeout()
        
        // 记录扫描持续时间
        if (scanStartTime > 0) {
            val scanDuration = System.currentTimeMillis() - scanStartTime
            Log.d(TAG, "扫描持续时间: ${scanDuration}ms")
            scanStartTime = 0
        }
    }
    
    /**
     * 连接设备
     */
    @SuppressLint("MissingPermission")
    fun connectDevice(device: BluetoothDevice) {
        if (_connectionState.value == ConnectionState.CONNECTING) return
        
        // 保存设备信息
        val deviceName = device.name ?: "未知设备"
        _connectedDevice.value = ConnectedDeviceInfo(
            name = deviceName,
            address = device.address
        )
        
        // 根据设备名称确定设备类型和采样率
        deviceType = DeviceType.fromDeviceName(deviceName)
        Log.i(TAG, "设备类型: ${deviceType.displayName}, 采样率: ${deviceType.sampleRate}Hz")
        
        // 重新创建相应采样率的处理器
        eegDataParser = EEGDataParser(deviceType)
        eegDataProcessor = EEGDataProcessor(deviceType.sampleRate)
        
        // 重新建立数据流连接
        setupEEGDataFlow()
        
        _connectionState.value = ConnectionState.CONNECTING
        bluetoothGatt = device.connectGatt(context, false, gattCallback)
    }
    
    /**
     * 断开连接
     */
    @SuppressLint("MissingPermission")
    fun disconnect() {
        bluetoothGatt?.disconnect()
    }
    
    /**
     * 获取蓝牙状态
     */
    @SuppressLint("MissingPermission")
    fun getBluetoothStatus() {
        val adapter = bluetoothAdapter
        if (adapter != null) {
            _bluetoothStatus.value = BluetoothStatus(
                isEnabled = adapter.isEnabled,
                isDiscovering = adapter.isDiscovering,
                adapterName = adapter.name ?: "未知",
                adapterAddress = adapter.address ?: "未知",
                bondedDeviceCount = adapter.bondedDevices?.size ?: 0
            )
        }
    }
    
    /**
     * 修改设备名称
     */
    @SuppressLint("MissingPermission")
    fun renameDevice(newName: String) {
        val service = bluetoothGatt?.getService(UUID.fromString(SERVICE_UUID))
        val characteristic = service?.getCharacteristic(UUID.fromString(WRITE_CHARACTERISTIC_UUID))
        
        if (characteristic != null) {
            // 名称前加0表示修改名称指令
            val data = "0$newName".toByteArray()
            characteristic.value = data
            bluetoothGatt?.writeCharacteristic(characteristic)
        }
    }
    
    /**
     * 处理扫描结果
     */
    @SuppressLint("MissingPermission")
    private fun handleScanResult(result: ScanResult) {
        val device = result.device
        val deviceName = device.name
        val connectedDeviceAddress = _connectedDevice.value?.address
        
        // 按照UniApp版本的过滤规则：过滤无名称和"未知设备"
        if (deviceName.isNullOrEmpty() || deviceName == "未知设备") {
            Log.d(TAG, "过滤设备 - 地址: ${device.address}, 原因: 无名称或未知设备")
            return
        }
        
        // 如果设备已连接，跳过扫描结果处理（保持已连接设备的状态不变）
        if (device.address == connectedDeviceAddress) {
            Log.d(TAG, "跳过已连接设备的扫描结果: $deviceName (${device.address})")
            return
        }
        
        Log.d(TAG, "发现设备 - 名称: $deviceName, 地址: ${device.address}, RSSI: ${result.rssi}")
        
        // 检查是否是脑电设备（Dbay或CT10）
        val isEEGDevice = deviceName.startsWith("Dbay", ignoreCase = true) || 
                          deviceName.startsWith("CT10", ignoreCase = true)
        if (isEEGDevice) {
            val type = DeviceType.fromDeviceName(deviceName)
            Log.d(TAG, "发现脑电设备: $deviceName (${type.displayName})")
        }
        
        val discoveredDevice = DiscoveredDevice(
            device = device,
            name = deviceName,
            address = device.address,
            rssi = result.rssi
        )
        
        coroutineScope.launch {
            val currentDevices = _discoveredDevices.value.toMutableList()
            val existingIndex = currentDevices.indexOfFirst { it.address == device.address }
            
            if (existingIndex >= 0) {
                // 更新现有设备信息（主要是RSSI）
                currentDevices[existingIndex] = discoveredDevice
                Log.d(TAG, "更新设备信息: $deviceName, RSSI: ${result.rssi}")
            } else {
                // 添加新发现的设备
                currentDevices.add(discoveredDevice)
                Log.d(TAG, "添加新设备: $deviceName")
            }
            
            _discoveredDevices.value = currentDevices
            Log.d(TAG, "设备列表更新，当前设备数: ${currentDevices.size}")
        }
    }
    
    /**
     * 读取固件版本 (从0x2A29特征值)
     */
    @SuppressLint("MissingPermission")
    private fun readFirmwareVersion() {
        Log.d(TAG, "开始读取固件版本...")
        
        val gatt = bluetoothGatt
        if (gatt == null) {
            Log.w(TAG, "BluetoothGatt为null，无法读取固件版本")
            setDefaultFirmwareVersion()
            return
        }
        
        // 列出所有可用服务
        val services = gatt.services
        Log.d(TAG, "发现服务数量: ${services.size}")
        services.forEach { service ->
            Log.d(TAG, "服务UUID: ${service.uuid}")
            service.characteristics.forEach { characteristic ->
                Log.d(TAG, "  特征值UUID: ${characteristic.uuid}")
            }
        }
        
        val deviceInfoService = gatt.getService(UUID.fromString(DEVICE_INFO_SERVICE_UUID))
        if (deviceInfoService == null) {
            Log.w(TAG, "未找到设备信息服务 (0x180A)，将使用默认版本信息")
            setDefaultFirmwareVersion()
            return
        }
        
        Log.d(TAG, "找到设备信息服务")
        
        // 根据题目要求，读取0x2A29特征值获取固件版本信息
        val firmwareCharacteristic = deviceInfoService.getCharacteristic(UUID.fromString(MANUFACTURER_NAME_UUID))
        
        if (firmwareCharacteristic != null) {
            val success = gatt.readCharacteristic(firmwareCharacteristic)
            Log.d(TAG, "开始读取0x2A29特征值，结果: $success")
            if (!success) {
                Log.w(TAG, "读取0x2A29特征值失败，将使用默认版本信息")
                setDefaultFirmwareVersion()
            } else {
                // 设置超时，如果3秒内没有收到响应，使用默认版本
                Handler(Looper.getMainLooper()).postDelayed({
                    if (_connectedDevice.value?.firmwareVersion == null) {
                        Log.w(TAG, "固件版本读取超时，使用默认版本")
                        setDefaultFirmwareVersion()
                    }
                }, 3000)
            }
        } else {
            Log.w(TAG, "未找到0x2A29特征值，将使用默认版本信息")
            setDefaultFirmwareVersion()
        }
    }
    
    /**
     * 设置默认固件版本信息
     */
    private fun setDefaultFirmwareVersion() {
        _connectedDevice.value?.let { currentDevice ->
            val defaultVersion = "未知版本"
            _connectedDevice.value = currentDevice.copy(firmwareVersion = defaultVersion)
            Log.d(TAG, "已设置默认固件版本信息: $defaultVersion")
        }
    }
    
    /**
     * 启用通知
     */
    @SuppressLint("MissingPermission")
    private fun enableNotifications() {
        val service = bluetoothGatt?.getService(UUID.fromString(SERVICE_UUID))
        val characteristic = service?.getCharacteristic(UUID.fromString(NOTIFY_CHARACTERISTIC_UUID))
        
        if (characteristic != null) {
            bluetoothGatt?.setCharacteristicNotification(characteristic, true)
            
            // 设置描述符
            val descriptor = characteristic.getDescriptor(UUID.fromString(CLIENT_CHARACTERISTIC_CONFIG))
            descriptor?.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
            bluetoothGatt?.writeDescriptor(descriptor)
        }
    }
    
    /**
     * 处理接收到的数据
     */
    private fun handleReceivedData(data: ByteArray) {
        if (data.size < 100) {
            // 版本信息
            val version = String(data)
            Log.i(TAG, "版本号: $version")
            return
        }
        
        if (data.size >= 244) {
            // 打印完整的包数据（十六进制）
            val hexString = data.joinToString(" ") { byte ->
                "%02X".format(byte.toInt() and 0xFF)
            }
//            Log.d(TAG, "接收到数据包 (${data.size}字节): $hexString")
            
            // 发送原始数据用于记录
            coroutineScope.launch {
                _rawEEGData.emit(data.copyOf())
            }
            
            // 使用新的EEG数据解析器
            val eegPacket = eegDataParser.parsePacket(data)
            if (eegPacket != null && eegDataParser.validatePacket(eegPacket)) {
                // 打印解析后的数据
                val parsedDataLog = buildString {
                    appendLine("---")
                    appendLine("{")
                    appendLine("    type: 7,")
                    appendLine("    address: ${_connectedDevice.value?.name ?: "未知设备"},")
                    append("    AF7Data: [")
                    append(eegPacket.af7Data.joinToString(", ") { "%.6f".format(it) })
                    appendLine("]")
                    append("    AF8Data: [")
                    append(eegPacket.af8Data.joinToString(", ") { "%.6f".format(it) })
                    appendLine("]")
                    appendLine("    signal: ${eegPacket.signalStatus},")
                    appendLine("    ele: ${eegPacket.batteryLevel},")
                    appendLine("    bagIndex: ${eegPacket.bagIndex}")
                    appendLine("}")
                }
//                Log.d(TAG, parsedDataLog)
                
                // 处理包序
                handlePacketSequence(eegPacket.bagIndex)
                
                // 异步处理EEG数据
                coroutineScope.launch {
                    eegDataProcessor.processPacket(eegPacket)
                }
                
                // 保持兼容性，仍然发送旧格式数据
                val parsedData = parseDataPacket(data)
                _receivedData.value = parsedData
            } else {
                Log.w(TAG, "无效的EEG数据包")
            }
        }
    }
    
    /**
     * 解析数据包
     * 244字节：4字节信号 + 120字节AF7 + 120字节AF8
     */
    private fun parseDataPacket(data: ByteArray): ReceivedData {
        val signalData = data.sliceArray(0..3)
        val af7Data = data.sliceArray(4..123)
        val af8Data = data.sliceArray(124..243)
        
        // 解析信号数据（使用与EEGDataParser一致的逻辑）
        val packageIndex = signalData[0].toInt() and 0xFF
        val rawSignalStatus = signalData[1].toInt() and 0xFF
        val signalQuality = eegDataParser.getNormalizedSignalStatus(rawSignalStatus)
        val batteryValue = signalData[3].toInt() and 0xFF
        val battery = eegDataParser.getBatteryStatusDescription(batteryValue)
        
//        Log.d(TAG, "Legacy data parsing - packageIndex: $packageIndex, rawSignal: $rawSignalStatus, normalizedSignal: $signalQuality, battery: $battery")
        
        // 解析通道数据
        val af7Values = parseChannelData(af7Data)
        val af8Values = parseChannelData(af8Data)
        
        return ReceivedData(
            packageIndex = packageIndex,
            signalQuality = signalQuality,
            battery = battery,
            af7Data = af7Values,
            af8Data = af8Values
        )
    }
    
    /**
     * 解析通道数据 - 使用与UniApp一致的方法
     */
    private fun parseChannelData(channelData: ByteArray): FloatArray {
        val values = FloatArray(40)
        
        for (i in 0 until 40) {
            val byte1 = channelData[i * 3].toInt() and 0xFF
            val byte2 = channelData[i * 3 + 1].toInt() and 0xFF
            val byte3 = channelData[i * 3 + 2].toInt() and 0xFF
            
            // UniApp方式：与EEGDataParser保持一致
            val rawValue = (byte1 * 0x1000000 + byte2 * 0x10000 + byte3 * 0x100)
            val signedValue = rawValue / 0x100
            
            // 转换公式：value * (4.84 / 2^24) * 10^6 / 6
            values[i] = (signedValue * 4.84 / 16777216.0 * 1000000.0 / 6.0).toFloat()
        }
        
        return values
    }
    
    /**
     * 处理包序（类似UniApp的getFakeTime）
     * @param currentBagIndex 当前包序（0-255）
     */
    private fun handlePacketSequence(currentBagIndex: Int) {
        val currentTime = System.currentTimeMillis()
        
        // 第一个包
        if (lastBagIndex == null) {
            firstPacketTime = currentTime
            lastPacketTime = currentTime
            totalPackets = 1
            lastBagIndex = currentBagIndex
            Log.i(TAG, "首个数据包，包序: $currentBagIndex")
            return
        }
        
        // 计算包序差值
        val lastIndex = lastBagIndex!!
        var bagIndexDiff = currentBagIndex - lastIndex
        
        // 处理包序回绕（0-255循环）
        if (bagIndexDiff < 0) {
            bagIndexDiff += 256
        }
        
        // 检测丢包
        if (bagIndexDiff > 1) {
            val lostCount = bagIndexDiff - 1
            lostPackets += lostCount
            Log.w(TAG, "检测到丢包！上个包序: $lastIndex, 当前包序: $currentBagIndex, 丢失: $lostCount 个包")
        }
        
        // 更新统计
        totalPackets++
        lastBagIndex = currentBagIndex
        lastPacketTime = currentTime
        
        // 每100个包输出一次统计信息
        if (totalPackets % 100 == 0) {
            val duration = (currentTime - firstPacketTime) / 1000.0
            val lossRate = if (totalPackets > 0) (lostPackets * 100.0 / totalPackets) else 0.0
            Log.i(TAG, "数据统计 - 总包数: $totalPackets, 丢包数: $lostPackets, 丢包率: %.2f%%, 持续时间: %.1f秒".format(lossRate, duration))
        }
    }
    
    /**
     * 重置包序跟踪
     */
    private fun resetPacketSequence() {
        lastBagIndex = null
        totalPackets = 0
        lostPackets = 0
        firstPacketTime = 0
        lastPacketTime = 0
        Log.d(TAG, "包序跟踪已重置")
    }
    
    /**
     * 获取包序统计信息
     */
    fun getPacketStatistics(): PacketStatistics {
        val duration = if (firstPacketTime > 0) {
            (lastPacketTime - firstPacketTime) / 1000.0
        } else {
            0.0
        }
        
        val lossRate = if (totalPackets > 0) {
            (lostPackets * 100.0 / totalPackets)
        } else {
            0.0
        }
        
        return PacketStatistics(
            totalPackets = totalPackets,
            lostPackets = lostPackets,
            lossRate = lossRate,
            durationSeconds = duration
        )
    }
    
    /**
     * 更新已连接设备在设备列表中的状态
     */
    private fun updateConnectedDeviceInList() {
        val connectedDeviceInfo = _connectedDevice.value ?: return
        val currentDevices = _discoveredDevices.value.toMutableList()
        
        // 查找已连接设备在列表中的位置
        val existingIndex = currentDevices.indexOfFirst { it.address == connectedDeviceInfo.address }
        
        if (existingIndex < 0) {
            // 如果已连接设备不在列表中，添加它
            val connectedDevice = DiscoveredDevice(
                device = bluetoothGatt?.device ?: return,
                name = connectedDeviceInfo.name,
                address = connectedDeviceInfo.address,
                rssi = -50 // 已连接设备使用默认RSSI值
            )
            currentDevices.add(0, connectedDevice) // 添加到列表顶部
            _discoveredDevices.value = currentDevices
            Log.d(TAG, "已连接设备添加到设备列表: ${connectedDeviceInfo.name}")
        }
    }
    
    /**
     * 更新断开连接设备在设备列表中的状态
     */
    private fun updateDisconnectedDeviceInList(disconnectedAddress: String?) {
        if (disconnectedAddress == null) return
        
        // 设备断开连接后，保持在设备列表中，但状态会通过UI层的isConnected判断来更新
        Log.d(TAG, "设备断开连接，地址: $disconnectedAddress")
    }
    
    /**
     * 更新滤波器设置
     */
    fun updateFilterSettings(settings: EEGDataProcessor.FilterSettings) {
        eegDataProcessor.updateSettings(settings)
    }
    
    /**
     * 重置滤波器状态
     */
    fun resetFilters() {
        eegDataProcessor.reset()
    }
    
    /**
     * 设置扫描超时
     */
    private fun setupScanTimeout() {
        // 清理之前的超时设置
        clearScanTimeout()
        
        // 创建超时任务
        scanTimeoutRunnable = Runnable {
            Log.w(TAG, "扫描超时，自动停止扫描")
            stopScan()
            _scanState.value = ScanState.ERROR("扫描超时，已自动停止")
        }
        
        // 设置超时
        scanTimeoutHandler.postDelayed(scanTimeoutRunnable!!, SCAN_TIMEOUT_MS)
        Log.d(TAG, "设置扫描超时: ${SCAN_TIMEOUT_MS}ms")
    }
    
    /**
     * 清理扫描超时
     */
    private fun clearScanTimeout() {
        scanTimeoutRunnable?.let { runnable ->
            scanTimeoutHandler.removeCallbacks(runnable)
            scanTimeoutRunnable = null
            Log.d(TAG, "清理扫描超时")
        }
    }
    
    /**
     * 获取扫描剩余时间（毫秒）
     */
    fun getScanRemainingTime(): Long {
        return if (scanStartTime > 0) {
            val elapsed = System.currentTimeMillis() - scanStartTime
            maxOf(0, SCAN_TIMEOUT_MS - elapsed)
        } else {
            0
        }
    }
    
    /**
     * 检查是否正在扫描
     */
    fun isScanning(): Boolean {
        return _scanState.value == ScanState.SCANNING
    }
    
    /**
     * 建立EEG数据流连接
     */
    private fun setupEEGDataFlow() {
        // 取消之前的数据流任务
        dataFlowJob?.cancel()
        
        // 建立新的数据流连接
        dataFlowJob = coroutineScope.launch {
            eegDataProcessor.processedData.collect { processedData ->
                _processedEEGData.emit(processedData)
            }
        }
        
        Log.d(TAG, "EEG数据流连接已建立")
    }
    
    /**
     * 清理资源
     */
    @SuppressLint("MissingPermission")
    fun cleanup() {
        // 清理数据流连接
        dataFlowJob?.cancel()
        dataFlowJob = null
        
        // 清理扫描超时
        clearScanTimeout()
        
        // 断开连接
        bluetoothGatt?.close()
        bluetoothGatt = null
        
        // 清理状态
        _scanState.value = ScanState.IDLE
        _connectionState.value = ConnectionState.DISCONNECTED
        _connectedDevice.value = null
        _receivedData.value = null
        
        // 清理设备列表
        _discoveredDevices.value = emptyList()
        
        // 清理EEG数据处理
        eegDataProcessor.cleanup()
    }
}

// 数据类定义
data class DiscoveredDevice(
    val device: BluetoothDevice,
    val name: String,
    val address: String,
    val rssi: Int
)

data class ConnectedDeviceInfo(
    val name: String,
    val address: String,
    val firmwareVersion: String? = null
)

data class ReceivedData(
    val packageIndex: Int,
    val signalQuality: Int,
    val battery: String,
    val af7Data: FloatArray,
    val af8Data: FloatArray
)

data class BluetoothStatus(
    val isEnabled: Boolean = false,
    val isDiscovering: Boolean = false,
    val adapterName: String = "",
    val adapterAddress: String = "",
    val bondedDeviceCount: Int = 0
)

data class PacketStatistics(
    val totalPackets: Int,
    val lostPackets: Int,
    val lossRate: Double,
    val durationSeconds: Double
)

// 状态枚举
sealed class ScanState {
    object IDLE : ScanState()
    object SCANNING : ScanState()
    data class ERROR(val message: String) : ScanState()
}

sealed class ConnectionState {
    object DISCONNECTED : ConnectionState()
    object CONNECTING : ConnectionState()
    object CONNECTED : ConnectionState()
    object ERROR : ConnectionState()
}