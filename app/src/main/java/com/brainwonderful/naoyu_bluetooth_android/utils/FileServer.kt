package com.brainwonderful.naoyu_bluetooth_android.utils

import android.content.Context
import kotlinx.coroutines.*
import java.io.*
import java.net.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 简单的HTTP文件服务器
 * 用于在内网中提供文件下载服务
 */
class FileServer(private val context: Context) {
    
    private var serverSocket: ServerSocket? = null
    private var serverJob: Job? = null
    private var isRunning = false
    
    /**
     * 启动服务器
     */
    fun start(port: Int = 8080) {
        // 如果已经在运行，先停止
        if (isRunning) {
            stop()
            // 等待一小段时间确保端口释放
            Thread.sleep(100)
        }
        
        // 尝试启动服务器，如果端口被占用则尝试其他端口
        var actualPort = port
        var attempts = 0
        val maxAttempts = 10
        
        while (attempts < maxAttempts) {
            try {
                serverSocket = ServerSocket(actualPort)
                isRunning = true
                break
            } catch (e: BindException) {
                attempts++
                actualPort = port + attempts
                if (attempts >= maxAttempts) {
                    throw e
                }
            }
        }
        
        serverJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                while (isRunning && !serverSocket!!.isClosed) {
                    try {
                        val clientSocket = serverSocket!!.accept()
                        // 为每个客户端连接创建新的协程
                        launch {
                            handleClient(clientSocket)
                        }
                    } catch (e: SocketException) {
                        // 服务器关闭时会抛出此异常，正常情况
                        if (isRunning) {
                            e.printStackTrace()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 停止服务器
     */
    fun stop() {
        isRunning = false
        try {
            serverSocket?.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        serverJob?.cancel()
        serverSocket = null
        serverJob = null
    }
    
    /**
     * 获取实际使用的端口
     */
    fun getActualPort(): Int {
        return serverSocket?.localPort ?: -1
    }
    
    /**
     * 处理客户端请求
     */
    private suspend fun handleClient(clientSocket: Socket) {
        withContext(Dispatchers.IO) {
            try {
                val input = BufferedReader(InputStreamReader(clientSocket.getInputStream()))
                val output = clientSocket.getOutputStream()
                
                // 读取HTTP请求
                val requestLine = input.readLine() ?: return@withContext
                val parts = requestLine.split(" ")
                if (parts.size < 2) return@withContext
                
                val method = parts[0]
                val path = parts[1]
                
                // 跳过其他HTTP头
                while (true) {
                    val line = input.readLine()
                    if (line.isNullOrEmpty()) break
                }
                
                when (method) {
                    "GET" -> handleGetRequest(path, output)
                    else -> sendErrorResponse(output, 405, "Method Not Allowed")
                }
                
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                try {
                    clientSocket.close()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
    
    /**
     * 处理GET请求
     */
    private fun handleGetRequest(path: String, output: OutputStream) {
        when {
            path == "/" || path == "/index.html" -> {
                sendFileListPage(output)
            }
            path.startsWith("/download/") -> {
                val fileName = path.substring("/download/".length)
                downloadFile(fileName, output)
            }
            path.startsWith("/delete/") -> {
                val fileName = path.substring("/delete/".length)
                deleteFile(fileName, output)
            }
            else -> {
                sendErrorResponse(output, 404, "Not Found")
            }
        }
    }
    
    /**
     * 发送文件列表页面
     */
    private fun sendFileListPage(output: OutputStream) {
        val recordingsDir = File(context.filesDir, "recordings")
        val tempDir = File(context.filesDir, "recordings/temp")
        val files = mutableListOf<Pair<File, String>>() // File和显示名称的对
        
        // 添加recordings目录下的文件
        if (recordingsDir.exists()) {
            recordingsDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    files.add(Pair(file, file.name))
                }
            }
        }
        
        // 添加temp目录下的文件
        if (tempDir.exists()) {
            tempDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    files.add(Pair(file, "temp/${file.name}"))
                }
            }
        }
        
        // 按修改时间倒序排列
        files.sortByDescending { it.first.lastModified() }
        
        val html = buildString {
            append("""
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>脑域蓝牙数据文件</title>
                    <style>
                        body {
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            margin: 0;
                            padding: 20px;
                            background-color: #f5f5f5;
                        }
                        .container {
                            max-width: 1200px;
                            margin: 0 auto;
                            background: white;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            overflow: hidden;
                        }
                        .header {
                            background: #2196F3;
                            color: white;
                            padding: 20px;
                            text-align: center;
                        }
                        .header h1 {
                            margin: 0;
                            font-size: 24px;
                        }
                        .controls {
                            padding: 20px;
                            border-bottom: 1px solid #eee;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        .refresh-btn {
                            background: #4CAF50;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                        }
                        .refresh-btn:hover {
                            background: #45a049;
                        }
                        .file-count {
                            color: #666;
                            font-size: 14px;
                        }
                        .file-list {
                            padding: 0;
                        }
                        .file-item {
                            display: flex;
                            align-items: center;
                            padding: 15px 20px;
                            border-bottom: 1px solid #eee;
                            transition: background-color 0.2s;
                        }
                        .file-item:hover {
                            background-color: #f9f9f9;
                        }
                        .file-item:last-child {
                            border-bottom: none;
                        }
                        .file-icon {
                            width: 40px;
                            height: 40px;
                            border-radius: 4px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 15px;
                            font-weight: bold;
                            color: white;
                            font-size: 12px;
                        }
                        .file-icon.neeg { background: #FF9800; }
                        .file-icon.txt { background: #4CAF50; }
                        .file-icon.raw { background: #9C27B0; }
                        .file-icon.json { background: #607D8B; }
                        .file-icon.csv { background: #2196F3; }
                        .file-icon.log { background: #FF5722; }
                        .file-info {
                            flex: 1;
                        }
                        .file-name {
                            font-weight: 500;
                            margin-bottom: 4px;
                            color: #333;
                        }
                        .file-details {
                            font-size: 12px;
                            color: #666;
                        }
                        .file-actions {
                            display: flex;
                            gap: 10px;
                        }
                        .btn {
                            padding: 6px 12px;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                            text-decoration: none;
                            display: inline-block;
                        }
                        .btn-download {
                            background: #2196F3;
                            color: white;
                        }
                        .btn-download:hover {
                            background: #1976D2;
                        }
                        .btn-delete {
                            background: #f44336;
                            color: white;
                        }
                        .btn-delete:hover {
                            background: #d32f2f;
                        }
                        .empty-state {
                            text-align: center;
                            padding: 60px 20px;
                            color: #666;
                        }
                        .empty-state h3 {
                            margin: 0 0 10px 0;
                            color: #999;
                        }
                        @media (max-width: 768px) {
                            .file-item {
                                flex-direction: column;
                                align-items: flex-start;
                            }
                            .file-info {
                                margin-bottom: 10px;
                            }
                            .controls {
                                flex-direction: column;
                                gap: 10px;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>📁 脑域蓝牙数据文件</h1>
                        </div>
                        <div class="controls">
                            <div class="file-count">共 ${files.size} 个文件</div>
                            <button class="refresh-btn" onclick="location.reload()">🔄 刷新</button>
                        </div>
                        <div class="file-list">
            """.trimIndent())
            
            if (files.isEmpty()) {
                append("""
                    <div class="empty-state">
                        <h3>📂 暂无文件</h3>
                        <p>请先进行数据采集，然后刷新页面查看文件</p>
                    </div>
                """.trimIndent())
            } else {
                files.forEach { (file, displayName) ->
                    val fileType = when (file.extension) {
                        "neeg" -> "neeg"
                        "txt" -> if (file.name.contains("_raw")) "raw" else "txt"
                        "json" -> "json"
                        "csv" -> "csv"
                        "log" -> "log"
                        else -> "unknown"
                    }
                    
                    val fileSize = formatFileSize(file.length())
                    val lastModified = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date(file.lastModified()))
                    
                    // 使用相对路径作为下载和删除的标识
                    val relativePath = if (displayName.startsWith("temp/")) {
                        "temp/${file.name}"
                    } else {
                        file.name
                    }
                    
                    append("""
                        <div class="file-item">
                            <div class="file-icon $fileType">${fileType.uppercase()}</div>
                            <div class="file-info">
                                <div class="file-name">$displayName</div>
                                <div class="file-details">大小: $fileSize | 修改时间: $lastModified</div>
                            </div>
                            <div class="file-actions">
                                <a href="/download/$relativePath" class="btn btn-download">📥 下载</a>
                                <a href="/delete/$relativePath" class="btn btn-delete" onclick="return confirm('确定要删除文件 $displayName 吗？')">🗑️ 删除</a>
                            </div>
                        </div>
                    """.trimIndent())
                }
            }
            
            append("""
                        </div>
                    </div>
                    <script>
                        // 自动刷新功能（每30秒）
                        setTimeout(function() {
                            location.reload();
                        }, 30000);
                    </script>
                </body>
                </html>
            """.trimIndent())
        }
        
        sendResponse(output, 200, "OK", "text/html; charset=utf-8", html.toByteArray())
    }
    
    /**
     * 下载文件
     */
    private fun downloadFile(fileName: String, output: OutputStream) {
        val file = if (fileName.startsWith("temp/")) {
            File(context.filesDir, "recordings/$fileName")
        } else {
            File(context.filesDir, "recordings/$fileName")
        }
        
        if (!file.exists() || !file.isFile) {
            sendErrorResponse(output, 404, "File Not Found")
            return
        }
        
        try {
            val contentType = when (file.extension) {
                "txt" -> "text/plain"
                "json" -> "application/json"
                "neeg" -> "application/octet-stream"
                else -> "application/octet-stream"
            }
            
            val headers = mutableMapOf<String, String>()
            headers["Content-Disposition"] = "attachment; filename=\"${file.name}\""
            
            sendResponse(output, 200, "OK", contentType, file.readBytes(), headers)
        } catch (e: Exception) {
            e.printStackTrace()
            sendErrorResponse(output, 500, "Internal Server Error")
        }
    }
    
    /**
     * 删除文件
     */
    private fun deleteFile(fileName: String, output: OutputStream) {
        val file = if (fileName.startsWith("temp/")) {
            File(context.filesDir, "recordings/$fileName")
        } else {
            File(context.filesDir, "recordings/$fileName")
        }
        
        if (!file.exists()) {
            sendErrorResponse(output, 404, "File Not Found")
            return
        }
        
        try {
            val success = file.delete()
            
            // 如果是neeg或txt文件，也删除对应的元数据文件
            if (success && (file.extension == "neeg" || file.extension == "txt")) {
                val metadataFile = File(context.filesDir, "recordings/metadata/${file.nameWithoutExtension}.json")
                if (metadataFile.exists()) {
                    metadataFile.delete()
                }
            }
            
            if (success) {
                // 重定向到首页
                sendRedirectResponse(output, "/")
            } else {
                sendErrorResponse(output, 500, "Failed to delete file")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            sendErrorResponse(output, 500, "Internal Server Error")
        }
    }
    
    /**
     * 发送HTTP响应
     */
    private fun sendResponse(
        output: OutputStream, 
        statusCode: Int, 
        statusText: String, 
        contentType: String, 
        content: ByteArray,
        additionalHeaders: Map<String, String> = emptyMap()
    ) {
        val writer = PrintWriter(output, true)
        
        writer.println("HTTP/1.1 $statusCode $statusText")
        writer.println("Content-Type: $contentType")
        writer.println("Content-Length: ${content.size}")
        writer.println("Connection: close")
        
        additionalHeaders.forEach { (key, value) ->
            writer.println("$key: $value")
        }
        
        writer.println() // 空行分隔头部和内容
        writer.flush()
        
        output.write(content)
        output.flush()
    }
    
    /**
     * 发送错误响应
     */
    private fun sendErrorResponse(output: OutputStream, statusCode: Int, statusText: String) {
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>$statusCode $statusText</title>
                <meta charset="UTF-8">
            </head>
            <body>
                <h1>$statusCode $statusText</h1>
                <p>请求处理失败</p>
            </body>
            </html>
        """.trimIndent()
        
        sendResponse(output, statusCode, statusText, "text/html; charset=utf-8", html.toByteArray())
    }
    
    /**
     * 发送重定向响应
     */
    private fun sendRedirectResponse(output: OutputStream, location: String) {
        val writer = PrintWriter(output, true)
        
        writer.println("HTTP/1.1 302 Found")
        writer.println("Location: $location")
        writer.println("Connection: close")
        writer.println()
        writer.flush()
    }
    
    /**
     * 格式化文件大小
     */
    private fun formatFileSize(bytes: Long): String {
        if (bytes < 1024) return "$bytes B"
        val kb = bytes / 1024.0
        if (kb < 1024) return "%.1f KB".format(kb)
        val mb = kb / 1024.0
        if (mb < 1024) return "%.1f MB".format(mb)
        val gb = mb / 1024.0
        return "%.1f GB".format(gb)
    }
}
