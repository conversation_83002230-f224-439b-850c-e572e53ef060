package com.brainwonderful.naoyu_bluetooth_android.utils

import android.content.Context
import android.content.res.Resources
import android.util.DisplayMetrics
import android.view.WindowManager
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.sqrt

/**
 * 屏幕信息数据类
 */
data class ScreenInfo(
    val widthPixels: Int,           // 屏幕宽度（像素）
    val heightPixels: Int,          // 屏幕高度（像素）
    val densityDpi: Int,            // 屏幕密度（DPI）
    val density: Float,             // 屏幕密度倍数
    val widthInches: Float,         // 屏幕宽度（英寸）
    val heightInches: Float,        // 屏幕高度（英寸）
    val diagonalInches: Float,      // 对角线长度（英寸）
    val widthMm: Float,             // 屏幕宽度（毫米）
    val heightMm: Float,            // 屏幕高度（毫米）
    val widthCm: Float,             // 屏幕宽度（厘米）
    val heightCm: Float,            // 屏幕高度（厘米）
    val xdpi: Float,                // X轴DPI
    val ydpi: Float                 // Y轴DPI
)

/**
 * 屏幕信息工具类
 */
object ScreenInfoUtil {
    
    /**
     * 获取屏幕信息
     */
    fun getScreenInfo(context: Context): ScreenInfo {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        
        val widthPixels = displayMetrics.widthPixels
        // 获取包含状态栏的完整屏幕高度
        val heightPixels = getRealScreenHeight(context)
        val densityDpi = displayMetrics.densityDpi
        val density = displayMetrics.density
        val xdpi = displayMetrics.xdpi
        val ydpi = displayMetrics.ydpi
        
        // 计算物理尺寸（英寸）
        val widthInches = widthPixels / xdpi
        val heightInches = heightPixels / ydpi
        val diagonalInches = sqrt(widthInches * widthInches + heightInches * heightInches)
        
        // 转换为毫米和厘米（1英寸 = 25.4毫米）
        val widthMm = widthInches * 25.4f
        val heightMm = heightInches * 25.4f
        val widthCm = widthMm / 10f
        val heightCm = heightMm / 10f
        
        return ScreenInfo(
            widthPixels = widthPixels,
            heightPixels = heightPixels,
            densityDpi = densityDpi,
            density = density,
            widthInches = widthInches,
            heightInches = heightInches,
            diagonalInches = diagonalInches,
            widthMm = widthMm,
            heightMm = heightMm,
            widthCm = widthCm,
            heightCm = heightCm,
            xdpi = xdpi,
            ydpi = ydpi
        )
    }
    
    /**
     * 像素转换为毫米
     */
    fun pixelsToMm(pixels: Float, dpi: Float): Float {
        return (pixels / dpi) * 25.4f
    }
    
    /**
     * 像素转换为厘米
     */
    fun pixelsToCm(pixels: Float, dpi: Float): Float {
        return pixelsToMm(pixels, dpi) / 10f
    }
    
    /**
     * 毫米转换为像素
     */
    fun mmToPixels(mm: Float, dpi: Float): Float {
        return (mm / 25.4f) * dpi
    }
    
    /**
     * 厘米转换为像素
     */
    fun cmToPixels(cm: Float, dpi: Float): Float {
        return mmToPixels(cm * 10f, dpi)
    }
    
    /**
     * 获取包含状态栏的真实屏幕高度
     */
    private fun getRealScreenHeight(context: Context): Int {
        val resources = context.resources
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        val statusBarHeight = if (resourceId > 0) resources.getDimensionPixelSize(resourceId) else 0
        
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        
        // 返回屏幕高度加上状态栏高度
        return displayMetrics.heightPixels + statusBarHeight
    }
}

/**
 * Compose中获取屏幕信息的扩展
 */
@Composable
fun rememberScreenInfo(): ScreenInfo {
    val context = LocalContext.current
    return ScreenInfoUtil.getScreenInfo(context)
}

/**
 * 物理单位转换扩展
 */
@Composable
fun Float.mmToDp(): Dp {
    val density = LocalDensity.current
    val context = LocalContext.current
    val screenInfo = ScreenInfoUtil.getScreenInfo(context)
    val pixels = ScreenInfoUtil.mmToPixels(this, screenInfo.xdpi)
    return with(density) { pixels.toDp() }
}

@Composable
fun Float.cmToDp(): Dp {
    return (this * 10f).mmToDp()
}

@Composable
fun Dp.toMm(): Float {
    val density = LocalDensity.current
    val context = LocalContext.current
    val screenInfo = ScreenInfoUtil.getScreenInfo(context)
    val pixels = with(density) { <EMAIL>() }
    return ScreenInfoUtil.pixelsToMm(pixels, screenInfo.xdpi)
}

@Composable
fun Dp.toCm(): Float {
    return toMm() / 10f
}