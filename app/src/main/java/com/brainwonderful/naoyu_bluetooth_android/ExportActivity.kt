package com.brainwonderful.naoyu_bluetooth_android

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.lifecycle.ViewModelProvider
import com.brainwonderful.naoyu_bluetooth_android.ui.screens.ExportScreen
import com.brainwonderful.naoyu_bluetooth_android.ui.theme.NaoyubluetoothandroidTheme
import com.brainwonderful.naoyu_bluetooth_android.viewmodel.ExportViewModel

class ExportActivity : ComponentActivity() {
    
    private lateinit var exportViewModel: ExportViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        exportViewModel = ViewModelProvider(this)[ExportViewModel::class.java]
        
        setContent {
            NaoyubluetoothandroidTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    ExportScreen(
                        viewModel = exportViewModel,
                        onBack = { finish() }
                    )
                }
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 退出页面时自动关闭服务器
        exportViewModel.stopServer()
    }
}
