package com.brainwonderful.naoyu_bluetooth_android.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.*
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.brainwonderful.naoyu_bluetooth_android.data.SpectrumData
import kotlin.math.*

/**
 * 高级实时频谱图表组件
 * 支持对数坐标显示，与UniApp版本保持一致
 */
@OptIn(ExperimentalTextApi::class)
@Composable
fun AdvancedSpectrumChart(
    spectrumData: SpectrumData,
    minFrequency: Double = 0.0,
    maxFrequency: Double = 100.0,
    channelName: String,
    fixedYAxisMax: Double? = null,
    isLogScale: Boolean = false,
    onLogScaleChange: (Boolean) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val textMeasurer = rememberTextMeasurer()
    
    Column(modifier = modifier) {
        // Control panel
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Log scale toggle
            Row(verticalAlignment = Alignment.CenterVertically) {
                Switch(
                    checked = isLogScale,
                    onCheckedChange = onLogScaleChange
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("对数坐标", fontSize = 12.sp)
            }
        }
        
        // Spectrum chart
        Box(modifier = Modifier.weight(1f)) {
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black)
            ) {
                val width = size.width
                val height = size.height
                
                // 边距
                val leftMargin = 80f
                val rightMargin = 40f
                val topMargin = 40f
                val bottomMargin = 60f
                
                // 绘图区域
                val plotWidth = width - leftMargin - rightMargin
                val plotHeight = height - topMargin - bottomMargin
                
                // 获取指定范围内的数据
                val (frequencies, magnitudes, _) = spectrumData.getDataInRange(minFrequency, maxFrequency)
                
                if (frequencies.isEmpty()) return@Canvas
                
                // 计算Y轴范围
                val processedMagnitudes = if (isLogScale) {
                    magnitudes.map { max(0.001, it) }.map { log10(it) }.toDoubleArray()
                } else {
                    magnitudes
                }
                
                val minMagnitude = if (isLogScale) {
                    processedMagnitudes.minOrNull() ?: -3.0
                } else {
                    0.0
                }
                
                val maxMagnitude = if (fixedYAxisMax != null) {
                    if (isLogScale) log10(max(0.001, fixedYAxisMax)) else fixedYAxisMax
                } else {
                    processedMagnitudes.maxOrNull() ?: 1.0
                }
                
                val magnitudeRange = if (maxMagnitude > minMagnitude) {
                    max(if (isLogScale) 0.1 else 1.0, maxMagnitude - minMagnitude)
                } else {
                    if (isLogScale) 0.1 else 1.0
                }
                
                // 绘制背景网格
                drawGrid(
                    leftMargin, topMargin, plotWidth, plotHeight,
                    minFrequency, maxFrequency, minMagnitude, maxMagnitude, isLogScale
                )
                
                // 绘制坐标轴
                drawAxes(
                    leftMargin, topMargin, plotWidth, plotHeight,
                    textMeasurer, minFrequency, maxFrequency, minMagnitude, maxMagnitude,
                    channelName, isLogScale
                )
                
                // 绘制频谱曲线
                drawSpectrum(
                    frequencies, processedMagnitudes,
                    leftMargin, topMargin, plotWidth, plotHeight,
                    minFrequency, maxFrequency, minMagnitude, magnitudeRange
                )
                
                // 绘制频带标记
                drawFrequencyBands(
                    leftMargin, topMargin, plotWidth, plotHeight,
                    minFrequency, maxFrequency, textMeasurer
                )
                
                // 显示主频率和频带功率
                val dominantFreqText = "主频率: %.1f Hz".format(spectrumData.dominantFrequency)
                val dominantTextResult = textMeasurer.measure(
                    dominantFreqText,
                    TextStyle(color = Color.Yellow, fontSize = 14.sp)
                )
                drawText(
                    textMeasurer = textMeasurer,
                    text = dominantFreqText,
                    topLeft = Offset(width - dominantTextResult.size.width - 20f, 20f),
                    style = TextStyle(color = Color.Yellow, fontSize = 14.sp)
                )
                
                // 显示频带幅值分布（使用幅值平方和）
                val bandMagnitudes = spectrumData.getBandMagnitudes()
                var yOffset = 50f
                bandMagnitudes.forEach { (band, magnitude) ->
                    val bandText = "$band: %.2f μV²".format(magnitude)
                    val bandTextResult = textMeasurer.measure(
                        bandText,
                        TextStyle(color = Color.White, fontSize = 12.sp)
                    )
                    drawText(
                        textMeasurer = textMeasurer,
                        text = bandText,
                        topLeft = Offset(width - bandTextResult.size.width - 20f, yOffset),
                        style = TextStyle(color = Color.White, fontSize = 12.sp)
                    )
                    yOffset += 20f
                }
                
            }
        }
    }
}

// 绘制网格（支持对数坐标）
private fun DrawScope.drawGrid(
    left: Float, top: Float, width: Float, height: Float,
    minFreq: Double, maxFreq: Double, minMagnitude: Double, maxMagnitude: Double,
    isLogScale: Boolean
) {
    val gridColor = Color.Gray.copy(alpha = 0.3f)
    val gridPath = Path()
    
    // 垂直网格线（频率）- 固定11条线对应刻度
    val tickCount = 11
    
    for (i in 0 until tickCount) {
        val x = left + (i.toFloat() / (tickCount - 1)) * width
        gridPath.moveTo(x, top)
        gridPath.lineTo(x, top + height)
    }
    
    // 水平网格线
    val gridLines = if (isLogScale) {
        // 对数坐标下的网格线
        val logRange = maxMagnitude - minMagnitude
        (0..5).map { i ->
            minMagnitude + (i / 5.0) * logRange
        }
    } else {
        // 线性坐标下的网格线
        (0..5).map { i ->
            minMagnitude + (i / 5.0) * (maxMagnitude - minMagnitude)
        }
    }
    
    gridLines.forEach { value ->
        val y = top + height - ((value - minMagnitude) / (maxMagnitude - minMagnitude) * height).toFloat()
        gridPath.moveTo(left, y)
        gridPath.lineTo(left + width, y)
    }
    
    drawPath(
        path = gridPath,
        color = gridColor,
        style = Stroke(width = 1f)
    )
}

// 绘制坐标轴（支持对数坐标）
@OptIn(ExperimentalTextApi::class)
private fun DrawScope.drawAxes(
    left: Float, top: Float, width: Float, height: Float,
    textMeasurer: TextMeasurer,
    minFreq: Double, maxFreq: Double, minMagnitude: Double, maxMagnitude: Double,
    channelName: String, isLogScale: Boolean
) {
    val axisColor = Color.White
    val axisPath = Path()
    
    // X轴
    axisPath.moveTo(left, top + height)
    axisPath.lineTo(left + width, top + height)
    
    // Y轴
    axisPath.moveTo(left, top)
    axisPath.lineTo(left, top + height)
    
    drawPath(
        path = axisPath,
        color = axisColor,
        style = Stroke(width = 2f)
    )
    
    // X轴标签（频率）- 固定11个刻度线
    val freqRange = maxFreq - minFreq
    val tickCount = 11 // 固定11个刻度线
    
    for (i in 0 until tickCount) {
        val freq = minFreq + (i * freqRange / (tickCount - 1))
        val x = left + (i.toFloat() / (tickCount - 1)) * width
        val labelText = if (freq < 1.0) {
            "%.1f".format(freq)
        } else {
            freq.toInt().toString()
        }
        val textResult = textMeasurer.measure(
            labelText,
            TextStyle(color = axisColor, fontSize = 12.sp)
        )
        drawText(
            textMeasurer = textMeasurer,
            text = labelText,
            topLeft = Offset(x - textResult.size.width / 2f, top + height + 5f),
            style = TextStyle(color = axisColor, fontSize = 12.sp)
        )
    }
    
    // X轴标题
    val xTitle = "频率 (Hz)"
    val xTitleResult = textMeasurer.measure(
        xTitle,
        TextStyle(color = axisColor, fontSize = 14.sp)
    )
    drawText(
        textMeasurer = textMeasurer,
        text = xTitle,
        topLeft = Offset(
            left + width / 2f - xTitleResult.size.width / 2f,
            top + height + 35f
        ),
        style = TextStyle(color = axisColor, fontSize = 14.sp)
    )
    
    // Y轴标签
    for (i in 0..5) {
        val y = top + height - (i / 5f) * height
        val magnitudeValue = minMagnitude + (i / 5.0) * (maxMagnitude - minMagnitude)
        
        val labelText = if (isLogScale) {
            val realValue = 10.0.pow(magnitudeValue)
            if (realValue < 1.0) {
                "%.3f".format(realValue)
            } else if (realValue < 10.0) {
                "%.1f".format(realValue)
            } else {
                realValue.toInt().toString()
            }
        } else {
            if (magnitudeValue < 10) {
                "%.1f".format(magnitudeValue)
            } else {
                magnitudeValue.toInt().toString()
            }
        }
        
        val textResult = textMeasurer.measure(
            labelText,
            TextStyle(color = axisColor, fontSize = 12.sp)
        )
        drawText(
            textMeasurer = textMeasurer,
            text = labelText,
            topLeft = Offset(left - textResult.size.width - 10f, y - textResult.size.height / 2f),
            style = TextStyle(color = axisColor, fontSize = 12.sp)
        )
    }
    
    // Y轴标题
    val yTitle = if (isLogScale) "幅值 (μV, 对数)" else "幅值 (μV)"
    val yTitleResult = textMeasurer.measure(
        yTitle,
        TextStyle(color = axisColor, fontSize = 14.sp)
    )
    drawText(
        textMeasurer = textMeasurer,
        text = yTitle,
        topLeft = Offset(20f, top + height / 2f + yTitleResult.size.width / 2f),
        style = TextStyle(color = axisColor, fontSize = 14.sp)
    )
    
    // 通道名称
    drawText(
        textMeasurer = textMeasurer,
        text = channelName,
        topLeft = Offset(left + 10f, top + 10f),
        style = TextStyle(color = Color.Cyan, fontSize = 16.sp)
    )
}

// 绘制频谱曲线（与原来相同）
private fun DrawScope.drawSpectrum(
    frequencies: DoubleArray,
    magnitudes: DoubleArray,
    left: Float, top: Float, width: Float, height: Float,
    minFreq: Double, maxFreq: Double, minMagnitude: Double, magnitudeRange: Double
) {
    if (frequencies.isEmpty()) return
    
    val spectrumPath = Path()
    val fillPath = Path()
    val freqRange = maxFreq - minFreq
    
    // 开始路径
    val firstX = left + ((frequencies[0] - minFreq) / freqRange).toFloat() * width
    val firstY = top + height - ((magnitudes[0] - minMagnitude) / magnitudeRange).toFloat() * height
    
    spectrumPath.moveTo(firstX, firstY)
    fillPath.moveTo(firstX, top + height)
    fillPath.lineTo(firstX, firstY)
    
    // 绘制曲线
    for (i in 1 until frequencies.size) {
        val x = left + ((frequencies[i] - minFreq) / freqRange).toFloat() * width
        val y = top + height - ((magnitudes[i] - minMagnitude) / magnitudeRange).toFloat() * height
        
        spectrumPath.lineTo(x, y)
        fillPath.lineTo(x, y)
    }
    
    // 完成填充路径
    if (frequencies.isNotEmpty()) {
        val lastX = left + ((frequencies.last() - minFreq) / freqRange).toFloat() * width
        fillPath.lineTo(lastX, top + height)
        fillPath.close()
    }
    
    // 绘制填充区域
    drawPath(
        path = fillPath,
        color = Color.Green.copy(alpha = 0.3f)
    )
    
    // 绘制曲线
    drawPath(
        path = spectrumPath,
        color = Color.Green,
        style = Stroke(width = 2f)
    )
}

// 绘制频带标记（与Python版本一致）
@OptIn(ExperimentalTextApi::class)
private fun DrawScope.drawFrequencyBands(
    left: Float, top: Float, width: Float, height: Float,
    minFreq: Double, maxFreq: Double, textMeasurer: TextMeasurer
) {
    val bands = listOf(
        Triple("δ", 1.0, 4.0),
        Triple("θ", 4.0, 8.0),
        Triple("α", 8.0, 13.0),
        Triple("β", 13.0, 30.0)
    )
    
    val bandColors = listOf(
        Color(0xFF9C27B0),  // Delta - 紫色
        Color(0xFF2196F3),  // Theta - 蓝色
        Color(0xFF4CAF50),  // Alpha - 绿色
        Color(0xFFFF9800)   // Beta - 橙色
    )
    
    val freqRange = maxFreq - minFreq
    
    bands.forEachIndexed { index, (name, startFreq, endFreq) ->
        // 只显示与当前频率范围有重叠的频带
        if (endFreq > minFreq && startFreq < maxFreq) {
            val visibleStartFreq = max(startFreq, minFreq)
            val visibleEndFreq = min(endFreq, maxFreq)
            
            val x1 = left + ((visibleStartFreq - minFreq) / freqRange).toFloat() * width
            val x2 = left + ((visibleEndFreq - minFreq) / freqRange).toFloat() * width
            
            // 绘制频带区域
            drawRect(
                color = bandColors[index].copy(alpha = 0.1f),
                topLeft = Offset(x1, top),
                size = androidx.compose.ui.geometry.Size(x2 - x1, height)
            )
            
            // 绘制频带标签
            val centerFreq = (visibleStartFreq + visibleEndFreq) / 2
            if (centerFreq in minFreq..maxFreq) {
                val centerX = left + ((centerFreq - minFreq) / freqRange).toFloat() * width
                val textResult = textMeasurer.measure(
                    name,
                    TextStyle(color = bandColors[index], fontSize = 14.sp)
                )
                drawText(
                    textMeasurer = textMeasurer,
                    text = name,
                    topLeft = Offset(centerX - textResult.size.width / 2f, top - 20f),
                    style = TextStyle(color = bandColors[index], fontSize = 14.sp)
                )
            }
        }
    }
}