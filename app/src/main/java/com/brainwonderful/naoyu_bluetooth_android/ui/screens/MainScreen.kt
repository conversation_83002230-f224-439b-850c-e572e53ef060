package com.brainwonderful.naoyu_bluetooth_android.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ConnectionState
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ConnectedDeviceInfo
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ScanState
import com.brainwonderful.naoyu_bluetooth_android.data.BluetoothDevice
import com.brainwonderful.naoyu_bluetooth_android.ui.components.DeviceItem
import com.brainwonderful.naoyu_bluetooth_android.viewmodel.BluetoothViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: BluetoothViewModel,
    onNavigateToCollect: () -> Unit,
    onNavigateToDebug: () -> Unit,
    onNavigateToExport: () -> Unit,
    onNavigateToPhysicalDesign: () -> Unit,
    onRequestPermissions: () -> Unit,
    onRequestEnableBluetooth: () -> Unit
) {
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE
    
    // 状态收集
    val scanState by viewModel.scanState.collectAsStateWithLifecycle()
    val connectionState by viewModel.connectionState.collectAsStateWithLifecycle()
    val discoveredDevices by viewModel.discoveredDevices.collectAsStateWithLifecycle()
    val connectedDevice by viewModel.connectedDevice.collectAsStateWithLifecycle()
    val bluetoothStatus by viewModel.bluetoothStatus.collectAsStateWithLifecycle()
    val logs by viewModel.logs.collectAsStateWithLifecycle()
    val receivedData by viewModel.receivedData.collectAsStateWithLifecycle()
    
    // UI状态
    var showRenameDialog by remember { mutableStateOf(false) }
    var showStatusDialog by remember { mutableStateOf(false) }
    
    // 扫描状态
    val isScanning = scanState == ScanState.SCANNING
    
    // 设备列表转换，确保连接状态正确显示
    val devices = discoveredDevices.map { device ->
        val isDeviceConnected = connectedDevice?.address == device.address && connectionState == ConnectionState.CONNECTED
        BluetoothDevice(
            id = device.address,
            name = device.name,
            address = device.address,
            isConnected = isDeviceConnected,
            rssi = device.rssi
        )
    }
    
    // 检查是否有已连接的设备
    val hasConnectedDevice = devices.any { it.isConnected } || 
                             (connectedDevice != null && connectionState == ConnectionState.CONNECTED)
    
    // 电量和信号显示
    val battery = receivedData?.battery ?: ""
    val signalQuality = receivedData?.signalQuality ?: 0
    
    // 调试日志：追踪信号状态更新
    LaunchedEffect(signalQuality, connectedDevice) {
        val currentConnectedDevice = connectedDevice // 创建局部副本避免智能转换问题
        if (currentConnectedDevice != null) {
            android.util.Log.d("MainScreen", "Signal quality updated: $signalQuality for device: ${currentConnectedDevice.name}")
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("脑域蓝牙测试") },
                actions = {
                    IconButton(onClick = onNavigateToPhysicalDesign) {
                        Icon(
                            imageVector = Icons.Default.Build,
                            contentDescription = "物理尺寸设计"
                        )
                    }
                    IconButton(onClick = onNavigateToExport) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "数据导出"
                        )
                    }
                    IconButton(onClick = onNavigateToDebug) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = "调试模式"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        if (isLandscape) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                DeviceListSection(
                    modifier = Modifier.weight(1f),
                    devices = devices,
                    isScanning = isScanning,
                    hasConnectedDevice = hasConnectedDevice,
                    connectedDevice = connectedDevice,
                    battery = battery,
                    signalQuality = signalQuality,
                    onStartScan = {
                        if (!viewModel.isBluetoothEnabled()) {
                            onRequestEnableBluetooth()
                        } else if (!viewModel.hasRequiredPermissions()) {
                            onRequestPermissions()
                        } else {
                            viewModel.startScan()
                        }
                    },
                    onStopScan = { viewModel.stopScan() },
                    onGetStatus = {
                        viewModel.getBluetoothStatus()
                        showStatusDialog = true
                    },
                    onRenameDevice = {
                        if (connectedDevice != null) {
                            showRenameDialog = true
                        }
                    },
                    onConnect = { device ->
                        val discoveredDevice = discoveredDevices.find { it.address == device.address }
                        discoveredDevice?.let { viewModel.connectDevice(it) }
                    },
                    onDisconnect = { viewModel.disconnect() },
                    onRename = { device ->
                        val discoveredDevice = discoveredDevices.find { it.address == device.address }
                        if (discoveredDevice != null && connectedDevice?.address == device.address) {
                            showRenameDialog = true
                        }
                    },
                    onNavigateToCollect = onNavigateToCollect
                )
                
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(1.dp)
                )
                
                LogSection(
                    modifier = Modifier.weight(1f),
                    logs = logs,
                    onClearLogs = { viewModel.clearLogs() }
                )
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                DeviceListSection(
                    modifier = Modifier.weight(1f),
                    devices = devices,
                    isScanning = isScanning,
                    hasConnectedDevice = hasConnectedDevice,
                    connectedDevice = connectedDevice,
                    battery = battery,
                    signalQuality = signalQuality,
                    onStartScan = {
                        if (!viewModel.isBluetoothEnabled()) {
                            onRequestEnableBluetooth()
                        } else if (!viewModel.hasRequiredPermissions()) {
                            onRequestPermissions()
                        } else {
                            viewModel.startScan()
                        }
                    },
                    onStopScan = { viewModel.stopScan() },
                    onGetStatus = {
                        viewModel.getBluetoothStatus()
                        showStatusDialog = true
                    },
                    onRenameDevice = {
                        if (connectedDevice != null) {
                            showRenameDialog = true
                        }
                    },
                    onConnect = { device ->
                        val discoveredDevice = discoveredDevices.find { it.address == device.address }
                        discoveredDevice?.let { viewModel.connectDevice(it) }
                    },
                    onDisconnect = { viewModel.disconnect() },
                    onRename = { device ->
                        val discoveredDevice = discoveredDevices.find { it.address == device.address }
                        if (discoveredDevice != null && connectedDevice?.address == device.address) {
                            showRenameDialog = true
                        }
                    },
                    onNavigateToCollect = onNavigateToCollect
                )
                
                HorizontalDivider()
                
                LogSection(
                    modifier = Modifier.weight(0.5f),
                    logs = logs,
                    onClearLogs = { viewModel.clearLogs() }
                )
            }
        }
    }
    
    // 重命名对话框
    connectedDevice?.let { device ->
        if (showRenameDialog) {
            RenameDialog(
                device = BluetoothDevice(
                    id = device.address,
                    name = device.name,
                    address = device.address,
                    isConnected = true,
                    rssi = 0 // ConnectedDeviceInfo 没有 rssi 信息，使用默认值
                ),
                onDismiss = { showRenameDialog = false },
                onConfirm = { newName ->
                    viewModel.renameDevice(newName)
                    showRenameDialog = false
                }
            )
        }
    }
    
    // 状态对话框
    if (showStatusDialog) {
        StatusDialog(
            bluetoothStatus = bluetoothStatus,
            onDismiss = { showStatusDialog = false }
        )
    }
}

@Composable
fun DeviceListSection(
    modifier: Modifier = Modifier,
    devices: List<BluetoothDevice>,
    isScanning: Boolean,
    hasConnectedDevice: Boolean,
    connectedDevice: ConnectedDeviceInfo?,
    battery: String,
    signalQuality: Int,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit,
    onGetStatus: () -> Unit,
    onRenameDevice: () -> Unit,
    onConnect: (BluetoothDevice) -> Unit,
    onDisconnect: (BluetoothDevice) -> Unit,
    onRename: (BluetoothDevice) -> Unit,
    onNavigateToCollect: () -> Unit
) {
    Column(modifier = modifier) {
        // 操作按钮区域
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "蓝牙操作",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 开始扫描按钮
                    ElevatedButton(
                        onClick = onStartScan,
                        modifier = Modifier.weight(1f),
                        enabled = !isScanning,
                        colors = ButtonDefaults.elevatedButtonColors(
                            containerColor = MaterialTheme.colorScheme.primary,
                            contentColor = MaterialTheme.colorScheme.onPrimary
                        )
                    ) {
                        Icon(
                            Icons.Default.Search,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("开始扫描")
                    }
                    
                    // 停止扫描按钮
                    ElevatedButton(
                        onClick = onStopScan,
                        modifier = Modifier.weight(1f),
                        enabled = isScanning,
                        colors = ButtonDefaults.elevatedButtonColors(
                            containerColor = MaterialTheme.colorScheme.error,
                            contentColor = if (isScanning) Color.White else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        )
                    ) {
                        Icon(
                            Icons.Default.Close,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("停止扫描")
                    }
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 获取状态按钮
                    OutlinedButton(
                        onClick = onGetStatus,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            Icons.Default.Info,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("获取状态")
                    }
                    
                    // 修改设备名称按钮
                    OutlinedButton(
                        onClick = onRenameDevice,
                        modifier = Modifier.weight(1f),
                        enabled = hasConnectedDevice
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("修改名称")
                    }
                }
            }
        }
        
        if (isScanning) {
            LinearProgressIndicator(
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        LazyColumn(
            modifier = Modifier.weight(1f),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            items(devices) { device ->
                DeviceItem(
                    device = device,
                    battery = if (device.isConnected && connectedDevice?.address == device.address) battery else "",
                    signalQuality = if (device.isConnected && connectedDevice?.address == device.address) signalQuality else null,
                    firmwareVersion = if (device.isConnected && connectedDevice?.address == device.address) connectedDevice?.firmwareVersion else null,
                    onConnect = { onConnect(device) },
                    onDisconnect = { onDisconnect(device) },
                    onRename = { onRename(device) }
                )
            }
        }
        
        Button(
            onClick = onNavigateToCollect,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            enabled = hasConnectedDevice
        ) {
            Text("进入数据采集")
        }
    }
}

@Composable
fun LogSection(
    modifier: Modifier = Modifier,
    logs: List<String>,
    onClearLogs: () -> Unit
) {
    Column(
        modifier = modifier.padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "日志",
                style = MaterialTheme.typography.titleMedium
            )
            TextButton(onClick = onClearLogs) {
                Text("清除")
            }
        }
        
        Card(
            modifier = Modifier.fillMaxSize(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp),
                reverseLayout = true
            ) {
                items(logs.reversed()) { log ->
                    Text(
                        text = log,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(vertical = 2.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun RenameDialog(
    device: BluetoothDevice,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var newName by remember { mutableStateOf(device.name) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("重命名设备") },
        text = {
            OutlinedTextField(
                value = newName,
                onValueChange = { newName = it },
                label = { Text("设备名称") },
                singleLine = true
            )
        },
        confirmButton = {
            TextButton(onClick = { onConfirm(newName) }) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
fun StatusDialog(
    bluetoothStatus: com.brainwonderful.naoyu_bluetooth_android.bluetooth.BluetoothStatus,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Info,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("蓝牙状态")
            }
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                StatusItem("蓝牙状态", if (bluetoothStatus.isEnabled) "已启用" else "未启用", true)
                StatusItem("适配器名称", bluetoothStatus.adapterName.ifEmpty { "未知" }, true)
                StatusItem("MAC地址", bluetoothStatus.adapterAddress.ifEmpty { "未知" }, true)
                StatusItem("可发现", if (bluetoothStatus.isDiscovering) "是" else "否", true)
                StatusItem("已配对设备", "${bluetoothStatus.bondedDeviceCount} 个", false)
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

@Composable
fun StatusItem(
    label: String,
    value: String,
    showDivider: Boolean
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
        if (showDivider) {
            HorizontalDivider(
                modifier = Modifier.padding(vertical = 4.dp),
                color = MaterialTheme.colorScheme.outlineVariant
            )
        }
    }
}