package com.brainwonderful.naoyu_bluetooth_android.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import kotlin.math.PI
import java.util.Locale

@Composable
fun FilterSettingsPanel(
    modifier: Modifier = Modifier,
    onSettingsChanged: (FilterSettings) -> Unit = {}
) {
    var notchEnabled by remember { mutableStateOf(true) }
    var bandpassEnabled by remember { mutableStateOf(false) }
    var lowFreq by remember { mutableStateOf(1f) }
    var highFreq by remember { mutableStateOf(35f) }
    
    // 时间常数相关状态 - 按照UniApp版本实现
    var timeConstantEnabled by remember { mutableStateOf(false) }
    var timeConstantValue by remember { mutableStateOf(2.0) } // 默认值与UniApp一致
    
    // 时间常数可选值 - 与UniApp版本完全一致
    val timeConstantOptions = listOf(
        10.0, 5.0, 2.0, 1.0, 0.6, 0.3, 0.1, 0.03, 0.003, 0.001
    )
    var timeConstantExpanded by remember { mutableStateOf(false) }
    
    Card(modifier = modifier) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "滤波设置",
                style = MaterialTheme.typography.titleMedium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("陷波滤波 (50Hz)")
                Switch(
                    checked = notchEnabled,
                    onCheckedChange = { 
                        notchEnabled = it
                        onSettingsChanged(
                            FilterSettings(
                                notchEnabled = notchEnabled,
                                bandpassEnabled = bandpassEnabled,
                                lowFreq = lowFreq,
                                highFreq = highFreq,
                                timeConstantEnabled = timeConstantEnabled,
                                timeConstantValue = timeConstantValue
                            )
                        )
                    }
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("带通滤波")
                Switch(
                    checked = bandpassEnabled,
                    onCheckedChange = { 
                        bandpassEnabled = it
                        onSettingsChanged(
                            FilterSettings(
                                notchEnabled = notchEnabled,
                                bandpassEnabled = bandpassEnabled,
                                lowFreq = lowFreq,
                                highFreq = highFreq,
                                timeConstantEnabled = timeConstantEnabled,
                                timeConstantValue = timeConstantValue
                            )
                        )
                    }
                )
            }
            
            if (bandpassEnabled) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = lowFreq.toString(),
                        onValueChange = { 
                            lowFreq = it.toFloatOrNull() ?: lowFreq
                            onSettingsChanged(
                                FilterSettings(
                                    notchEnabled = notchEnabled,
                                    bandpassEnabled = bandpassEnabled,
                                    lowFreq = lowFreq,
                                    highFreq = highFreq,
                                    timeConstantEnabled = timeConstantEnabled,
                                    timeConstantValue = timeConstantValue
                                )
                            )
                        },
                        label = { Text("低频 (Hz)") },
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                    OutlinedTextField(
                        value = highFreq.toString(),
                        onValueChange = { 
                            highFreq = it.toFloatOrNull() ?: highFreq
                            onSettingsChanged(
                                FilterSettings(
                                    notchEnabled = notchEnabled,
                                    bandpassEnabled = bandpassEnabled,
                                    lowFreq = lowFreq,
                                    highFreq = highFreq,
                                    timeConstantEnabled = timeConstantEnabled,
                                    timeConstantValue = timeConstantValue
                                )
                            )
                        },
                        label = { Text("高频 (Hz)") },
                        modifier = Modifier.weight(1f),
                        singleLine = true
                    )
                }
            }
            
            // 时间常数设置 - 按照UniApp版本实现
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("时间常数")
                Switch(
                    checked = timeConstantEnabled,
                    onCheckedChange = { 
                        timeConstantEnabled = it
                        onSettingsChanged(
                            FilterSettings(
                                notchEnabled = notchEnabled,
                                bandpassEnabled = bandpassEnabled,
                                lowFreq = lowFreq,
                                highFreq = highFreq,
                                timeConstantEnabled = timeConstantEnabled,
                                timeConstantValue = timeConstantValue
                            )
                        )
                    }
                )
            }
            
            if (timeConstantEnabled) {
                // 旲间常数值选择下拉框 - 使用稳定的DropdownMenu API
                Box(modifier = Modifier.fillMaxWidth()) {
                    OutlinedTextField(
                        value = "${timeConstantValue}s",
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("时间常数 (s)") },
                        trailingIcon = {
                            IconButton(onClick = { timeConstantExpanded = !timeConstantExpanded }) {
                                Icon(
                                    imageVector = if (timeConstantExpanded) {
                                        Icons.Default.KeyboardArrowUp
                                    } else {
                                        Icons.Default.KeyboardArrowDown
                                    },
                                    contentDescription = "Dropdown"
                                )
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    )
                    DropdownMenu(
                        expanded = timeConstantExpanded,
                        onDismissRequest = { timeConstantExpanded = false },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        timeConstantOptions.forEach { option ->
                            DropdownMenuItem(
                                text = { 
                                    Column {
                                        Text("${option}s")
                                        Text(
                                            text = "截止频率: ${String.format(Locale.US, "%.3f", 1.0 / (2.0 * PI * option))}Hz",
                                            style = MaterialTheme.typography.bodySmall,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                },
                                onClick = {
                                    timeConstantValue = option
                                    timeConstantExpanded = false
                                    onSettingsChanged(
                                        FilterSettings(
                                            notchEnabled = notchEnabled,
                                            bandpassEnabled = bandpassEnabled,
                                            lowFreq = lowFreq,
                                            highFreq = highFreq,
                                            timeConstantEnabled = timeConstantEnabled,
                                            timeConstantValue = timeConstantValue
                                        )
                                    )
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

data class FilterSettings(
    val notchEnabled: Boolean,
    val bandpassEnabled: Boolean,
    val lowFreq: Float,
    val highFreq: Float,
    val timeConstantEnabled: Boolean = false,
    val timeConstantValue: Double = 2.0
) {
    /**
     * 计算时间常数对应的截止频率 - 按照UniApp版本的公式
     * 公式: f = 1 / (2 * π * τ)
     */
    fun getTimeConstantCutoffFreq(): Double {
        return if (timeConstantEnabled) {
            1.0 / (2.0 * PI * timeConstantValue)
        } else {
            0.0
        }
    }
}