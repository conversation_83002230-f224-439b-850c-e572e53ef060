package com.brainwonderful.naoyu_bluetooth_android.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import kotlin.math.*
import java.util.Locale
import com.brainwonderful.naoyu_bluetooth_android.config.EEGProcessingConfig
import com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfoUtil
import com.brainwonderful.naoyu_bluetooth_android.utils.rememberScreenInfo
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.wrapContentHeight

/**
 * 双通道脑电波形图组件 - 上下布局，连续滚动显示
 * 基于UniApp版本的参数和交互方式
 */
@Composable
fun DualChannelWaveformChart(
    modifier: Modifier = Modifier,
    af7Data: List<Double> = emptyList(),
    af8Data: List<Double> = emptyList(),
    af7RawData: List<Double> = emptyList(),
    af8RawData: List<Double> = emptyList(),
    paperSpeed: String = "30mm/s", // 走纸速度
    sensitivity: String = "2uv/mm", // 灵敏度 - 保留以保持API兼容性
    isCollecting: Boolean = false,
    showRawData: Boolean = false,
    sampleRate: Double = 250.0 // 采样率，支持250Hz和500Hz
) {
    val af7Color = Color(0xFF2196F3) // 蓝色 - AF7 滤波后
    val af8Color = Color(0xFFE91E63) // 粉色 - AF8 滤波后
    val af7RawColor = Color(0xFF4CAF50) // 绿色 - AF7 原始数据
    val af8RawColor = Color(0xFFFF9800) // 橙色 - AF8 原始数据
    
    // 解析参数
    val speedValue = parseSpeedValue(paperSpeed)
    val sensitivityValue = parseSensitivityValue(sensitivity)
    
    // 采样率和时间窗口设置 - 使用配置参数
    val sampleRateInt = sampleRate.toInt()
    val timeWindowSeconds = EEGProcessingConfig.BufferConfig.PAPER_SPEED_TIME_WINDOWS[paperSpeed] 
        ?: EEGProcessingConfig.BufferConfig.DEFAULT_DISPLAY_TIME_WINDOW
    val windowSize = sampleRateInt * timeWindowSeconds // 时间窗口内的数据点数
    
    // 时间窗口缓冲区 - 固定大小的滑动窗口
    var af7WindowBuffer by remember { mutableStateOf(mutableListOf<Double>()) }
    var af8WindowBuffer by remember { mutableStateOf(mutableListOf<Double>()) }
    var af7RawWindowBuffer by remember { mutableStateOf(mutableListOf<Double>()) }
    var af8RawWindowBuffer by remember { mutableStateOf(mutableListOf<Double>()) }
    var currentPosition by remember { mutableIntStateOf(0) } // 当前写入位置
    var windowFilled by remember { mutableStateOf(false) } // 窗口是否已填满
    
    val renderFps = EEGProcessingConfig.DisplayConfig.WAVEFORM_RENDER_FPS // 使用配置的渲染帧率
    
    // 当窗口大小改变时，重新初始化缓冲区
    LaunchedEffect(windowSize) {
        af7WindowBuffer = MutableList(windowSize) { 0.0 }
        af8WindowBuffer = MutableList(windowSize) { 0.0 }
        af7RawWindowBuffer = MutableList(windowSize) { 0.0 }
        af8RawWindowBuffer = MutableList(windowSize) { 0.0 }
        currentPosition = 0
        windowFilled = false
    }
    
    // 数据更新 - 写入时间窗口缓冲区
    LaunchedEffect(af7Data, af8Data, af7RawData, af8RawData) {
        if (af7Data.isNotEmpty() || af8Data.isNotEmpty() || af7RawData.isNotEmpty() || af8RawData.isNotEmpty()) {
            // 检查缓冲区是否需要初始化
            if (af7WindowBuffer.size != windowSize) {
                af7WindowBuffer = MutableList(windowSize) { 0.0 }
                af8WindowBuffer = MutableList(windowSize) { 0.0 }
                af7RawWindowBuffer = MutableList(windowSize) { 0.0 }
                af8RawWindowBuffer = MutableList(windowSize) { 0.0 }
                currentPosition = 0
                windowFilled = false
            }
            
            // 同步写入AF7和AF8数据
            val maxSize = maxOf(af7Data.size, af8Data.size, af7RawData.size, af8RawData.size)
            for (i in 0 until maxSize) {
                if (i < af7Data.size && currentPosition < af7WindowBuffer.size) {
                    af7WindowBuffer[currentPosition] = af7Data[i]
                }
                if (i < af8Data.size && currentPosition < af8WindowBuffer.size) {
                    af8WindowBuffer[currentPosition] = af8Data[i]
                }
                if (i < af7RawData.size && currentPosition < af7RawWindowBuffer.size) {
                    af7RawWindowBuffer[currentPosition] = af7RawData[i]
                }
                if (i < af8RawData.size && currentPosition < af8RawWindowBuffer.size) {
                    af8RawWindowBuffer[currentPosition] = af8RawData[i]
                }
                currentPosition = (currentPosition + 1) % windowSize
                if (currentPosition == 0) windowFilled = true
            }
        }
    }
    
    // 渲染刷新
    var renderTrigger by remember { mutableIntStateOf(0) }
    LaunchedEffect(isCollecting) {
        while (isCollecting) {
            delay(1000L / renderFps) // 30fps固定刷新
            renderTrigger++
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // AF7 通道 (上)
        TimeWindowWaveformChannel(
            title = "AF7",
            windowBuffer = af7WindowBuffer,
            rawWindowBuffer = if (showRawData) af7RawWindowBuffer else emptyList(),
            currentPosition = currentPosition,
            windowSize = windowSize,
            windowFilled = windowFilled,
            color = af7Color,
            rawColor = af7RawColor,
            sensitivity = sensitivityValue,
            paperSpeed = paperSpeed,
            renderTrigger = renderTrigger,
            showRawData = showRawData,
            sampleRate = sampleRate,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )
        
        // 分隔线
        HorizontalDivider(
            color = Color.Gray.copy(alpha = 0.3f),
            thickness = 1.dp
        )
        
        // AF8 通道 (下)
        TimeWindowWaveformChannel(
            title = "AF8",
            windowBuffer = af8WindowBuffer,
            rawWindowBuffer = if (showRawData) af8RawWindowBuffer else emptyList(),
            currentPosition = currentPosition,
            windowSize = windowSize,
            windowFilled = windowFilled,
            color = af8Color,
            rawColor = af8RawColor,
            sensitivity = sensitivityValue,
            paperSpeed = paperSpeed,
            renderTrigger = renderTrigger,
            showRawData = showRawData,
            sampleRate = sampleRate,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )
    }
}

@Composable
fun TimeWindowWaveformChannel(
    modifier: Modifier = Modifier,
    title: String,
    windowBuffer: List<Double>,
    rawWindowBuffer: List<Double> = emptyList(),
    currentPosition: Int,
    windowSize: Int,
    windowFilled: Boolean,
    color: Color,
    rawColor: Color = Color.Gray,
    sensitivity: Double,
    paperSpeed: String,
    renderTrigger: Int,
    showRawData: Boolean = false,
    sampleRate: Double = 250.0
) {
    Column(modifier = modifier) {
        // 通道标题
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = color,
                fontSize = 14.sp
            )
            
            // 数据类型图例
            Spacer(modifier = Modifier.width(8.dp))
            when {
                showRawData && rawWindowBuffer.isNotEmpty() && windowBuffer.isNotEmpty() -> {
                    // 对比模式 - 显示两种数据
                    Text(
                        text = "滤波",
                        style = MaterialTheme.typography.bodySmall,
                        color = color,
                        fontSize = 10.sp
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "原始",
                        style = MaterialTheme.typography.bodySmall,
                        color = rawColor,
                        fontSize = 10.sp
                    )
                }
                showRawData && rawWindowBuffer.isNotEmpty() && windowBuffer.isEmpty() -> {
                    // 仅原始数据模式
                    Text(
                        text = "原始数据",
                        style = MaterialTheme.typography.bodySmall,
                        color = rawColor,
                        fontSize = 10.sp
                    )
                }
//                !showRawData && windowBuffer.isNotEmpty() -> {
//                    // 仅滤波数据模式
//                    Text(
//                        text = "滤波数据",
//                        style = MaterialTheme.typography.bodySmall,
//                        color = color,
//                        fontSize = 10.sp
//                    )
//                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
            // 显示动态幅值范围
            Text(
                text = "±${(sensitivity * 20).toInt()}μV",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray,
                fontSize = 12.sp
            )
        }
        
        // 波形绘制区域 - 包含底部时间坐标轴
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 8.dp, vertical = 2.dp)
        ) {
            // 主绘制区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                // 波形绘制区域
                val screenInfo = rememberScreenInfo()
                val currentSampleRate = sampleRate // 捕获采样率变量供Canvas内部使用
                Canvas(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.02f))
                ) {
                    drawWaveformGrid(paperSpeed, screenInfo, sensitivity)
                    
                    // 绘制原始数据 (如果启用且有数据)
                    if (showRawData && rawWindowBuffer.isNotEmpty()) {
                        drawTimeWindowWaveform(
                            windowBuffer = rawWindowBuffer,
                            currentPosition = currentPosition,
                            windowSize = windowSize,
                            windowFilled = windowFilled,
                            color = if (windowBuffer.isNotEmpty()) rawColor.copy(alpha = 0.7f) else rawColor, // 对比模式时透明，单独模式时不透明
                            sensitivity = sensitivity,
                            paperSpeed = paperSpeed,
                            screenInfo = screenInfo,
                            sampleRate = currentSampleRate
                        )
                    }

                    // 绘制滤波后数据
                    if (windowBuffer.isNotEmpty()) {
                        drawTimeWindowWaveform(
                            windowBuffer = windowBuffer,
                            currentPosition = currentPosition,
                            windowSize = windowSize,
                            windowFilled = windowFilled,
                            color = if (showRawData && rawWindowBuffer.isNotEmpty()) color else color, // 保持不透明以确保可见性
                            sensitivity = sensitivity,
                            paperSpeed = paperSpeed,
                            screenInfo = screenInfo,
                            sampleRate = currentSampleRate
                        )
                    }
                }
                
                // Y轴刻度标签 - 覆盖在图形左侧
                YAxisLabels(
                    sensitivity = sensitivity,
                    modifier = Modifier
                        .width(45.dp)
                        .fillMaxHeight()
                        .align(Alignment.CenterStart)
                )
            }
            
            // 底部时间坐标轴
            PhysicalDimensionLabels(
                paperSpeed = paperSpeed,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(24.dp)
            )
        }
    }
}

/**
 * 绘制网格和Y轴标签 - 竖线与时间标记对齐，Y轴标签与网格线精确对齐
 */
private fun DrawScope.drawWaveformGrid(paperSpeed: String, screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo, sensitivity: Double) {
    val gridColor = Color.Gray.copy(alpha = 0.3f)
    val width = size.width
    val height = size.height
    
    // 水平网格线 (幅值刻度)
    val gridSteps = 8
    val stepHeight = height / gridSteps
    
    // 计算电压范围和刻度值
    val voltageRange = sensitivity * 20 // μV 总范围
    val maxValue = voltageRange.toInt()
    val minValue = -maxValue
    
    for (i in 0..gridSteps) {
        val y = i * stepHeight
        val isCenter = i == gridSteps / 2
        
        // 绘制网格线
        drawLine(
            color = gridColor,
            start = Offset(0f, y),
            end = Offset(width, y),
            strokeWidth = if (isCenter) EEGProcessingConfig.DisplayConfig.GRID_LINE_WIDTH * 2 
                         else EEGProcessingConfig.DisplayConfig.GRID_LINE_WIDTH
        )
        
        // 绘制Y轴标签
        val ratio = i.toDouble() / gridSteps.toDouble()
        val value = (maxValue - (ratio * (maxValue - minValue))).toInt()
        
        // 根据电压范围决定是否显示标签
        val shouldShow = when {
            value == 0 -> true // 总是显示0点
            maxValue <= 40 -> value % 10 == 0    // 小范围：每10μV显示
            maxValue <= 100 -> value % 20 == 0   // 中范围：每20μV显示
            maxValue <= 200 -> value % 50 == 0   // 大范围：每50μV显示
            maxValue <= 1000 -> value % 100 == 0 // 很大范围：每100μV显示
            else -> value % 200 == 0             // 极大范围：每200μV显示
        }
        
        if (shouldShow) {
            val text = when {
                value == 0 -> "0"
                value > 0 -> "+$value"
                else -> "$value"
            }
            
            // 使用Canvas的drawText API绘制文本（注意：这里需要使用原生Canvas API）
            // 由于Compose Canvas不支持文本绘制，我们保留覆盖式的Y轴标签组件
        }
    }
    
    // 垂直网格线 (时间刻度) - 与时间标记对齐
    drawVerticalGridLines(paperSpeed, width, height, gridColor, screenInfo)
}

/**
 * 绘制垂直网格线 - 与底部时间标记位置完全对齐
 */
private fun DrawScope.drawVerticalGridLines(
    paperSpeed: String,
    width: Float,
    height: Float,
    gridColor: Color,
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo
) {
    // 解析走纸速度，获取每秒对应的毫米数（与PhysicalDimensionLabels保持一致）
    val mmPerSecond = when (paperSpeed) {
        "15mm/s" -> 15.0
        "30mm/s" -> 30.0
        "60mm/s" -> 60.0
        else -> 30.0
    }
    
    // Y轴标签宽度(45dp转换为像素) - 与PhysicalDimensionLabels的padding保持一致
    val yAxisWidthDp = 45f
    val yAxisWidthPx = yAxisWidthDp * screenInfo.density
    
    // 计算有效绘制区域宽度（排除Y轴标签区域）
    val effectiveWidth = width - yAxisWidthPx
    val physicalWidthMm = ScreenInfoUtil.pixelsToMm(effectiveWidth, screenInfo.xdpi)
    
    // 计算能显示的理论最大秒数
    val theoreticalMaxSeconds = physicalWidthMm / mmPerSecond
    val maxSeconds = kotlin.math.ceil(theoreticalMaxSeconds).toInt()
    
    // 为每个整数秒时间绘制竖线，起始位置从Y轴标签右侧开始
    for (seconds in 0..maxSeconds) {
        // 计算当前时间对应的物理距离（毫米）
        val physicalDistanceMm = seconds * mmPerSecond
        
        // 计算标记在有效绘制区域中的位置比例
        val positionFraction = physicalDistanceMm / physicalWidthMm
        
        // 只在容器范围内绘制竖线
        if (positionFraction <= 0.98) {
            // 计算实际X坐标（Y轴标签宽度 + 有效区域内的偏移）
            val x = yAxisWidthPx + effectiveWidth * positionFraction.toFloat()
            
            drawLine(
                color = gridColor,
                start = Offset(x, 0f),
                end = Offset(x, height),
                strokeWidth = EEGProcessingConfig.DisplayConfig.GRID_LINE_WIDTH
            )
        }
    }
}

/**
 * 绘制时间窗口波形 - 循环缓冲区实现，从右到左滚动，限制在网格区域内
 * 完全匹配走纸速度和灵敏度设置
 */
private fun DrawScope.drawTimeWindowWaveform(
    windowBuffer: List<Double>,
    currentPosition: Int,
    windowSize: Int,
    windowFilled: Boolean,
    color: Color,
    sensitivity: Double,
    paperSpeed: String,
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo,
    sampleRate: Double
) {
    if (windowBuffer.size < 2) return

    // Y轴标签宽度(45dp转换为像素) - 与绘制网格线的计算保持一致
    val yAxisWidthDp = 45f
    val yAxisWidthPx = yAxisWidthDp * screenInfo.density

    // 计算有效绘制区域（排除Y轴标签区域）
    val totalWidth = size.width
    val totalHeight = size.height
    val effectiveWidth = totalWidth - yAxisWidthPx // 有效绘制宽度
    val effectiveHeight = totalHeight // 高度保持不变

    val center = effectiveHeight / 2

    // 灵敏度计算：与Y轴刻度完全匹配
    // Y轴显示范围是 ±(sensitivity * 20)，8个网格步长，每步长对应 (sensitivity * 20) / 4 μV
    val voltageRange = sensitivity * 20 // μV 总范围（±范围）
    val pixelsPerMicroVolt = effectiveHeight / (2 * voltageRange) // 每微伏对应的像素

    // 走纸速度计算：确保与时间轴刻度完全匹配
    val mmPerSecond = when (paperSpeed) {
        "15mm/s" -> 15.0
        "30mm/s" -> 30.0
        "60mm/s" -> 60.0
        else -> 30.0
    }

    // 计算有效绘制区域的物理宽度（毫米）
    val physicalWidthMm = com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfoUtil.pixelsToMm(effectiveWidth, screenInfo.xdpi)

    // 根据走纸速度计算该物理宽度对应的时间（秒）
    val physicalTimeWindowSeconds = physicalWidthMm / mmPerSecond

    // 使用配置的时间窗口，确保与缓冲区大小一致
    val configTimeWindowSeconds = EEGProcessingConfig.BufferConfig.PAPER_SPEED_TIME_WINDOWS[paperSpeed]
        ?: EEGProcessingConfig.BufferConfig.DEFAULT_DISPLAY_TIME_WINDOW

    // 计算应该显示的数据点数：基于物理时间窗口和采样率
    val physicalPointsToDisplay = (physicalTimeWindowSeconds * sampleRate).toInt()

    // 限制显示的数据点数不超过可用数据和缓冲区大小
    val displaySize = if (windowFilled) windowSize else currentPosition
    val maxAvailablePoints = minOf(displaySize, windowSize)

    // 使用物理计算的点数，但不超过可用数据
    val effectiveDisplaySize = minOf(physicalPointsToDisplay, maxAvailablePoints)

    if (effectiveDisplaySize < 2) return

    // 计算每个数据点对应的像素宽度 - 基于实际要显示的点数
    val pixelSpacing = effectiveWidth / effectiveDisplaySize.toFloat()

    // 调试信息：验证计算是否正确
    if (effectiveDisplaySize > 0) {
        val packetTimeInterval = 40.0 / sampleRate // 每个数据包的时间间隔
        val expectedPacketsPerSecond = sampleRate / 40.0 // 每秒预期数据包数

        android.util.Log.d("WaveformChart",
            "=== 波形显示计算 ===" +
            "\n走纸速度: $paperSpeed" +
            "\n采样率: ${sampleRate}Hz" +
            "\n每包数据点: 40" +
            "\n每包时间间隔: ${String.format("%.3f", packetTimeInterval)}s" +
            "\n每秒数据包数: ${String.format("%.1f", expectedPacketsPerSecond)}" +
            "\n物理宽度: ${String.format("%.1f", physicalWidthMm)}mm" +
            "\n物理时间窗口: ${String.format("%.2f", physicalTimeWindowSeconds)}s" +
            "\n配置时间窗口: ${configTimeWindowSeconds}s" +
            "\n物理计算点数: $physicalPointsToDisplay" +
            "\n实际显示点数: $effectiveDisplaySize" +
            "\n缓冲区大小: $windowSize" +
            "\n可用数据: $displaySize" +
            "\n像素间距: ${String.format("%.2f", pixelSpacing)}px/点"
        )
    }
    
    // 创建路径
    val path = Path()
    var pathStarted = false
    
    // 从右到左绘制，最新数据在右边
    for (i in 0 until effectiveDisplaySize) {
        // 计算实际的缓冲区索引 - 从最新数据开始往前取
        val bufferIndex = if (windowFilled) {
            // 窗口已满，从当前位置向前读取最近的effectiveDisplaySize个点
            (currentPosition - effectiveDisplaySize + i + windowSize) % windowSize
        } else {
            // 窗口未满，取最近的effectiveDisplaySize个点
            if (currentPosition >= effectiveDisplaySize) {
                currentPosition - effectiveDisplaySize + i
            } else {
                maxOf(0, currentPosition - effectiveDisplaySize + i)
            }
        }
        
        // 边界检查，防止越界
        if (bufferIndex < 0 || bufferIndex >= windowBuffer.size) {
            continue
        }
        
        val value = windowBuffer[bufferIndex]
        val clampedValue = value.coerceIn(-voltageRange, voltageRange)
        
        // 计算坐标，最新数据在右边 - X坐标从Y轴标签右侧开始
        val x = yAxisWidthPx + effectiveWidth - (effectiveDisplaySize - i) * pixelSpacing
        val y = center - (clampedValue * pixelsPerMicroVolt).toFloat()
        
        // 限制绘制范围：X轴在有效区域内，Y轴在网格范围内
        if (x >= yAxisWidthPx && x <= totalWidth && y >= 0 && y <= effectiveHeight) {
            if (!pathStarted) {
                path.moveTo(x, y)
                pathStarted = true
            } else {
                path.lineTo(x, y)
            }
        }
    }
    
    // 绘制路径
    if (pathStarted) {
        drawPath(
            path = path,
            color = color,
            style = Stroke(
                width = EEGProcessingConfig.DisplayConfig.WAVEFORM_STROKE_WIDTH,
                cap = StrokeCap.Round,
                join = StrokeJoin.Round
            )
        )
    }
}

/**
 * 时间标签组件 - 显示在脑电图底部
 * 根据走纸速度显示正确的时间间隔
 */
@Composable
fun TimeLabels(
    paperSpeed: String,
    modifier: Modifier = Modifier
) {
    // 根据走纸速度确定时间窗口和标签数量
    val timeWindowSeconds = when (paperSpeed) {
        "15mm/s" -> 8  // 8秒窗口
        "30mm/s" -> 4  // 4秒窗口
        "60mm/s" -> 2  // 2秒窗口
        else -> 4
    }
    
    // 根据走纸速度确定时间标签数量（对应垂直网格线）
    val timeGridSteps = when (paperSpeed) {
        "15mm/s" -> 16 // 16条竖线，0.5秒间隔
        "30mm/s" -> 8  // 8条竖线，0.5秒间隔
        "60mm/s" -> 4  // 4条竖线，0.5秒间隔
        else -> 8
    }
    
    // 计算时间间隔
    val timeInterval = timeWindowSeconds.toDouble() / timeGridSteps.toDouble()
    
    BoxWithConstraints(
        modifier = modifier
            .padding(start = 50.dp) // 对齐波形显示区域（排除Y轴标签宽度）
            .background(Color.White)
    ) {
        val containerWidth = maxWidth
        
        // 时间标签精确对应垂直网格线位置
        for (i in 0..timeGridSteps) {
            val timeValue = i * timeInterval
            
            // 只显示主要的时间标签，避免过于密集
            val shouldShow = when (paperSpeed) {
                "15mm/s" -> i % 4 == 0  // 每2秒显示一个标签
                "30mm/s" -> i % 2 == 0  // 每1秒显示一个标签
                "60mm/s" -> true        // 每0.5秒显示一个标签
                else -> i % 2 == 0
            }
            
            if (shouldShow) {
                // 计算标签的绝对位置（对应垂直网格线）
                val xOffsetFraction = i.toFloat() / timeGridSteps.toFloat()
                val xOffset = containerWidth * xOffsetFraction
                
                Box(
                    modifier = Modifier
                        .offset(x = xOffset - 15.dp) // 减去标签宽度的一半，使其居中对齐网格线
                        .width(30.dp)
                        .wrapContentHeight(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = if (timeValue == timeValue.toInt().toDouble()) {
                            "${timeValue.toInt()}s"
                        } else {
                            String.format(Locale.getDefault(), "%.1fs", timeValue)
                        },
                        style = MaterialTheme.typography.bodySmall,
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * 物理尺寸标签组件 - 按整数秒时间间隔，基于走纸速度计算物理位置
 */
@Composable
fun PhysicalDimensionLabels(
    paperSpeed: String,
    modifier: Modifier = Modifier
) {
    val screenInfo = rememberScreenInfo()
    
    // 解析走纸速度，获取每秒对应的毫米数
    val mmPerSecond = when (paperSpeed) {
        "15mm/s" -> 15.0
        "30mm/s" -> 30.0
        "60mm/s" -> 60.0
        else -> 30.0
    }
    
    BoxWithConstraints(
        modifier = modifier
            .padding(start = 45.dp) // 与Y轴标签宽度保持一致
            .background(Color.White.copy(alpha = 0.9f))
    ) {
        val containerWidth = maxWidth
        val containerWidthPx = with(LocalDensity.current) { containerWidth.toPx() }
        
        // 计算容器的物理宽度（毫米）
        val physicalWidthMm = ScreenInfoUtil.pixelsToMm(containerWidthPx, screenInfo.xdpi)
        
        // 计算能显示的理论最大秒数，然后向上取整以确保充分利用显示空间
        val theoreticalMaxSeconds = physicalWidthMm / mmPerSecond
        val maxSeconds = kotlin.math.ceil(theoreticalMaxSeconds).toInt()
        
        // 从0秒开始，按整数秒间隔添加时间标记
        for (seconds in 0..maxSeconds) {
            // 计算当前时间对应的物理距离（毫米）
            val physicalDistanceMm = seconds * mmPerSecond
            
            // 计算标记在容器中的位置比例
            val positionFraction = physicalDistanceMm / physicalWidthMm
            
            // 只在容器范围内显示标记（留一点边距，避免标记被截断，但放宽限制）
            if (positionFraction <= 0.98) {
                val xOffset = containerWidth * positionFraction.toFloat()
                
                Box(
                    modifier = Modifier
                        .offset(x = xOffset - 15.dp) // 居中对齐
                        .width(30.dp)
                        .wrapContentHeight(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "${seconds}s",
                        style = MaterialTheme.typography.bodySmall,
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * Y轴刻度标签组件 - 使用绝对定位确保与网格线精确对齐
 */
@Composable
fun YAxisLabels(
    sensitivity: Double,
    modifier: Modifier = Modifier
) {
    val voltageRange = sensitivity * 20 // μV 总范围
    val maxValue = voltageRange.toInt()
    val minValue = -maxValue
    
    // 网格线配置
    val gridSteps = 8 // 网格线数量
    
    BoxWithConstraints(
        modifier = modifier
            .background(Color.White.copy(alpha = 0.8f)) // 半透明背景确保文字可读
    ) {
        val containerHeight = maxHeight
        val containerHeightPx = with(LocalDensity.current) { containerHeight.toPx() }
        val stepHeight = containerHeightPx / gridSteps
        
        // 固定显示9个刻度值：上4个、中心0、下4个（对应8条网格线的分界点）
        for (i in 0..gridSteps) {
            // 计算标签对应的电压值
            val ratio = i.toDouble() / gridSteps.toDouble()
            val value = (maxValue - (ratio * (maxValue - minValue))).toInt()
            
            // 固定显示所有9个刻度点（不再进行筛选）
            val shouldShow = true
            
            if (shouldShow) {
                // 计算标签的精确Y位置（对应网格线）
                val yOffset = with(LocalDensity.current) { (i * stepHeight).toDp() }
                
                Box(
                    modifier = Modifier
                        .offset(x = 0.dp, y = yOffset - 8.dp) // 减去文字高度的一半以居中对齐
                        .fillMaxWidth()
                        .height(16.dp),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    Text(
                        text = when {
                            value == 0 -> "0"
                            value > 0 -> "+$value"
                            else -> "$value"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        fontSize = 9.sp,
                        color = if (value == 0) Color.Black else Color.Gray,
                        fontWeight = if (value == 0) FontWeight.Bold else FontWeight.Normal,
                        modifier = Modifier.padding(end = 2.dp)
                    )
                }
            }
        }
    }
}

/**
 * 解析走纸速度值
 */
private fun parseSpeedValue(paperSpeed: String): Float {
    return when (paperSpeed) {
        "15mm/s" -> 0.5f
        "30mm/s" -> 1.0f
        "60mm/s" -> 2.0f
        else -> 1.0f
    }
}

/**
 * 解析灵敏度值
 */
private fun parseSensitivityValue(sensitivity: String): Double {
    return try {
        sensitivity.replace("uv/mm", "").replace("μV/mm", "").toDouble()
    } catch (e: Exception) {
        2.0 // 默认值
    }
}

