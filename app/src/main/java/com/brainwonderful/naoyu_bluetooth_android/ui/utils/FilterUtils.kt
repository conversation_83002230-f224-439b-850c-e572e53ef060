package com.brainwonderful.naoyu_bluetooth_android.ui.utils

/**
 * 滤波器可用性状态数据类
 */
data class FilterAvailability(
    val bandpassAvailable: Boolean = true,
    val lowPassAvailable: Boolean = true,
    val highPassAvailable: Boolean = true,
    val notchAvailable: Boolean = true,      // 50Hz陷波始终可用
    val timeConstantAvailable: Boolean = true // 时间常数始终可用
)

/**
 * 计算滤波器可用性状态
 * 主滤波器组（带通/低通/高通）互斥，只能选择其中一个
 * 50Hz陷波和时间常数独立，可与任何其他滤波器组合
 */
fun calculateFilterAvailability(
    bandpassEnabled: Boolean,
    lowPassEnabled: Boolean,
    highPassEnabled: Boolean
): FilterAvailability {
    // 计算当前已启用的主滤波器数量
    val enabledMainFilters = listOf(bandpassEnabled, lowPassEnabled, highPassEnabled).count { it }
    
    return when {
        // 没有主滤波器启用时，所有选项都可用
        enabledMainFilters == 0 -> FilterAvailability()
        
        // 只有一个主滤波器启用时，其他主滤波器不可用
        enabledMainFilters == 1 -> FilterAvailability(
            bandpassAvailable = !lowPassEnabled && !highPassEnabled,
            lowPassAvailable = !bandpassEnabled && !highPassEnabled,
            highPassAvailable = !bandpassEnabled && !lowPassEnabled
        )
        
        // 多个主滤波器同时启用时（异常情况），按优先级只允许一个
        else -> {
            when {
                bandpassEnabled -> FilterAvailability(
                    bandpassAvailable = true,
                    lowPassAvailable = false,
                    highPassAvailable = false
                )
                lowPassEnabled -> FilterAvailability(
                    bandpassAvailable = false,
                    lowPassAvailable = true,
                    highPassAvailable = false
                )
                else -> FilterAvailability(
                    bandpassAvailable = false,
                    lowPassAvailable = false,
                    highPassAvailable = true
                )
            }
        }
    }
}