package com.brainwonderful.naoyu_bluetooth_android.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.brainwonderful.naoyu_bluetooth_android.data.SpectrumData

/**
 * 全屏频域分析对话框
 * 显示实时频谱分析结果
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FrequencyAnalysisDialog(
    onDismiss: () -> Unit,
    af7SpectrumData: SpectrumData?,
    af8SpectrumData: SpectrumData?,
    isCollecting: Boolean,
    onStartAnalysis: () -> Unit,
    onStopAnalysis: () -> Unit
) {
    var selectedChannel by remember { mutableStateOf("AF7") }
    var xAxisMin by remember { mutableStateOf("0") } // 默认起始频率为0Hz
    var xAxisMax by remember { mutableStateOf("100") } // 默认结束频率为100Hz
    var xAxisMinError by remember { mutableStateOf(false) }
    var xAxisMaxError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    
    // Y轴显示方式相关状态
    var yAxisAutoScale by remember { mutableStateOf(false) } // 默认固定范围
    var yAxisFixedMax by remember { mutableStateOf("100") } // 固定Y轴最大值，默认100
    var yAxisFixedMaxError by remember { mutableStateOf(false) }
    
    // 验证频率范围的函数
    fun validateFrequencyRange() {
        val minVal = xAxisMin.toDoubleOrNull()
        val maxVal = xAxisMax.toDoubleOrNull()
        
        when {
            minVal == null && xAxisMin.isNotEmpty() -> {
                xAxisMinError = true
                errorMessage = "起始频率格式不正确"
            }
            maxVal == null && xAxisMax.isNotEmpty() -> {
                xAxisMaxError = true
                errorMessage = "结束频率格式不正确"
            }
            minVal != null && minVal < 0 -> {
                xAxisMinError = true
                errorMessage = "起始频率不能小于0Hz"
            }
            maxVal != null && maxVal > 125 -> { // 采样率250Hz，奈奎斯特频率为125Hz
                xAxisMaxError = true
                errorMessage = "结束频率不能大于125Hz（采样率限制）"
            }
            minVal != null && maxVal != null && minVal >= maxVal -> {
                xAxisMinError = true
                xAxisMaxError = true
                errorMessage = "起始频率必须小于结束频率"
            }
            else -> {
                xAxisMinError = false
                xAxisMaxError = false
                errorMessage = ""
            }
        }
    }
    
    // 验证Y轴固定值的函数
    fun validateYAxisFixedMax() {
        val maxVal = yAxisFixedMax.toDoubleOrNull()
        
        when {
            maxVal == null && yAxisFixedMax.isNotEmpty() -> {
                yAxisFixedMaxError = true
                errorMessage = "Y轴最大值格式不正确"
            }
            maxVal != null && maxVal <= 0 -> {
                yAxisFixedMaxError = true
                errorMessage = "Y轴最大值必须大于0"
            }
            else -> {
                yAxisFixedMaxError = false
                if (xAxisMinError || xAxisMaxError) {
                    // 保持其他错误信息
                } else {
                    errorMessage = ""
                }
            }
        }
    }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false // 允许全屏
        )
    ) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 顶部工具栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "实时频域图分析",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            modifier = Modifier.size(28.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 控制区域
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        // 通道选择
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectableGroup(),
                            horizontalArrangement = Arrangement.Start,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "选择通道：",
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.width(100.dp)
                            )
                            
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(32.dp)
                            ) {
                                Row(
                                    modifier = Modifier
                                        .selectable(
                                            selected = (selectedChannel == "AF7"),
                                            onClick = { selectedChannel = "AF7" },
                                            role = Role.RadioButton
                                        ),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    RadioButton(
                                        selected = (selectedChannel == "AF7"),
                                        onClick = null
                                    )
                                    Text(
                                        text = "AF7",
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }
                                
                                Row(
                                    modifier = Modifier
                                        .selectable(
                                            selected = (selectedChannel == "AF8"),
                                            onClick = { selectedChannel = "AF8" },
                                            role = Role.RadioButton
                                        ),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    RadioButton(
                                        selected = (selectedChannel == "AF8"),
                                        onClick = null
                                    )
                                    Text(
                                        text = "AF8",
                                        style = MaterialTheme.typography.bodyLarge,
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Y轴显示方式设置
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "Y轴显示方式：",
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.width(100.dp)
                            )
                            
                            Row(
                                modifier = Modifier
                                    .selectable(
                                        selected = !yAxisAutoScale,
                                        onClick = { yAxisAutoScale = false },
                                        role = Role.RadioButton
                                    ),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                RadioButton(
                                    selected = !yAxisAutoScale,
                                    onClick = null
                                )
                                Text(
                                    text = "固定范围",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(start = 4.dp)
                                )
                            }
                            
                            // 固定Y轴最大值输入框
                            if (!yAxisAutoScale) {
                                Text(
                                    text = "最大值：",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(start = 16.dp)
                                )
                                
                                OutlinedTextField(
                                    value = yAxisFixedMax,
                                    onValueChange = { value ->
                                        // 只允许输入数字和小数点
                                        if (value.isEmpty() || value.matches(Regex("^\\d*\\.?\\d*$"))) {
                                            yAxisFixedMax = value
                                            validateYAxisFixedMax()
                                        }
                                    },
                                    modifier = Modifier.width(100.dp),
                                    singleLine = true,
                                    isError = yAxisFixedMaxError,
                                    placeholder = { Text("100") }
                                )
                                
                                Text(
                                    text = "μV",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(start = 4.dp)
                                )
                            }
                            
                            Row(
                                modifier = Modifier
                                    .selectable(
                                        selected = yAxisAutoScale,
                                        onClick = { yAxisAutoScale = true },
                                        role = Role.RadioButton
                                    )
                                    .padding(start = 16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                RadioButton(
                                    selected = yAxisAutoScale,
                                    onClick = null
                                )
                                Text(
                                    text = "自动缩放",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(start = 4.dp)
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // 横坐标范围设置
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "横坐标范围：",
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.width(100.dp)
                            )
                            
                            // 起始频率输入
                            OutlinedTextField(
                                value = xAxisMin,
                                onValueChange = { value ->
                                    // 只允许输入数字和小数点
                                    if (value.isEmpty() || value.matches(Regex("^\\d*\\.?\\d*$"))) {
                                        xAxisMin = value
                                        validateFrequencyRange()
                                    }
                                },
                                modifier = Modifier.width(80.dp),
                                singleLine = true,
                                isError = xAxisMinError,
                                placeholder = { Text("0") }
                            )
                            
                            Text(" - ")
                            
                            // 结束频率输入
                            OutlinedTextField(
                                value = xAxisMax,
                                onValueChange = { value ->
                                    // 只允许输入数字和小数点
                                    if (value.isEmpty() || value.matches(Regex("^\\d*\\.?\\d*$"))) {
                                        xAxisMax = value
                                        validateFrequencyRange()
                                    }
                                },
                                modifier = Modifier.width(80.dp),
                                singleLine = true,
                                isError = xAxisMaxError,
                                placeholder = { Text("100") }
                            )
                            
                            Text(" Hz")
                            
                            Spacer(modifier = Modifier.width(32.dp))
                            
                            // 开始/停止按钮
                            Button(
                                onClick = {
                                    if (isCollecting) {
                                        onStopAnalysis()
                                    } else {
                                        onStartAnalysis()
                                    }
                                },
                                enabled = !xAxisMinError && !xAxisMaxError && !yAxisFixedMaxError,
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = if (isCollecting) {
                                        MaterialTheme.colorScheme.error
                                    } else {
                                        MaterialTheme.colorScheme.primary
                                    }
                                )
                            ) {
                                Text(
                                    text = if (isCollecting) "停止分析" else "开始分析"
                                )
                            }
                        }
                        
                        // 错误提示信息
                        if (errorMessage.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = errorMessage,
                                color = MaterialTheme.colorScheme.error,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 频谱图显示区域
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                    ) {
                        when {
                            !isCollecting -> {
                                // 未开始分析时的提示
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "点击\"开始分析\"按钮查看实时频谱",
                                        style = MaterialTheme.typography.bodyLarge,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                            else -> {
                                // 实时频谱图
                                val currentSpectrum = if (selectedChannel == "AF7") {
                                    af7SpectrumData
                                } else {
                                    af8SpectrumData
                                }
                                
                                if (currentSpectrum != null) {
                                    val minFreq = xAxisMin.toDoubleOrNull() ?: 0.0
                                    val maxFreq = xAxisMax.toDoubleOrNull() ?: 100.0
                                    val fixedYMax = if (!yAxisAutoScale) {
                                        yAxisFixedMax.toDoubleOrNull() ?: 100.0
                                    } else null
                                    
                                    RealtimeSpectrumChart(
                                        spectrumData = currentSpectrum,
                                        minFrequency = minFreq,
                                        maxFrequency = maxFreq,
                                        channelName = selectedChannel,
                                        fixedYAxisMax = fixedYMax,
                                        modifier = Modifier.fillMaxSize()
                                    )
                                } else {
                                    // 等待数据
                                    Box(
                                        modifier = Modifier.fillMaxSize(),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Column(
                                            horizontalAlignment = Alignment.CenterHorizontally,
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            CircularProgressIndicator()
                                            Text(
                                                text = "正在积累FFT数据...",
                                                style = MaterialTheme.typography.bodyMedium
                                            )
                                            Text(
                                                text = "需要约1-2秒的数据积累",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 底部信息栏
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "实时频谱分析",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        
                        if (isCollecting && (af7SpectrumData != null || af8SpectrumData != null)) {
                            val spectrum = if (selectedChannel == "AF7") af7SpectrumData else af8SpectrumData
                            spectrum?.let {
                                val updateTime = remember(it.timestamp) {
                                    val sdf = java.text.SimpleDateFormat("HH:mm:ss.SSS", java.util.Locale.getDefault())
                                    sdf.format(java.util.Date(it.timestamp))
                                }
                                Column {
                                    Text(
                                        text = "更新时间: $updateTime | FFT点数: ${it.fftSize} | 采样率: ${it.sampleRate.toInt()}Hz",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    Text(
                                        text = "使用采集时的滤波参数进行频谱分析",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}