package com.brainwonderful.naoyu_bluetooth_android.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.brainwonderful.naoyu_bluetooth_android.data.BluetoothDevice

@Composable
fun DeviceItem(
    device: BluetoothDevice,
    battery: String = "",
    signalQuality: Int? = null,
    firmwareVersion: String? = null,
    onConnect: () -> Unit,
    onDisconnect: () -> Unit,
    onRename: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp)
            .clickable { 
                if (device.isConnected) onDisconnect() else onConnect() 
            },
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (device.isConnected) 6.dp else 2.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = if (device.isConnected) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.name,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = device.address,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = "信号强度: ${device.rssi}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                if (device.isConnected) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            signalQuality?.let {
                                Text(
                                    text = if (it == 0) "信号: 良好" else "信号: 脱落",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = if (it == 0) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                                )
                            }
                            
                            if (battery.isNotEmpty()) {
                                Text(
                                    text = "电量: $battery",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        
                        firmwareVersion?.let {
                            Text(
                                text = "固件版本: $it",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                if (device.isConnected) {
                    TextButton(onClick = onRename) {
                        Text("重命名")
                    }
                    Button(
                        onClick = onDisconnect,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("断开")
                    }
                } else {
                    Button(onClick = onConnect) {
                        Text("连接")
                    }
                }
            }
        }
    }
}