package com.brainwonderful.naoyu_bluetooth_android.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.horizontalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.material3.MenuAnchorType
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import java.util.Locale
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.brainwonderful.naoyu_bluetooth_android.ui.components.DualChannelWaveformChart
import com.brainwonderful.naoyu_bluetooth_android.ui.components.FrequencyAnalysisDialog
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ConnectionState
import com.brainwonderful.naoyu_bluetooth_android.utils.formatDuration
import com.brainwonderful.naoyu_bluetooth_android.utils.formatFileSize
import com.brainwonderful.naoyu_bluetooth_android.ui.utils.FilterAvailability
import com.brainwonderful.naoyu_bluetooth_android.ui.utils.calculateFilterAvailability


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CollectScreen(
    viewModel: com.brainwonderful.naoyu_bluetooth_android.viewmodel.CollectViewModel,
    onBack: () -> Unit
) {
    // 从ViewModel获取状态
    val connectionState by viewModel.connectionState.collectAsStateWithLifecycle()
    val connectedDevice by viewModel.connectedDevice.collectAsStateWithLifecycle()
    val isCollecting by viewModel.isCollecting.collectAsStateWithLifecycle()
    val af7DisplayData by viewModel.af7DisplayData.collectAsStateWithLifecycle()
    val af8DisplayData by viewModel.af8DisplayData.collectAsStateWithLifecycle()
    val signalQuality by viewModel.signalQuality.collectAsStateWithLifecycle()
    val showSaveDialog by viewModel.showSaveDialog.collectAsStateWithLifecycle()
    val isRecording by viewModel.isRecording.collectAsStateWithLifecycle()
    val isRawRecording by viewModel.isRawRecording.collectAsStateWithLifecycle()
    val recordingStats by viewModel.recordingStats.collectAsStateWithLifecycle()
    val rawRecordingStats by viewModel.rawRecordingStats.collectAsStateWithLifecycle()
    val deviceType by viewModel.deviceType.collectAsStateWithLifecycle()
    
    // 频谱分析相关状态
    val isAnalyzingSpectrum by viewModel.isAnalyzingSpectrum.collectAsStateWithLifecycle()
    val af7SpectrumData by viewModel.af7SpectrumData.collectAsStateWithLifecycle()
    val af8SpectrumData by viewModel.af8SpectrumData.collectAsStateWithLifecycle()
    
    // 本地UI状态
    var elapsedTime by remember { mutableLongStateOf(0L) }
    var recordingTime by remember { mutableLongStateOf(0L) } // 录制时长
    var showStopDialog by remember { mutableStateOf(false) }
    var saveFileName by remember { mutableStateOf("") }
    var showFreqAnalysisDialog by remember { mutableStateOf(false) }
    var showDebugResultDialog by remember { mutableStateOf(false) }
    var showExitConfirmDialog by remember { mutableStateOf(false) }
    var pendingExit by remember { mutableStateOf(false) } // 标记是否需要在保存后退出
    
    // Filter states - 按照UniApp版本的默认值
    var paperSpeed by remember { mutableStateOf("30mm/s") } // 默认30mm/s (对应8格)
    var sensitivity by remember { mutableStateOf("5uv/mm") } // 默认5μV/mm
    var notch50Hz by remember { mutableStateOf(true) }       // 默认开启50Hz陷波
    var bandpassEnabled by remember { mutableStateOf(true) } // 默认开启带通滤波
    var bandpassStart by remember { mutableStateOf("1") }    // 默认1Hz
    var bandpassEnd by remember { mutableStateOf("35") }     // 默认35Hz
    var timeConstantEnabled by remember { mutableStateOf(false) } // 默认关闭时间常数
    var timeConstant by remember { mutableStateOf("0.03") }  // 优化默认值提供更好的信号响应
    var lowPassEnabled by remember { mutableStateOf(false) } // 默认关闭低通滤波
    var lowPassFreq by remember { mutableStateOf("35") }
    var highPassEnabled by remember { mutableStateOf(false) } // 默认关闭高通滤波
    var highPassFreq by remember { mutableStateOf("0.5") }
    
    // 动态计算滤波器可用性状态
    val filterAvailability by remember {
        derivedStateOf {
            calculateFilterAvailability(
                bandpassEnabled = bandpassEnabled,
                lowPassEnabled = lowPassEnabled,
                highPassEnabled = highPassEnabled
            )
        }
    }
    
    // 更新滤波器设置 - 优化后的简洁处理逻辑
    LaunchedEffect(notch50Hz, bandpassEnabled, bandpassStart, bandpassEnd, 
                   timeConstantEnabled, timeConstant, lowPassEnabled, lowPassFreq, 
                   highPassEnabled, highPassFreq) {
        // 50Hz陷波 - 独立处理，始终可用
        viewModel.toggleNotchFilter(notch50Hz)
        
        // 时间常数滤波 - 独立处理（高通特性），始终可用
        if (timeConstantEnabled) {
            val tcValue = timeConstant.toDoubleOrNull() ?: 0.03
            viewModel.updateTimeConstant(true, tcValue)
        } else {
            viewModel.updateTimeConstant(false, 0.03)
        }
        
        // 主滤波器设置 - 由于UI层已确保互斥，这里只需要简单处理
        when {
            bandpassEnabled -> {
                val startFreq = bandpassStart.toDoubleOrNull() ?: 1.0
                val endFreq = bandpassEnd.toDoubleOrNull() ?: 35.0
                viewModel.updateBandpassFilter(true, startFreq, endFreq)
            }
            lowPassEnabled -> {
                val freq = lowPassFreq.toDoubleOrNull() ?: 35.0
                viewModel.updateLowPassFilter(true, freq)
            }
            highPassEnabled -> {
                val freq = highPassFreq.toDoubleOrNull() ?: 0.5
                viewModel.updateHighPassFilter(true, freq)
            }
            // 所有主滤波器都禁用
            else -> {
                viewModel.updateBandpassFilter(false, 1.0, 35.0)
            }
        }
    }
    
    // Timer effect - 采集时长
    LaunchedEffect(isCollecting) {
        if (isCollecting) {
            while (isCollecting) {
                delay(1000)
                elapsedTime++
            }
        } else {
            elapsedTime = 0
        }
    }
    
    // Recording timer effect - 录制时长
    LaunchedEffect(isRecording) {
        if (isRecording) {
            while (isRecording) {
                delay(1000)
                recordingTime++
            }
        } else {
            recordingTime = 0
        }
    }
    
    Scaffold { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color(0xFFF5F5F5))
        ) {
            // Top control bar
            TopControlBar(
                onBack = {
                    // 如果正在采集，弹窗确认退出
                    if (isCollecting) {
                        showExitConfirmDialog = true
                    } else {
                        onBack()
                    }
                },
                deviceName = connectedDevice?.name ?: "未连接",
                isConnected = connectionState == ConnectionState.CONNECTED,
                elapsedTime = elapsedTime,
                recordingTime = recordingTime,
                signalQuality = when (signalQuality) {
                    true -> "良好"
                    false -> "脱落"
                    null -> if (connectionState == ConnectionState.CONNECTED) "等待信号" else "未连接"
                },
                isCollecting = isCollecting,
                sampleRate = deviceType.sampleRate.toInt(),
                onToggleCollect = {
                    if (isCollecting) {
                        showStopDialog = true
                    } else {
                        viewModel.startCollection()
                    }
                },
                isRecording = isRecording,
                isRawRecording = isRawRecording,
                onToggleRecord = {
                    if (isRecording || isRawRecording) {
                        viewModel.stopRecording()
                    } else {
                        viewModel.startRecording()
                    }
                },
                onFreqAnalysis = { showFreqAnalysisDialog = true },
                onDebugResult = { showDebugResultDialog = true }
            )
            
            // Filter controls
            FilterControls(
                paperSpeed = paperSpeed,
                onPaperSpeedChange = { paperSpeed = it },
                sensitivity = sensitivity,
                onSensitivityChange = { sensitivity = it },
                notch50Hz = notch50Hz,
                onNotch50HzChange = { notch50Hz = it },
                bandpassEnabled = bandpassEnabled,
                onBandpassEnabledChange = { bandpassEnabled = it },
                bandpassStart = bandpassStart,
                onBandpassStartChange = { bandpassStart = it },
                bandpassEnd = bandpassEnd,
                onBandpassEndChange = { bandpassEnd = it },
                timeConstantEnabled = timeConstantEnabled,
                onTimeConstantEnabledChange = { timeConstantEnabled = it },
                timeConstant = timeConstant,
                onTimeConstantChange = { timeConstant = it },
                lowPassEnabled = lowPassEnabled,
                onLowPassEnabledChange = { lowPassEnabled = it },
                lowPassFreq = lowPassFreq,
                onLowPassFreqChange = { lowPassFreq = it },
                highPassEnabled = highPassEnabled,
                onHighPassEnabledChange = { highPassEnabled = it },
                highPassFreq = highPassFreq,
                onHighPassFreqChange = { highPassFreq = it },
                filterAvailability = filterAvailability
            )
            
            // Waveform display - 双通道上下布局
            when {
                connectionState != ConnectionState.CONNECTED -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = null,
                                modifier = Modifier.size(48.dp),
                                tint = MaterialTheme.colorScheme.error
                            )
                            Text(
                                text = "请先连接蓝牙设备",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
                !isCollecting -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "当前为接入脑电信号页面，请点击\"开始\"进行采集",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                else -> {
                    DualChannelWaveformChart(
                        af7Data = af7DisplayData,
                        af8Data = af8DisplayData,
                        paperSpeed = paperSpeed,
                        sensitivity = sensitivity,
                        isCollecting = isCollecting,
                        sampleRate = deviceType.sampleRate,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(8.dp)
                    )
                }
            }
        }
    }
    
    // Dialogs
    if (showStopDialog) {
        AlertDialog(
            onDismissRequest = { showStopDialog = false },
            title = { Text("停止采集") },
            text = { 
                Column {
                    Text("确定要停止采集吗？")
                    if (isRecording) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "正在保存数据，停止采集将同时停止保存并显示保存对话框。",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.stopCollection()
                        showStopDialog = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showStopDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
    
    if (showFreqAnalysisDialog) {
        FrequencyAnalysisDialog(
            onDismiss = { 
                showFreqAnalysisDialog = false
                viewModel.stopSpectrumAnalysis()
            },
            af7SpectrumData = af7SpectrumData,
            af8SpectrumData = af8SpectrumData,
            isCollecting = isAnalyzingSpectrum,
            onStartAnalysis = { viewModel.startSpectrumAnalysis() },
            onStopAnalysis = { viewModel.stopSpectrumAnalysis() }
        )
    }
    
    if (showDebugResultDialog) {
        DebugResultDialog(
            onDismiss = { showDebugResultDialog = false }
        )
    }
    
    // Exit confirmation dialog - 退出确认对话框
    if (showExitConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showExitConfirmDialog = false },
            title = { Text("退出确认") },
            text = { 
                Column {
                    Text("正在采集数据，确定要退出吗？")
                    if (isRecording) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "正在保存数据，退出将停止采集和保存。",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showExitConfirmDialog = false
                        // 如果正在录制，需要特殊处理
                        if (isRecording) {
                            // 设置标记，表示保存后需要退出
                            pendingExit = true
                            // 停止采集，这会触发保存对话框
                            viewModel.stopCollection()
                        } else {
                            // 如果没有录制，直接停止采集并退出
                            viewModel.stopCollection()
                            onBack()
                        }
                    }
                ) {
                    Text("确定退出")
                }
            },
            dismissButton = {
                TextButton(onClick = { showExitConfirmDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
    
    // Save recording dialog
    if (showSaveDialog) {
        var selectedFormat by remember { mutableStateOf("txt") }
        
        AlertDialog(
            onDismissRequest = { /* 不允许点击外部关闭 */ },
            title = { Text("保存录制数据") },
            text = {
                Column {
                    // 处理后数据统计
                    Text(
                        text = "处理后数据统计",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "录制时长: ${formatDuration(recordingStats.duration)}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "数据包数: ${recordingStats.packetCount}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = "文件大小: ${formatFileSize(recordingStats.fileSize)}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    // 原始数据统计（如果有记录的话）
                    if (isRawRecording) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "原始数据包统计",
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "数据包数: ${rawRecordingStats.packetCount}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "文件大小: ${formatFileSize(rawRecordingStats.fileSize)}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                    OutlinedTextField(
                        value = saveFileName,
                        onValueChange = { saveFileName = it },
                        label = { Text("文件名") },
                        placeholder = { Text("输入文件名以保存数据") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 保存格式选择
                    Text(
                        text = "保存格式",
                        style = MaterialTheme.typography.labelMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    // 处理后数据格式选项
                    Column {
                        // NEEG格式选项
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(vertical = 4.dp)
                        ) {
                            RadioButton(
                                selected = selectedFormat == "neeg",
                                onClick = { selectedFormat = "neeg" }
                            )
                            Column(modifier = Modifier.padding(start = 8.dp)) {
                                Text("NEEG格式", style = MaterialTheme.typography.bodyMedium)
                                Text(
                                    "处理后的二进制数据格式",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        
                        // TXT格式选项
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(vertical = 4.dp)
                        ) {
                            RadioButton(
                                selected = selectedFormat == "txt",
                                onClick = { selectedFormat = "txt" }
                            )
                            Column(modifier = Modifier.padding(start = 8.dp)) {
                                Text("TXT格式", style = MaterialTheme.typography.bodyMedium)
                                Text(
                                    "处理后数据，兼容回放分析",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        
                        // 同时保存两种处理后格式的选项
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(vertical = 4.dp)
                        ) {
                            RadioButton(
                                selected = selectedFormat == "both",
                                onClick = { selectedFormat = "both" }
                            )
                            Column(modifier = Modifier.padding(start = 8.dp)) {
                                Text("NEEG + TXT", style = MaterialTheme.typography.bodyMedium)
                                Text(
                                    "同时保存两种处理后格式",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        
                        // 原始数据包格式选项（如果有记录的话）
                        if (isRawRecording) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(vertical = 4.dp)
                            ) {
                                RadioButton(
                                    selected = selectedFormat == "raw",
                                    onClick = { selectedFormat = "raw" }
                                )
                                Column(modifier = Modifier.padding(start = 8.dp)) {
                                    Text("原始数据包", style = MaterialTheme.typography.bodyMedium)
                                    Text(
                                        "十六进制格式的原始蓝牙数据包",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                            
                            // 保存所有格式的选项
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(vertical = 4.dp)
                            ) {
                                RadioButton(
                                    selected = selectedFormat == "all",
                                    onClick = { selectedFormat = "all" }
                                )
                                Column(modifier = Modifier.padding(start = 8.dp)) {
                                    Text("保存所有格式", style = MaterialTheme.typography.bodyMedium)
                                    Text(
                                        "NEEG + TXT + 原始数据包",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (saveFileName.isNotBlank()) {
                            viewModel.saveRecording(
                                fileName = saveFileName.trim(),
                                saveFormat = selectedFormat
                            )
                            saveFileName = ""
                            // 如果有待退出标记，则退出；否则继续采集
                            if (pendingExit) {
                                pendingExit = false
                                onBack()
                            }
                        }
                    },
                    enabled = saveFileName.isNotBlank()
                ) {
                    Text("保存")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        viewModel.discardRecording()
                        saveFileName = ""
                        // 如果有待退出标记，则退出；否则继续采集
                        if (pendingExit) {
                            pendingExit = false
                            onBack()
                        }
                    }
                ) {
                    Text("放弃")
                }
            }
        )
    }
}

@Composable
fun TopControlBar(
    onBack: () -> Unit,
    deviceName: String,
    isConnected: Boolean,
    elapsedTime: Long,
    recordingTime: Long,
    signalQuality: String,
    isCollecting: Boolean,
    isRecording: Boolean,
    isRawRecording: Boolean,
    sampleRate: Int,
    onToggleCollect: () -> Unit,
    onToggleRecord: () -> Unit,
    onFreqAnalysis: () -> Unit,
    onDebugResult: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // Left section
            Row(verticalAlignment = Alignment.CenterVertically) {
                IconButton(onClick = onBack) {
                    Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                }
                
                Column {
                    Text(
                        text = deviceName,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = if (isConnected) "已连接" else "未连接",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isConnected) Color(0xFF4CAF50) else Color(0xFFF44336)
                    )
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // Timer - 采集时长和录制时长
                Column {
                    Text(
                        text = formatTime(elapsedTime),
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                    // 显示录制时长（只有在录制中或录制时长大于0时显示）
                    if (isRecording || recordingTime > 0) {
                        Text(
                            text = "录制: ${formatTime(recordingTime)}",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (isRecording) Color(0xFFF44336) else MaterialTheme.colorScheme.onSurfaceVariant,
                            fontSize = 12.sp
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // Sampling rate display
                Text(
                    text = "采样率:${sampleRate}Hz",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontSize = 12.sp
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // Signal quality
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = when (signalQuality) {
                            "良好" -> Color(0xFF4CAF50)
                            "脱落" -> Color(0xFFF44336)
                            "等待信号" -> Color(0xFF2196F3)
                            else -> Color(0xFF9E9E9E)
                        }
                    )
                ) {
                    Text(
                        text = signalQuality,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
                        color = Color.White,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
            
            // Right section - buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedButton(
                    onClick = onFreqAnalysis,
                    enabled = isCollecting, // 只有在采集时才可用
                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                ) {
                    Text("频域分析", fontSize = 14.sp)
                }
                
                OutlinedButton(
                    onClick = onDebugResult,
                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                ) {
                    Text("生成调试检测结果", fontSize = 14.sp)
                }
                
                Button(
                    onClick = onToggleCollect,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isCollecting) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
                    ),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(if (isCollecting) "停止采集" else "开始采集", fontSize = 14.sp)
                }
                
                // 保存按钮 - 只有在采集中才能使用
                Button(
                    onClick = onToggleRecord,
                    enabled = isCollecting, // 只有采集中才能保存
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isRecording) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.secondary
                    ),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 4.dp)
                ) {
                    Icon(
                        imageVector = if (isRecording) Icons.Default.PlayArrow else Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = when {
                            isRecording && isRawRecording -> "停止保存(含原始数据)"
                            isRecording -> "停止保存"
                            isRawRecording -> "停止原始数据保存"
                            else -> "开始保存"
                        }, 
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterControls(
    paperSpeed: String,
    onPaperSpeedChange: (String) -> Unit,
    sensitivity: String,
    onSensitivityChange: (String) -> Unit,
    notch50Hz: Boolean,
    onNotch50HzChange: (Boolean) -> Unit,
    bandpassEnabled: Boolean,
    onBandpassEnabledChange: (Boolean) -> Unit,
    bandpassStart: String,
    onBandpassStartChange: (String) -> Unit,
    bandpassEnd: String,
    onBandpassEndChange: (String) -> Unit,
    timeConstantEnabled: Boolean,
    onTimeConstantEnabledChange: (Boolean) -> Unit,
    timeConstant: String,
    onTimeConstantChange: (String) -> Unit,
    lowPassEnabled: Boolean,
    onLowPassEnabledChange: (Boolean) -> Unit,
    lowPassFreq: String,
    onLowPassFreqChange: (String) -> Unit,
    highPassEnabled: Boolean,
    onHighPassEnabledChange: (Boolean) -> Unit,
    highPassFreq: String,
    onHighPassFreqChange: (String) -> Unit,
    filterAvailability: FilterAvailability
) {
    val scrollState = rememberScrollState()
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 2.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(scrollState)
                .padding(8.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Paper speed
            FilterDropdown(
                label = "走纸速度",
                value = paperSpeed,
                onValueChange = onPaperSpeedChange,
                options = listOf("15mm/s", "30mm/s", "60mm/s")
            )
            
            // Sensitivity - 按照uniapp版本的完整选项
            FilterDropdown(
                label = "灵敏度",
                value = sensitivity,
                onValueChange = onSensitivityChange,
                options = listOf("1uv/mm", "2uv/mm", "3uv/mm", "5uv/mm", "7uv/mm", "10uv/mm", "15uv/mm", "20uv/mm", "30uv/mm", "50uv/mm", "75uv/mm", "100uv/mm", "150uv/mm", "200uv/mm")
            )
            
            // 50Hz Notch - 始终可用
            Row(verticalAlignment = Alignment.CenterVertically) {
                Checkbox(
                    checked = notch50Hz,
                    onCheckedChange = onNotch50HzChange,
                    enabled = filterAvailability.notchAvailable,
                    modifier = Modifier.scale(0.8f)
                )
                Text(
                    text = "50Hz陷波", 
                    fontSize = 14.sp,
                    color = if (filterAvailability.notchAvailable) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                    }
                )
            }
            
            // Bandpass filter - 带通滤波
            Row(verticalAlignment = Alignment.CenterVertically) {
                Checkbox(
                    checked = bandpassEnabled,
                    onCheckedChange = if (filterAvailability.bandpassAvailable) onBandpassEnabledChange else null,
                    enabled = filterAvailability.bandpassAvailable,
                    modifier = Modifier.scale(0.8f)
                )
                Text(
                    text = "带通滤波", 
                    fontSize = 14.sp,
                    color = if (filterAvailability.bandpassAvailable) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                    }
                )
                if (bandpassEnabled && filterAvailability.bandpassAvailable) {
                    Spacer(modifier = Modifier.width(8.dp))
                    OutlinedTextField(
                        value = bandpassStart,
                        onValueChange = onBandpassStartChange,
                        enabled = filterAvailability.bandpassAvailable,
                        modifier = Modifier
                            .width(60.dp)
                            .height(48.dp),
                        singleLine = true,
                        textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp)
                    )
                    Text(
                        text = "-", 
                        modifier = Modifier.padding(horizontal = 4.dp),
                        color = if (filterAvailability.bandpassAvailable) {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        }
                    )
                    OutlinedTextField(
                        value = bandpassEnd,
                        onValueChange = onBandpassEndChange,
                        enabled = filterAvailability.bandpassAvailable,
                        modifier = Modifier
                            .width(60.dp)
                            .height(48.dp),
                        singleLine = true,
                        textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp)
                    )
                    Text(
                        text = "Hz", 
                        fontSize = 14.sp,
                        color = if (filterAvailability.bandpassAvailable) {
                            MaterialTheme.colorScheme.onSurface
                        } else {
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        }
                    )
                }
            }
            
            // Time constant - 始终可用，独立于主滤波器组
            Box {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Checkbox(
                        checked = timeConstantEnabled,
                        onCheckedChange = onTimeConstantEnabledChange,
                        enabled = filterAvailability.timeConstantAvailable,
                        modifier = Modifier.scale(0.8f)
                    )
                    Column {
                        Text(
                            text = "时间常数", 
                            fontSize = 14.sp,
                            color = if (filterAvailability.timeConstantAvailable) {
                                MaterialTheme.colorScheme.onSurface
                            } else {
                                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                            }
                        )
                        if (timeConstantEnabled && filterAvailability.timeConstantAvailable) {
                            val cutoffFreq = 1.0 / (2.0 * kotlin.math.PI * timeConstant.toDouble())
                            Text(
                                text = "${String.format(java.util.Locale.US, "%.3f", cutoffFreq)}Hz",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                    if (timeConstantEnabled && filterAvailability.timeConstantAvailable) {
                        Spacer(modifier = Modifier.width(8.dp))
                        FilterDropdown(
                            label = "",
                            value = timeConstant,
                            onValueChange = onTimeConstantChange,
                            options = listOf("10", "5.0", "2.0", "1.0", "0.6", "0.3", "0.1", "0.03", "0.003", "0.001"),
                            enabled = filterAvailability.timeConstantAvailable
                        )
                    }
                }
            }
            
            // Low pass filter - 主滤波器组成员
            Row(verticalAlignment = Alignment.CenterVertically) {
                Checkbox(
                    checked = lowPassEnabled,
                    onCheckedChange = if (filterAvailability.lowPassAvailable) onLowPassEnabledChange else null,
                    enabled = filterAvailability.lowPassAvailable,
                    modifier = Modifier.scale(0.8f)
                )
                Text(
                    text = "低通滤波", 
                    fontSize = 14.sp,
                    color = if (filterAvailability.lowPassAvailable) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                    }
                )
                if (lowPassEnabled && filterAvailability.lowPassAvailable) {
                    Spacer(modifier = Modifier.width(8.dp))
                    FilterDropdown(
                        label = "",
                        value = lowPassFreq,
                        onValueChange = onLowPassFreqChange,
                        options = listOf("15", "30", "35", "60", "70", "100"),
                        enabled = filterAvailability.lowPassAvailable
                    )
                }
            }
            
            // High pass filter - 主滤波器组成员
            Row(verticalAlignment = Alignment.CenterVertically) {
                Checkbox(
                    checked = highPassEnabled,
                    onCheckedChange = if (filterAvailability.highPassAvailable) onHighPassEnabledChange else null,
                    enabled = filterAvailability.highPassAvailable,
                    modifier = Modifier.scale(0.8f)
                )
                Text(
                    text = "高通滤波", 
                    fontSize = 14.sp,
                    color = if (filterAvailability.highPassAvailable) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                    }
                )
                if (highPassEnabled && filterAvailability.highPassAvailable) {
                    Spacer(modifier = Modifier.width(8.dp))
                    FilterDropdown(
                        label = "",
                        value = highPassFreq,
                        onValueChange = onHighPassFreqChange,
                        options = listOf("0.1", "0.3", "0.5", "1.0", "1.5", "5", "10", "20", "50"),
                        enabled = filterAvailability.highPassAvailable
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterDropdown(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    options: List<String>,
    enabled: Boolean = true
) {
    var expanded by remember { mutableStateOf(false) }
    
    Row(verticalAlignment = Alignment.CenterVertically) {
        if (label.isNotEmpty()) {
            Text(
                text = label, 
                fontSize = 14.sp,
                color = if (enabled) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                }
            )
            Spacer(modifier = Modifier.width(4.dp))
        }
        ExposedDropdownMenuBox(
            expanded = expanded && enabled,
            onExpandedChange = { if (enabled) expanded = it }
        ) {
            OutlinedTextField(
                value = value,
                onValueChange = {},
                readOnly = true,
                enabled = enabled,
                singleLine = true,
                textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 14.sp),
                modifier = Modifier
                    .menuAnchor(MenuAnchorType.PrimaryEditable, enabled)
                    .width(100.dp)
                    .height(48.dp),
                trailingIcon = { 
                    ExposedDropdownMenuDefaults.TrailingIcon(
                        expanded = expanded && enabled
                    ) 
                }
            )
            if (enabled) {
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    options.forEach { option ->
                        DropdownMenuItem(
                            text = { Text(option, fontSize = 14.sp) },
                            onClick = {
                                onValueChange(option)
                                expanded = false
                            },
                            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun WaveformPanel(
    title: String,
    modifier: Modifier = Modifier,
    color: Color
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = color
            )
            
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .border(1.dp, Color.Gray.copy(alpha = 0.3f))
                    .background(Color.Black.copy(alpha = 0.05f)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "波形显示区域",
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}


@Composable
fun DebugResultDialog(
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("调试检测结果") },
        text = {
            Column {
                Text("检测时间：${formatCurrentTime()}")
                Spacer(modifier = Modifier.height(8.dp))
                Text("AF7通道：正常")
                Text("AF8通道：正常")
                Text("信号质量：良好")
                Text("噪声水平：低")
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

private fun formatTime(seconds: Long): String {
    val hours = seconds / 3600
    val minutes = (seconds % 3600) / 60
    val secs = seconds % 60
    return String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, secs)
}

private fun formatCurrentTime(): String {
    return try {
        val sdf = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        sdf.format(java.util.Date())
    } catch (e: Exception) {
        "N/A"
    }
}

