package com.brainwonderful.naoyu_bluetooth_android.ui.screens

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.brainwonderful.naoyu_bluetooth_android.ui.components.ScreenInfoDisplay
import com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfoUtil
import com.brainwonderful.naoyu_bluetooth_android.utils.rememberScreenInfo
import kotlin.math.roundToInt

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhysicalDesignScreen(
    onBack: () -> Unit
) {
    val screenInfo = rememberScreenInfo()
    val density = LocalDensity.current
    var showScreenInfo by remember { mutableStateOf(false) }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // 网格背景
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            drawGrid(screenInfo, density)
        }
        
        // 左上角返回按钮
        FloatingActionButton(
            onClick = onBack,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
                .size(48.dp),
            containerColor = MaterialTheme.colorScheme.primary,
            elevation = FloatingActionButtonDefaults.elevation(defaultElevation = 8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Color.White
            )
        }
        
        // 信息按钮 - 位于左上角返回按钮右侧
        FloatingActionButton(
            onClick = { showScreenInfo = true },
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(start = 80.dp, top = 16.dp)  // 在返回按钮右侧
                .size(48.dp),
            containerColor = MaterialTheme.colorScheme.secondary,
            elevation = FloatingActionButtonDefaults.elevation(defaultElevation = 8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Info,
                contentDescription = "屏幕信息",
                tint = Color.White
            )
        }
        
        // 底部横向刻度尺
        BottomHorizontalRuler(
            screenInfo = screenInfo,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
        
        // 右侧纵向刻度尺
        RightVerticalRuler(
            screenInfo = screenInfo,
            modifier = Modifier.align(Alignment.CenterEnd)
        )
        
        // 屏幕信息对话框
        if (showScreenInfo) {
            Dialog(onDismissRequest = { showScreenInfo = false }) {
                Card(
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White)
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp)
                    ) {
                        Text(
                            text = "屏幕信息",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                        ScreenInfoDisplay()
                        
                        TextButton(
                            onClick = { showScreenInfo = false },
                            modifier = Modifier
                                .align(Alignment.End)
                                .padding(top = 16.dp)
                        ) {
                            Text("关闭")
                        }
                    }
                }
            }
        }
    }
}

/**
 * 绘制网格
 */
fun androidx.compose.ui.graphics.drawscope.DrawScope.drawGrid(
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo,
    density: androidx.compose.ui.unit.Density
) {
    val pixelsPerMm = screenInfo.xdpi / 25.4f
    val pixelsPerCm = pixelsPerMm * 10f
    
    // 绘制毫米网格线（浅灰色）
    val mmLineColor = Color.Gray.copy(alpha = 0.1f)
    for (mm in 0..(screenInfo.widthMm).roundToInt()) {
        val x = mm * pixelsPerMm
        if (x <= size.width) {
            drawLine(
                color = mmLineColor,
                start = Offset(x, 0f),
                end = Offset(x, size.height),
                strokeWidth = 0.5f
            )
        }
    }
    
    for (mm in 0..(screenInfo.heightMm).roundToInt()) {
        val y = mm * pixelsPerMm
        if (y <= size.height) {
            drawLine(
                color = mmLineColor,
                start = Offset(0f, y),
                end = Offset(size.width, y),
                strokeWidth = 0.5f
            )
        }
    }
    
    // 绘制厘米网格线（深一点的灰色）
    val cmLineColor = Color.Gray.copy(alpha = 0.3f)
    for (cm in 0..(screenInfo.widthCm).roundToInt()) {
        val x = cm * pixelsPerCm
        if (x <= size.width) {
            drawLine(
                color = cmLineColor,
                start = Offset(x, 0f),
                end = Offset(x, size.height),
                strokeWidth = 1f
            )
        }
    }
    
    for (cm in 0..(screenInfo.heightCm).roundToInt()) {
        val y = cm * pixelsPerCm
        if (y <= size.height) {
            drawLine(
                color = cmLineColor,
                start = Offset(0f, y),
                end = Offset(size.width, y),
                strokeWidth = 1f
            )
        }
    }
}

/**
 * 底部横向刻度尺
 */
@Composable
fun BottomHorizontalRuler(
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val pixelsPerMm = screenInfo.xdpi / 25.4f
    val rulerHeight = 40.dp
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(rulerHeight)
                .background(Color.White.copy(alpha = 0.9f))
        ) {
            // 绘制基线（在底部）
            drawLine(
                color = Color.Black,
                start = Offset(0f, size.height),
                end = Offset(size.width, size.height),
                strokeWidth = 3f
            )
            
            // 绘制毫米刻度（朝上）
            for (mm in 0..(screenInfo.widthMm).roundToInt()) {
                val x = mm * pixelsPerMm
                if (x <= size.width) {
                    val tickHeight = when {
                        mm % 10 == 0 -> size.height * 0.8f
                        mm % 5 == 0 -> size.height * 0.6f
                        else -> size.height * 0.3f
                    }
                    
                    drawLine(
                        color = Color.Black,
                        start = Offset(x, size.height),
                        end = Offset(x, size.height - tickHeight),
                        strokeWidth = if (mm % 10 == 0) 2f else 1f
                    )
                }
            }
        }
        
        // 厘米标签（在刻度尺下面）
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp)
        ) {
            for (cm in 0..(screenInfo.widthCm).roundToInt()) {
                val widthDp = with(density) { (cm * 10f * pixelsPerMm).toDp() }
                Box(
                    modifier = Modifier.width(
                        if (cm == 0) widthDp
                        else widthDp - with(density) { ((cm - 1) * 10f * pixelsPerMm).toDp() }
                    ),
                    contentAlignment = if (cm == 0) Alignment.CenterStart else Alignment.CenterEnd
                ) {
                    if (cm > 0) {
                        Text(
                            text = "${cm}cm",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Red
                        )
                    }
                }
            }
        }
    }
}

/**
 * 右侧纵向刻度尺
 */
@Composable
fun RightVerticalRuler(
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val pixelsPerMm = screenInfo.ydpi / 25.4f
    val rulerWidth = 40.dp
    
    Row(
        modifier = modifier.fillMaxHeight()
    ) {
        // 刻度尺
        Canvas(
            modifier = Modifier
                .width(rulerWidth)
                .fillMaxHeight()
                .background(Color.White.copy(alpha = 0.9f))
        ) {
            // 绘制基线（在右侧）
            drawLine(
                color = Color.Black,
                start = Offset(size.width, 0f),
                end = Offset(size.width, size.height),
                strokeWidth = 3f
            )
            
            // 绘制毫米刻度（朝左）
            for (mm in 0..(screenInfo.heightMm).roundToInt()) {
                val y = mm * pixelsPerMm
                if (y <= size.height) {
                    val tickWidth = when {
                        mm % 10 == 0 -> size.width * 0.8f
                        mm % 5 == 0 -> size.width * 0.6f
                        else -> size.width * 0.3f
                    }
                    
                    drawLine(
                        color = Color.Black,
                        start = Offset(size.width, y),
                        end = Offset(size.width - tickWidth, y),
                        strokeWidth = if (mm % 10 == 0) 2f else 1f
                    )
                }
            }
        }
        
        // 厘米标签（在刻度尺右侧）
        Column(
            modifier = Modifier
                .width(30.dp)
                .fillMaxHeight()
                .padding(start = 4.dp),
            horizontalAlignment = Alignment.Start
        ) {
            val totalCm = (screenInfo.heightCm).roundToInt()
            for (cm in 0..totalCm) {
                // 跳过最后两个厘米标注，避免与底部横向刻度尺重叠
                if (cm > totalCm - 2) {
                    continue
                }
                
                val heightDp = with(density) { (cm * 10f * pixelsPerMm).toDp() }
                Box(
                    modifier = Modifier.height(
                        if (cm == 0) heightDp
                        else heightDp - with(density) { ((cm - 1) * 10f * pixelsPerMm).toDp() }
                    ),
                    contentAlignment = if (cm == 0) Alignment.TopStart else Alignment.BottomStart
                ) {
                    if (cm > 0) {
                        Text(
                            text = "${cm}cm",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Red
                        )
                    }
                }
            }
            
            // 为最后两个厘米添加空白空间，但不显示标签
            val lastTwoCmHeight = with(density) { (20f * pixelsPerMm).toDp() }
            Spacer(modifier = Modifier.height(lastTwoCmHeight))
        }
    }
}

