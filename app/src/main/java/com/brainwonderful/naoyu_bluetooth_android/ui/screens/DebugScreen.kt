package com.brainwonderful.naoyu_bluetooth_android.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.brainwonderful.naoyu_bluetooth_android.data.DataPlayer
import com.brainwonderful.naoyu_bluetooth_android.ui.components.DualChannelWaveformChart
import com.brainwonderful.naoyu_bluetooth_android.ui.components.FrequencyAnalysisDialog
import com.brainwonderful.naoyu_bluetooth_android.viewmodel.DebugViewModel
import com.brainwonderful.naoyu_bluetooth_android.utils.formatDuration
import com.brainwonderful.naoyu_bluetooth_android.utils.formatFileSize
import com.brainwonderful.naoyu_bluetooth_android.ui.utils.FilterAvailability
import com.brainwonderful.naoyu_bluetooth_android.ui.utils.calculateFilterAvailability
import java.text.SimpleDateFormat
import java.util.*


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DebugScreen(
    viewModel: DebugViewModel,
    onBack: () -> Unit
) {
    // State from ViewModel
    val recordings by viewModel.recordings.collectAsStateWithLifecycle()
    val selectedRecording by viewModel.selectedRecording.collectAsStateWithLifecycle()
    val playbackState by viewModel.playbackState.collectAsStateWithLifecycle()
    val playbackProgress by viewModel.playbackProgress.collectAsStateWithLifecycle()
    val fileInfo by viewModel.fileInfo.collectAsStateWithLifecycle()
    val af7DisplayData by viewModel.af7DisplayData.collectAsStateWithLifecycle()
    val af8DisplayData by viewModel.af8DisplayData.collectAsStateWithLifecycle()
    
    // Local UI state
    var showDeleteDialog by remember { mutableStateOf<DebugViewModel.RecordingItem?>(null) }
    var showFileListDialog by remember { mutableStateOf(false) }
    var showSampleRateDialog by remember { mutableStateOf<DebugViewModel.RecordingItem?>(null) }
    
    
    // Filter states - 按照采集界面的完整参数
    var paperSpeed by remember { mutableStateOf("30mm/s") } // 默认30mm/s (对应8格)
    var sensitivity by remember { mutableStateOf("5uv/mm") } // 默认5μV/mm
    var notch50Hz by remember { mutableStateOf(true) }       // 默认开启50Hz陷波
    var bandpassEnabled by remember { mutableStateOf(true) } // 默认开启带通滤波
    var bandpassStart by remember { mutableStateOf("1") }    // 默认1Hz
    var bandpassEnd by remember { mutableStateOf("35") }     // 默认35Hz
    var timeConstantEnabled by remember { mutableStateOf(false) } // 默认关闭时间常数
    var timeConstant by remember { mutableStateOf("0.03") }  // 优化默认值提供更好的信号响应
    var lowPassEnabled by remember { mutableStateOf(false) } // 默认关闭低通滤波
    var lowPassFreq by remember { mutableStateOf("35") }
    var highPassEnabled by remember { mutableStateOf(false) } // 默认关闭高通滤波
    var highPassFreq by remember { mutableStateOf("0.5") }
    
    // 动态计算滤波器可用性状态
    val filterAvailability by remember {
        derivedStateOf {
            calculateFilterAvailability(
                bandpassEnabled = bandpassEnabled,
                lowPassEnabled = lowPassEnabled,
                highPassEnabled = highPassEnabled
            )
        }
    }
    
    // Playback speed options - 隐藏速度选择，保持默认1.0x速度
    // val playbackSpeeds = listOf(0.5f, 1.0f, 1.5f, 2.0f)
    // var playbackSpeed by remember { mutableFloatStateOf(1.0f) }
    
    // 频谱分析相关状态
    var showFreqAnalysisDialog by remember { mutableStateOf(false) }
    val isAnalyzingSpectrum by viewModel.isAnalyzingSpectrum.collectAsStateWithLifecycle()
    val af7SpectrumData by viewModel.af7SpectrumData.collectAsStateWithLifecycle()
    val af8SpectrumData by viewModel.af8SpectrumData.collectAsStateWithLifecycle()
    
    // 更新滤波器设置 - 修复滤波器冲突问题
    LaunchedEffect(notch50Hz, bandpassEnabled, bandpassStart, bandpassEnd, 
                   timeConstantEnabled, timeConstant, lowPassEnabled, lowPassFreq, 
                   highPassEnabled, highPassFreq) {
        // 50Hz陷波 - 独立处理
        viewModel.toggleNotchFilter(notch50Hz)
        
        // 时间常数滤波 - 独立处理（高通特性）
        if (timeConstantEnabled) {
            val tcValue = timeConstant.toDoubleOrNull() ?: 0.3
            viewModel.updateTimeConstant(true, tcValue)
        } else {
            viewModel.updateTimeConstant(false, 0.3)
        }
        
        // 主滤波器设置 - 修复互斥逻辑，确保只有一个主滤波器生效
        // 计算当前启用的滤波器数量
        val enabledFilters = listOf(bandpassEnabled, lowPassEnabled, highPassEnabled).count { it }
        
        when {
            // 如果多个滤波器同时启用，按优先级选择一个
            enabledFilters > 1 -> {
                when {
                    // 优先级1：带通滤波（最常用）
                    bandpassEnabled -> {
                        val startFreq = bandpassStart.toDoubleOrNull() ?: 1.0
                        val endFreq = bandpassEnd.toDoubleOrNull() ?: 35.0
                        viewModel.updateBandpassFilter(true, startFreq, endFreq)
                        android.util.Log.w("DebugScreen", "多个主滤波器同时启用，优先使用带通滤波")
                    }
                    // 优先级2：低通滤波
                    lowPassEnabled -> {
                        val freq = lowPassFreq.toDoubleOrNull() ?: 35.0
                        viewModel.updateLowPassFilter(true, freq)
                        android.util.Log.w("DebugScreen", "多个主滤波器同时启用，优先使用低通滤波")
                    }
                    // 优先级3：高通滤波
                    highPassEnabled -> {
                        val freq = highPassFreq.toDoubleOrNull() ?: 0.5
                        viewModel.updateHighPassFilter(true, freq)
                        android.util.Log.w("DebugScreen", "多个主滤波器同时启用，优先使用高通滤波")
                    }
                }
            }
            // 只有一个滤波器启用时的正常处理
            bandpassEnabled -> {
                val startFreq = bandpassStart.toDoubleOrNull() ?: 1.0
                val endFreq = bandpassEnd.toDoubleOrNull() ?: 35.0
                viewModel.updateBandpassFilter(true, startFreq, endFreq)
            }
            lowPassEnabled -> {
                val freq = lowPassFreq.toDoubleOrNull() ?: 35.0
                viewModel.updateLowPassFilter(true, freq)
            }
            highPassEnabled -> {
                val freq = highPassFreq.toDoubleOrNull() ?: 0.5
                viewModel.updateHighPassFilter(true, freq)
            }
            // 所有主滤波器都禁用
            else -> {
                viewModel.updateBandpassFilter(false, 1.0, 35.0)
            }
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("数据回放调试") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    OutlinedButton(
                        onClick = { showFreqAnalysisDialog = true },
                        enabled = playbackState == DataPlayer.PlaybackState.PLAYING,
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                    ) {
                        Text("频域分析", fontSize = 14.sp)
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    IconButton(
                        onClick = { showFileListDialog = true }
                    ) {
                        Icon(Icons.Default.Settings, contentDescription = "选择文件")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color(0xFFF5F5F5))
        ) {
            // Compact file info and playback controls
            CompactFileInfoCard(
                selectedRecording = selectedRecording,
                fileInfo = fileInfo,
                playbackState = playbackState,
                playbackProgress = playbackProgress,
                onPlay = { viewModel.startPlayback() },
                onStop = { viewModel.stopPlayback() },
            )
            
            // Filter controls - 按照采集界面的完整滤波参数
            FilterControls(
                paperSpeed = paperSpeed,
                onPaperSpeedChange = { paperSpeed = it },
                sensitivity = sensitivity,
                onSensitivityChange = { sensitivity = it },
                notch50Hz = notch50Hz,
                onNotch50HzChange = { notch50Hz = it },
                bandpassEnabled = bandpassEnabled,
                onBandpassEnabledChange = { bandpassEnabled = it },
                bandpassStart = bandpassStart,
                onBandpassStartChange = { bandpassStart = it },
                bandpassEnd = bandpassEnd,
                onBandpassEndChange = { bandpassEnd = it },
                timeConstantEnabled = timeConstantEnabled,
                onTimeConstantEnabledChange = { timeConstantEnabled = it },
                timeConstant = timeConstant,
                onTimeConstantChange = { timeConstant = it },
                lowPassEnabled = lowPassEnabled,
                onLowPassEnabledChange = { lowPassEnabled = it },
                lowPassFreq = lowPassFreq,
                onLowPassFreqChange = { lowPassFreq = it },
                highPassEnabled = highPassEnabled,
                onHighPassEnabledChange = { highPassEnabled = it },
                highPassFreq = highPassFreq,
                onHighPassFreqChange = { highPassFreq = it },
                filterAvailability = filterAvailability
            )
            
            // Waveform display
            when {
                selectedRecording == null -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Settings,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Text(
                                text = "选择录制文件开始回放",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Button(
                                onClick = { showFileListDialog = true }
                            ) {
                                Icon(Icons.Default.Settings, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("选择文件")
                            }
                        }
                    }
                }
                playbackState == DataPlayer.PlaybackState.LOADING -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                playbackState == DataPlayer.PlaybackState.PLAYING -> {
                    // Show filtered waveform only
                    DualChannelWaveformChart(
                        af7Data = af7DisplayData,
                        af8Data = af8DisplayData,
                        paperSpeed = paperSpeed,
                        sensitivity = sensitivity,
                        isCollecting = true,
                        sampleRate = fileInfo?.sampleRate?.toDouble() ?: 250.0, // 使用文件实际采样率
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(8.dp)
                    )
                }
                else -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = when (playbackState) {
                                DataPlayer.PlaybackState.STOPPED -> "点击播放按钮开始回放"
                                DataPlayer.PlaybackState.FINISHED -> "回放完成"
                                DataPlayer.PlaybackState.ERROR -> "文件加载错误"
                                else -> "等待操作"
                            },
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
    
    // File selection dialog
    if (showFileListDialog) {
        FileSelectionDialog(
            recordings = recordings,
            onDismiss = { showFileListDialog = false },
            onSelect = { recording ->
                val file = java.io.File(recording.filePath)
                if (file.extension.lowercase() == "txt") {
                    // Show sampling rate selection for TXT files
                    showSampleRateDialog = recording
                } else {
                    // Direct load for NEEG files
                    viewModel.selectRecording(recording)
                }
                showFileListDialog = false
            },
            onDelete = { recording ->
                showDeleteDialog = recording
            },
            onRefresh = { viewModel.loadRecordings() }
        )
    }
    
    // Delete confirmation dialog
    showDeleteDialog?.let { recording ->
        AlertDialog(
            onDismissRequest = { showDeleteDialog = null },
            title = { Text("删除录制文件") },
            text = { Text("确定要删除 \"${recording.fileName}\" 吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deleteRecording(recording)
                        showDeleteDialog = null
                    }
                ) {
                    Text("删除")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = null }
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 频域分析对话框
    if (showFreqAnalysisDialog) {
        FrequencyAnalysisDialog(
            onDismiss = { 
                showFreqAnalysisDialog = false
                viewModel.stopSpectrumAnalysis()
            },
            af7SpectrumData = af7SpectrumData,
            af8SpectrumData = af8SpectrumData,
            isCollecting = isAnalyzingSpectrum,
            onStartAnalysis = { viewModel.startSpectrumAnalysis() },
            onStopAnalysis = { viewModel.stopSpectrumAnalysis() }
        )
    }
    
    // 采样率选择对话框（仅TXT文件）
    showSampleRateDialog?.let { recording ->
        SampleRateSelectionDialog(
            fileName = recording.fileName,
            onDismiss = { showSampleRateDialog = null },
            onConfirm = { sampleRate ->
                viewModel.selectRecording(recording, sampleRate)
                showSampleRateDialog = null
            }
        )
    }
}

@Composable
fun CompactFileInfoCard(
    selectedRecording: DebugViewModel.RecordingItem?,
    fileInfo: DataPlayer.FileInfo?,
    playbackState: DataPlayer.PlaybackState,
    playbackProgress: DataPlayer.PlaybackProgress,
    onPlay: () -> Unit,
    onStop: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // File info in horizontal layout
            fileInfo?.let { info ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "文件: ${info.fileName}",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = "时长: ${formatDuration(info.duration)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "数据包: ${info.totalPackets}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "设备: ${info.deviceName}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "采样率: ${info.sampleRate}Hz",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // Progress bar
            if (playbackProgress.totalPackets > 0) {
                LinearProgressIndicator(
                    progress = { playbackProgress.percentage / 100f },
                    modifier = Modifier.fillMaxWidth(),
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = formatDuration(playbackProgress.currentTime),
                        style = MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "${String.format(Locale.getDefault(), "%.1f", playbackProgress.percentage)}%",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = formatDuration(playbackProgress.totalTime),
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // Compact controls row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Control buttons
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = onStop,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("停止")
                    }
                    
                    // 播放按钮 - 只在停止状态时显示
                    if (playbackState != DataPlayer.PlaybackState.PLAYING) {
                        Button(
                            onClick = onPlay,
                            enabled = selectedRecording != null && 
                                     playbackState != DataPlayer.PlaybackState.LOADING,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.primary
                            ),
                            modifier = Modifier.width(80.dp)
                        ) {
                            Text(
                                text = "播放",
                                fontSize = 14.sp,
                                maxLines = 1
                            )
                        }
                    }
                }
                
                // Speed control - 隐藏速度选择
                // Row(verticalAlignment = Alignment.CenterVertically) {
                //     Text("速度:", fontSize = 12.sp)
                //     Spacer(modifier = Modifier.width(4.dp))
                //     playbackSpeeds.forEach { speed ->
                //         FilterChip(
                //             selected = playbackSpeed == speed,
                //             onClick = { onSpeedChange(speed) },
                //             label = { Text("${speed}x", fontSize = 10.sp) },
                //             modifier = Modifier
                //                 .padding(end = 2.dp)
                //                 .height(28.dp)
                //         )
                //     }
                // }
                
            }
        }
    }
}


@Composable
fun FileSelectionDialog(
    recordings: List<DebugViewModel.RecordingItem>,
    onDismiss: () -> Unit,
    onSelect: (DebugViewModel.RecordingItem) -> Unit,
    onDelete: (DebugViewModel.RecordingItem) -> Unit,
    onRefresh: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("选择录制文件")
                IconButton(onClick = onRefresh) {
                    Icon(Icons.Default.Refresh, contentDescription = "刷新")
                }
            }
        },
        text = {
            if (recordings.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "没有找到录制文件",
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(400.dp)
                ) {
                    items(recordings) { recording ->
                        RecordingListItem(
                            recording = recording,
                            onSelect = { onSelect(recording) },
                            onDelete = { onDelete(recording) }
                        )
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        }
    )
}

@Composable
fun SampleRateSelectionDialog(
    fileName: String,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit
) {
    var selectedSampleRate by remember { mutableIntStateOf(500) } // 默认500Hz
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("选择采样率") },
        text = {
            Column {
                Text(
                    text = "文件: $fileName",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "TXT文件需要手动选择采样率：",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(top = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 采样率选项
                val sampleRates = listOf(250, 500)
                sampleRates.forEach { rate ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { selectedSampleRate = rate }
                            .padding(vertical = 8.dp)
                    ) {
                        RadioButton(
                            selected = selectedSampleRate == rate,
                            onClick = { selectedSampleRate = rate }
                        )
                        Text(
                            text = "${rate}Hz",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onConfirm(selectedSampleRate) }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
fun RecordingListItem(
    recording: DebugViewModel.RecordingItem,
    onSelect: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { onSelect() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = recording.fileName,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    // File type badge
                    val file = java.io.File(recording.filePath)
                    val fileExtension = file.extension.uppercase()
                    val badgeColor = when (fileExtension) {
                        "NEEG" -> MaterialTheme.colorScheme.primary
                        "TXT" -> MaterialTheme.colorScheme.secondary
                        else -> MaterialTheme.colorScheme.outline
                    }
                    val badgeText = if (fileExtension == "TXT") {
                        "TXT"
                    } else {
                        "NEEG"
                    }
                    Surface(
                        color = badgeColor.copy(alpha = 0.1f),
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(4.dp),
                        modifier = Modifier.padding(2.dp)
                    ) {
                        Text(
                            text = badgeText,
                            style = MaterialTheme.typography.labelSmall,
                            color = badgeColor,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
                Text(
                    text = formatFileSize(recording.fileSize),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                        .format(Date(recording.recordTime)),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            IconButton(onClick = onDelete) {
                Icon(
                    Icons.Default.Delete,
                    contentDescription = "删除",
                    tint = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}


