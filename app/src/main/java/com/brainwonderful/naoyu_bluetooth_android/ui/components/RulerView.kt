package com.brainwonderful.naoyu_bluetooth_android.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfoUtil
import com.brainwonderful.naoyu_bluetooth_android.utils.rememberScreenInfo
import kotlin.math.roundToInt

/**
 * 横向刻度尺
 */
@Composable
fun HorizontalRuler(
    modifier: Modifier = Modifier,
    height: Dp = 80.dp,
    showMm: Boolean = true,
    showCm: Boolean = true
) {
    val screenInfo = rememberScreenInfo()
    val density = LocalDensity.current
    
    Column(modifier = modifier) {
        // 标题
        Text(
            text = "横向刻度尺（宽度: ${String.format("%.1f", screenInfo.widthCm)}cm）",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(height)
        ) {
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White)
            ) {
                drawHorizontalRuler(
                    screenInfo = screenInfo,
                    density = density,
                    showMm = showMm,
                    showCm = showCm
                )
            }
            
            // 添加厘米标注
            HorizontalRulerLabels(screenInfo = screenInfo)
        }
    }
}

@Composable
fun HorizontalRulerLabels(screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo) {
    val density = LocalDensity.current
    val pixelsPerMm = screenInfo.xdpi / 25.4f
    val totalWidthMm = screenInfo.widthMm
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 4.dp)
    ) {
        for (cm in 0..(totalWidthMm / 10f).toInt()) {
            val widthMm = cm * 10f
            if (widthMm <= totalWidthMm) {
                val widthDp = with(density) { (widthMm * pixelsPerMm).toDp() }
                
                Box(
                    modifier = Modifier.width(
                        if (cm == 0) widthDp 
                        else (widthMm * pixelsPerMm - (cm - 1) * 10f * pixelsPerMm).let { with(density) { it.toDp() } }
                    ),
                    contentAlignment = if (cm == 0) Alignment.CenterStart else Alignment.CenterEnd
                ) {
                    if (cm > 0) {
                        Text(
                            text = "${cm}cm",
                            style = MaterialTheme.typography.labelSmall,
                            fontSize = 10.sp,
                            color = Color.Red,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

/**
 * 纵向刻度尺
 */
@Composable
fun VerticalRuler(
    modifier: Modifier = Modifier,
    width: Dp = 80.dp,
    showMm: Boolean = true,
    showCm: Boolean = true
) {
    val screenInfo = rememberScreenInfo()
    val density = LocalDensity.current
    
    Row(modifier = modifier) {
        // 标题和标注区域
        Column(
            modifier = Modifier.width(80.dp),
            horizontalAlignment = Alignment.End
        ) {
            Text(
                text = "纵向刻度尺",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text(
                text = "高度: ${String.format("%.1f", screenInfo.heightCm)}cm",
                style = MaterialTheme.typography.labelSmall,
                fontSize = 10.sp
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 厘米标注
            VerticalRulerLabels(
                screenInfo = screenInfo,
                modifier = Modifier.weight(1f)
            )
        }
        
        // 刻度尺
        Canvas(
            modifier = Modifier
                .width(width)
                .fillMaxHeight()
                .background(Color.White)
        ) {
            drawVerticalRuler(
                screenInfo = screenInfo,
                density = density,
                showMm = showMm,
                showCm = showCm
            )
        }
    }
}

@Composable
fun VerticalRulerLabels(
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val pixelsPerMm = screenInfo.ydpi / 25.4f
    val totalHeightMm = screenInfo.heightMm
    
    Column(
        modifier = modifier
            .fillMaxHeight()
    ) {
        for (cm in 0..(totalHeightMm / 10f).toInt()) {
            val heightMm = cm * 10f
            if (heightMm <= totalHeightMm) {
                val heightDp = with(density) { (heightMm * pixelsPerMm).toDp() }
                
                Box(
                    modifier = Modifier.height(
                        if (cm == 0) heightDp
                        else (heightMm * pixelsPerMm - (cm - 1) * 10f * pixelsPerMm).let { with(density) { it.toDp() } }
                    ),
                    contentAlignment = if (cm == 0) Alignment.TopEnd else Alignment.BottomEnd
                ) {
                    if (cm > 0) {
                        Text(
                            text = "${cm}cm",
                            style = MaterialTheme.typography.labelSmall,
                            fontSize = 10.sp,
                            color = Color.Red,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

/**
 * 绘制横向刻度尺
 */
private fun DrawScope.drawHorizontalRuler(
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo,
    density: androidx.compose.ui.unit.Density,
    showMm: Boolean,
    showCm: Boolean
) {
    val totalWidthMm = screenInfo.widthMm
    val pixelsPerMm = screenInfo.xdpi / 25.4f
    val height = size.height
    
    // 绘制背景线
    drawLine(
        color = Color.Black,
        start = Offset(0f, height),
        end = Offset(size.width, height),
        strokeWidth = 2.dp.toPx()
    )
    
    // 绘制毫米刻度
    if (showMm) {
        for (mm in 0..totalWidthMm.roundToInt()) {
            val x = mm * pixelsPerMm
            if (x <= size.width) {
                val tickHeight = if (mm % 10 == 0) height * 0.7f else if (mm % 5 == 0) height * 0.5f else height * 0.3f
                
                drawLine(
                    color = Color.Black,
                    start = Offset(x, height),
                    end = Offset(x, height - tickHeight),
                    strokeWidth = 1.dp.toPx()
                )
                
                // 每10mm标注数字
                if (mm % 10 == 0 && mm > 0) {
                    // 注意：这里无法直接绘制文字，需要在Compose层面处理
                }
            }
        }
    }
    
    // 绘制厘米刻度（更粗的线）
    if (showCm) {
        for (cm in 0..(totalWidthMm / 10f).roundToInt()) {
            val x = cm * 10f * pixelsPerMm
            if (x <= size.width) {
                drawLine(
                    color = Color.Red,
                    start = Offset(x, height),
                    end = Offset(x, height * 0.2f),
                    strokeWidth = 2.dp.toPx()
                )
            }
        }
    }
}

/**
 * 绘制纵向刻度尺
 */
private fun DrawScope.drawVerticalRuler(
    screenInfo: com.brainwonderful.naoyu_bluetooth_android.utils.ScreenInfo,
    density: androidx.compose.ui.unit.Density,
    showMm: Boolean,
    showCm: Boolean
) {
    val totalHeightMm = screenInfo.heightMm
    val pixelsPerMm = screenInfo.ydpi / 25.4f
    val width = size.width
    
    // 绘制背景线
    drawLine(
        color = Color.Black,
        start = Offset(width, 0f),
        end = Offset(width, size.height),
        strokeWidth = 2.dp.toPx()
    )
    
    // 绘制毫米刻度
    if (showMm) {
        for (mm in 0..totalHeightMm.roundToInt()) {
            val y = mm * pixelsPerMm
            if (y <= size.height) {
                val tickWidth = if (mm % 10 == 0) width * 0.7f else if (mm % 5 == 0) width * 0.5f else width * 0.3f
                
                drawLine(
                    color = Color.Black,
                    start = Offset(width, y),
                    end = Offset(width - tickWidth, y),
                    strokeWidth = 1.dp.toPx()
                )
            }
        }
    }
    
    // 绘制厘米刻度（更粗的线）
    if (showCm) {
        for (cm in 0..(totalHeightMm / 10f).roundToInt()) {
            val y = cm * 10f * pixelsPerMm
            if (y <= size.height) {
                drawLine(
                    color = Color.Red,
                    start = Offset(width, y),
                    end = Offset(width * 0.2f, y),
                    strokeWidth = 2.dp.toPx()
                )
            }
        }
    }
}

/**
 * 屏幕信息显示组件
 */
@Composable
fun ScreenInfoDisplay(
    modifier: Modifier = Modifier
) {
    val screenInfo = rememberScreenInfo()
    
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "屏幕信息",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        Text("分辨率: ${screenInfo.widthPixels} × ${screenInfo.heightPixels} 像素")
        Text("密度: ${screenInfo.densityDpi} DPI (${String.format("%.2f", screenInfo.density)}x)")
        Text("物理尺寸: ${String.format("%.2f", screenInfo.widthCm)} × ${String.format("%.2f", screenInfo.heightCm)} cm")
        Text("物理尺寸: ${String.format("%.1f", screenInfo.widthMm)} × ${String.format("%.1f", screenInfo.heightMm)} mm")
        Text("对角线: ${String.format("%.2f", screenInfo.diagonalInches)} 英寸")
        Text("X轴DPI: ${String.format("%.1f", screenInfo.xdpi)}")
        Text("Y轴DPI: ${String.format("%.1f", screenInfo.ydpi)}")
    }
}