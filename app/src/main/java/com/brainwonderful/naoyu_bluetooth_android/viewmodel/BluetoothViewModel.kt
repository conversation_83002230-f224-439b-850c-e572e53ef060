package com.brainwonderful.naoyu_bluetooth_android.viewmodel

import android.app.Application
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.location.LocationManager
import android.os.Build
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.brainwonderful.naoyu_bluetooth_android.NaoyuApplication
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.BluetoothStatus
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ConnectedDeviceInfo
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ConnectionState
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.DiscoveredDevice
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ReceivedData
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ScanState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 蓝牙功能的ViewModel
 */
class BluetoothViewModel(application: Application) : AndroidViewModel(application) {
    
    private val bluetoothManager = (application as NaoyuApplication).bluetoothManager
    
    // 状态流
    val scanState: StateFlow<ScanState> = bluetoothManager.scanState
    val connectionState: StateFlow<ConnectionState> = bluetoothManager.connectionState
    val discoveredDevices: StateFlow<List<DiscoveredDevice>> = bluetoothManager.discoveredDevices
    val receivedData: StateFlow<ReceivedData?> = bluetoothManager.receivedData
    val bluetoothStatus: StateFlow<BluetoothStatus> = bluetoothManager.bluetoothStatus
    val connectedDevice: StateFlow<ConnectedDeviceInfo?> = bluetoothManager.connectedDevice
    
    // 日志信息
    private val _logs = MutableStateFlow<List<String>>(emptyList())
    val logs: StateFlow<List<String>> = _logs.asStateFlow()
    
    init {
        // 监听连接状态变化
        viewModelScope.launch {
            connectionState.collect { state ->
                when (state) {
                    ConnectionState.CONNECTED -> addLog("设备连接成功")
                    ConnectionState.DISCONNECTED -> {
                        addLog("设备断开连接")
                    }
                    ConnectionState.CONNECTING -> addLog("正在连接设备...")
                    ConnectionState.ERROR -> addLog("连接错误")
                }
            }
        }
        
        // 监听扫描状态变化
        viewModelScope.launch {
            scanState.collect { state ->
                when (state) {
                    ScanState.SCANNING -> addLog("开始扫描蓝牙设备...")
                    ScanState.IDLE -> addLog("停止扫描")
                    is ScanState.ERROR -> addLog("扫描错误: ${state.message}")
                }
            }
        }
    }
    
    /**
     * 检查蓝牙是否开启
     */
    fun isBluetoothEnabled(): Boolean = bluetoothManager.isBluetoothEnabled()
    
    /**
     * 检查权限
     */
    fun hasRequiredPermissions(): Boolean = bluetoothManager.hasRequiredPermissions()
    
    /**
     * 检查位置服务是否开启
     */
    fun isLocationEnabled(): Boolean {
        val locationManager = getApplication<Application>().getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || 
               locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
    }
    
    /**
     * 开始扫描
     */
    fun startScan() {
        addLog("开始扫描 - Android版本: ${Build.VERSION.SDK_INT}")
        
        if (!isBluetoothEnabled()) {
            addLog("请先开启蓝牙")
            return
        }
        
        // 只在Android 11及以下版本检查位置服务
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            addLog("Android 11及以下版本，检查位置服务")
            if (!isLocationEnabled()) {
                addLog("请先开启位置服务才能扫描BLE设备")
                return
            }
        } else {
            addLog("Android 12+版本，跳过位置服务检查")
        }
        
        if (!hasRequiredPermissions()) {
            addLog("缺少必要的权限")
            return
        }
        
        addLog("所有检查通过，开始蓝牙扫描")
        bluetoothManager.startScan()
    }
    
    /**
     * 停止扫描
     */
    fun stopScan() {
        bluetoothManager.stopScan()
    }
    
    /**
     * 连接设备
     */
    fun connectDevice(device: DiscoveredDevice) {
        if (connectedDevice.value != null) {
            disconnect()
        }
        stopScan()
        addLog("连接设备: ${device.name}")
        bluetoothManager.connectDevice(device.device)
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        bluetoothManager.disconnect()
    }
    
    /**
     * 获取蓝牙状态
     */
    fun getBluetoothStatus() {
        bluetoothManager.getBluetoothStatus()
    }
    
    /**
     * 修改设备名称
     */
    fun renameDevice(newName: String) {
        if (connectionState.value != ConnectionState.CONNECTED) {
            addLog("请先连接设备")
            return
        }
        addLog("发送重命名指令: $newName")
        bluetoothManager.renameDevice(newName)
    }
    
    /**
     * 添加日志
     */
    private fun addLog(message: String) {
        viewModelScope.launch {
            val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
                .format(java.util.Date())
            val logMessage = "$timestamp: $message"
            _logs.value = _logs.value + logMessage
        }
    }
    
    /**
     * 获取扫描剩余时间
     */
    fun getScanRemainingTime(): Long = bluetoothManager.getScanRemainingTime()
    
    /**
     * 检查是否正在扫描
     */
    fun isScanning(): Boolean = bluetoothManager.isScanning()
    
    /**
     * 清除日志
     */
    fun clearLogs() {
        _logs.value = emptyList()
    }
    
    override fun onCleared() {
        super.onCleared()
        // 不清理共享的 bluetoothManager
    }
}