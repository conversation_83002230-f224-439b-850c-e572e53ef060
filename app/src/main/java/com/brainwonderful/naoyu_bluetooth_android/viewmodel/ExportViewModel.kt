package com.brainwonderful.naoyu_bluetooth_android.viewmodel

import android.app.Application
import android.content.Context
import android.net.wifi.WifiManager
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.brainwonderful.naoyu_bluetooth_android.data.DataRecorder
import com.brainwonderful.naoyu_bluetooth_android.data.RawDataRecorder
import com.brainwonderful.naoyu_bluetooth_android.utils.FileServer
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.io.File
import java.net.InetAddress
import java.net.NetworkInterface
import java.text.SimpleDateFormat
import java.util.*

class ExportViewModel(application: Application) : AndroidViewModel(application) {
    
    private val context = application.applicationContext
    private val dataRecorder = DataRecorder(context)
    private val rawDataRecorder = RawDataRecorder(context)
    private val fileServer = FileServer(context)
    
    private val _serverState = MutableStateFlow(ServerState())
    val serverState: StateFlow<ServerState> = _serverState.asStateFlow()
    
    private val _fileList = MutableStateFlow<List<FileInfo>>(emptyList())
    val fileList: StateFlow<List<FileInfo>> = _fileList.asStateFlow()
    
    data class ServerState(
        val isRunning: Boolean = false,
        val ipAddress: String = "",
        val port: Int = 8080,
        val url: String = ""
    )
    
    data class FileInfo(
        val name: String,
        val path: String,
        val size: Long,
        val lastModified: Long,
        val type: String // "neeg", "txt", "raw", "json"
    )
    
    init {
        refreshFileList()
    }
    
    /**
     * 启动文件服务器
     */
    fun startServer() {
        viewModelScope.launch {
            try {
                val ipAddress = getLocalIpAddress()
                val requestedPort = 8080
                
                fileServer.start(requestedPort)
                val actualPort = fileServer.getActualPort()
                
                if (actualPort > 0) {
                    _serverState.value = ServerState(
                        isRunning = true,
                        ipAddress = ipAddress,
                        port = actualPort,
                        url = "http://$ipAddress:$actualPort"
                    )
                } else {
                    _serverState.value = ServerState(isRunning = false)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                _serverState.value = ServerState(isRunning = false)
            }
        }
    }
    
    /**
     * 停止文件服务器
     */
    fun stopServer() {
        viewModelScope.launch {
            try {
                fileServer.stop()
                _serverState.value = ServerState(isRunning = false)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 刷新文件列表
     */
    fun refreshFileList() {
        viewModelScope.launch {
            val files = mutableListOf<FileInfo>()
            
            // 获取recordings目录下的所有文件
            val recordingsDir = File(context.filesDir, "recordings")
            if (recordingsDir.exists()) {
                recordingsDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        val type = when (file.extension) {
                            "neeg" -> "neeg"
                            "txt" -> if (file.name.contains("_raw")) "raw" else "txt"
                            "json" -> "json"
                            else -> "unknown"
                        }
                        
                        files.add(
                            FileInfo(
                                name = file.name,
                                path = file.absolutePath,
                                size = file.length(),
                                lastModified = file.lastModified(),
                                type = type
                            )
                        )
                    }
                }
            }
            
            // 获取recordings/temp目录下的所有文件
            val tempDir = File(context.filesDir, "recordings/temp")
            if (tempDir.exists()) {
                tempDir.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        val type = when (file.extension) {
                            "neeg" -> "neeg"
                            "txt" -> if (file.name.contains("_raw")) "raw" else "txt"
                            "json" -> "json"
                            "csv" -> "csv"
                            "log" -> "log"
                            else -> "unknown"
                        }
                        
                        files.add(
                            FileInfo(
                                name = "temp/${file.name}", // 添加temp前缀以区分目录
                                path = file.absolutePath,
                                size = file.length(),
                                lastModified = file.lastModified(),
                                type = type
                            )
                        )
                    }
                }
            }
            
            // 按修改时间倒序排列
            files.sortByDescending { it.lastModified }
            _fileList.value = files
        }
    }
    
    /**
     * 删除文件
     */
    fun deleteFile(fileInfo: FileInfo) {
        viewModelScope.launch {
            try {
                val file = File(fileInfo.path)
                if (file.exists()) {
                    file.delete()
                    
                    // 如果是neeg或txt文件，也删除对应的元数据文件
                    if (fileInfo.type == "neeg" || fileInfo.type == "txt") {
                        val metadataFile = File(context.filesDir, "recordings/metadata/${file.nameWithoutExtension}.json")
                        if (metadataFile.exists()) {
                            metadataFile.delete()
                        }
                    }
                    
                    refreshFileList()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 获取本地IP地址
     */
    private fun getLocalIpAddress(): String {
        try {
            // 首先尝试获取WiFi IP地址
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val wifiInfo = wifiManager.connectionInfo
            val ipInt = wifiInfo.ipAddress
            
            if (ipInt != 0) {
                return String.format(
                    Locale.getDefault(),
                    "%d.%d.%d.%d",
                    ipInt and 0xff,
                    ipInt shr 8 and 0xff,
                    ipInt shr 16 and 0xff,
                    ipInt shr 24 and 0xff
                )
            }
            
            // 如果WiFi不可用，尝试其他网络接口
            val interfaces = NetworkInterface.getNetworkInterfaces()
            for (networkInterface in interfaces) {
                val addresses = networkInterface.inetAddresses
                for (address in addresses) {
                    if (!address.isLoopbackAddress && address is InetAddress && address.hostAddress?.contains(':') == false) {
                        return address.hostAddress ?: "未知"
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "未知"
    }
    
    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        if (bytes < 1024) return "$bytes B"
        val kb = bytes / 1024.0
        if (kb < 1024) return "%.1f KB".format(kb)
        val mb = kb / 1024.0
        if (mb < 1024) return "%.1f MB".format(mb)
        val gb = mb / 1024.0
        return "%.1f GB".format(gb)
    }
    
    /**
     * 格式化时间
     */
    fun formatTime(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date(timestamp))
    }
    
    override fun onCleared() {
        super.onCleared()
        stopServer()
    }
}
