package com.brainwonderful.naoyu_bluetooth_android.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.brainwonderful.naoyu_bluetooth_android.data.DataPlayer
import com.brainwonderful.naoyu_bluetooth_android.data.DataRecorder
import com.brainwonderful.naoyu_bluetooth_android.data.EEGDataParser
import com.brainwonderful.naoyu_bluetooth_android.data.EEGDataProcessor
import com.brainwonderful.naoyu_bluetooth_android.data.EEGPacket
import com.brainwonderful.naoyu_bluetooth_android.data.ProcessedEEGData
import com.brainwonderful.naoyu_bluetooth_android.data.SpectrumData
import com.brainwonderful.naoyu_bluetooth_android.data.DeviceType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import java.io.File

/**
 * ViewModel for debug/replay screen
 * Manages recorded data playback and analysis
 */
class DebugViewModel(application: Application) : AndroidViewModel(application) {
    
    private val dataPlayer = DataPlayer()
    private val dataRecorder = DataRecorder(application)
    private val eegDataParser = EEGDataParser()
    private val eegDataProcessor = EEGDataProcessor()
    
    // Available recordings
    private val _recordings = MutableStateFlow<List<RecordingItem>>(emptyList())
    val recordings: StateFlow<List<RecordingItem>> = _recordings.asStateFlow()
    
    // Selected recording
    private val _selectedRecording = MutableStateFlow<RecordingItem?>(null)
    val selectedRecording: StateFlow<RecordingItem?> = _selectedRecording.asStateFlow()
    
    // Playback state
    val playbackState = dataPlayer.playbackState
    val playbackProgress = dataPlayer.progress
    val fileInfo = dataPlayer.fileInfo
    
    // Processed EEG data
    val processedEEGData = eegDataProcessor.processedData
    
    // Display buffers for waveform (processed data)
    private val _af7DisplayData = MutableStateFlow<List<Double>>(emptyList())
    val af7DisplayData: StateFlow<List<Double>> = _af7DisplayData.asStateFlow()
    
    private val _af8DisplayData = MutableStateFlow<List<Double>>(emptyList())
    val af8DisplayData: StateFlow<List<Double>> = _af8DisplayData.asStateFlow()
    
    // Display buffers for raw waveform (original data)
    private val _af7RawDisplayData = MutableStateFlow<List<Double>>(emptyList())
    val af7RawDisplayData: StateFlow<List<Double>> = _af7RawDisplayData.asStateFlow()
    
    private val _af8RawDisplayData = MutableStateFlow<List<Double>>(emptyList())
    val af8RawDisplayData: StateFlow<List<Double>> = _af8RawDisplayData.asStateFlow()
    
    // Filter settings (same as CollectViewModel) - 添加缺失的时间常数设置
    private val _filterSettings = MutableStateFlow(
        EEGDataProcessor.FilterSettings(
            notchEnabled = true,
            notchFreq = 50.0,
            firEnabled = true,
            firType = com.brainwonderful.naoyu_bluetooth_android.filters.FilterType.BANDPASS,
            firFreq1 = 1.0,
            firFreq2 = 35.0,
            fftEnabled = false,
            fftSize = 512,
            timeConstantEnabled = false, // 添加时间常数支持
            timeConstantValue = 0.3      // 默认值
        )
    )
    val filterSettings: StateFlow<EEGDataProcessor.FilterSettings> = _filterSettings.asStateFlow()
    
    private val maxDisplayPoints = 1250 // 5 seconds at 250Hz
    
    // 实时频谱分析相关
    private val _isAnalyzingSpectrum = MutableStateFlow(false)
    val isAnalyzingSpectrum: StateFlow<Boolean> = _isAnalyzingSpectrum.asStateFlow()
    
    private val _af7SpectrumData = MutableStateFlow<SpectrumData?>(null)
    val af7SpectrumData: StateFlow<SpectrumData?> = _af7SpectrumData.asStateFlow()
    
    private val _af8SpectrumData = MutableStateFlow<SpectrumData?>(null)
    val af8SpectrumData: StateFlow<SpectrumData?> = _af8SpectrumData.asStateFlow()
    
    private var spectrumAnalysisJob: Job? = null
    
    data class RecordingItem(
        val fileName: String,
        val filePath: String,
        val fileSize: Long,
        val recordTime: Long,
        val duration: Long
    )
    
    init {
        // Load available recordings
        loadRecordings()
        
        // Listen to NEEG file playback data (raw bytes)
        viewModelScope.launch {
            dataPlayer.dataFlow.collect { rawData ->
                // Parse data
                val eegPacket = eegDataParser.parsePacket(rawData)
                if (eegPacket != null && eegDataParser.validatePacket(eegPacket)) {
                    // Update raw data buffers
                    if (playbackState.value == DataPlayer.PlaybackState.PLAYING) {
                        updateRawDisplayBuffers(eegPacket)
                    }
                    
                    // Process data for filtered display
                    eegDataProcessor.processPacket(eegPacket)
                    
                    // 如果正在进行频谱分析，更新频谱数据
                    if (_isAnalyzingSpectrum.value) {
                        // 频谱分析在processedEEGData中处理
                    }
                }
            }
        }
        
        // Listen to TXT file playback data (direct microvolt values)
        viewModelScope.launch {
            dataPlayer.directDataFlow.collect { (af7, af8) ->
                if (playbackState.value == DataPlayer.PlaybackState.PLAYING) {
                    // Create simplified EEGPacket for TXT data
                    val txtPacket = EEGPacket(
                        bagIndex = 0,
                        signalStatus = 0, // Good signal
                        reserved = 0,
                        batteryLevel = 100,
                        af7Data = doubleArrayOf(af7), // Single data point
                        af8Data = doubleArrayOf(af8)  // Single data point
                    )
                    
                    // Update raw data buffers with single point
                    updateRawDisplayBuffers(txtPacket)
                    
                    // Process data for filtered display
                    eegDataProcessor.processPacket(txtPacket)
                }
            }
        }
        
        // Listen to processed EEG data (common for both file types)
        viewModelScope.launch {
            processedEEGData.collect { data ->
                if (data != null && playbackState.value == DataPlayer.PlaybackState.PLAYING) {
                    updateDisplayBuffers(data)
                    
                    // 如果正在进行频谱分析，更新频谱数据
                    if (_isAnalyzingSpectrum.value) {
                        updateSpectrumData(data)
                    }
                }
            }
        }
    }
    
    /**
     * Load available recordings (supports both .neeg and .txt files)
     */
    fun loadRecordings() {
        viewModelScope.launch {
            val recordingsDir = File(getApplication<Application>().filesDir, "recordings")
            if (recordingsDir.exists()) {
                val files = recordingsDir.listFiles { file ->
                    file.isFile && (file.extension == "neeg" || file.extension == "txt")
                } ?: emptyArray()
                
                _recordings.value = files.map { file ->
                    RecordingItem(
                        fileName = file.nameWithoutExtension,
                        filePath = file.absolutePath,
                        fileSize = file.length(),
                        recordTime = file.lastModified(),
                        duration = 0 // Will be updated when file is loaded
                    )
                }.sortedByDescending { it.recordTime }
            }
        }
    }
    
    /**
     * Select a recording
     */
    fun selectRecording(recording: RecordingItem, sampleRate: Int? = null) {
        viewModelScope.launch {
            _selectedRecording.value = recording
            val file = File(recording.filePath)
            if (dataPlayer.loadFile(file, sampleRate)) {
                // Update recording duration from file info
                fileInfo.value?.let { info ->
                    _selectedRecording.value = recording.copy(duration = info.duration)
                }
            }
        }
    }
    
    /**
     * Start playback
     */
    fun startPlayback() {
        dataPlayer.play()
        clearDisplayBuffers()
        
        // Update EEGDataProcessor sampling rate based on file type
        fileInfo.value?.let { info ->
            // Update sampling rate for correct filter initialization
            eegDataProcessor.updateSampleRate(info.sampleRate.toDouble())
        }
    }
    
    /**
     * Pause playback
     */
    fun pausePlayback() {
        dataPlayer.pause()
    }
    
    /**
     * Stop playback
     */
    fun stopPlayback() {
        dataPlayer.stop()
        clearDisplayBuffers()
    }
    
    /**
     * Seek to position
     */
    fun seekTo(position: Float) {
        viewModelScope.launch {
            dataPlayer.seekTo(position)
        }
    }
    
    /**
     * Set playback speed
     */
    fun setPlaybackSpeed(speed: Float) {
        dataPlayer.setPlaybackSpeed(speed)
    }
    
    /**
     * Delete recording
     */
    fun deleteRecording(recording: RecordingItem) {
        viewModelScope.launch {
            val file = File(recording.filePath)
            if (file.exists()) {
                file.delete()
                
                // Also delete metadata if exists
                val metadataFile = File(
                    getApplication<Application>().filesDir,
                    "recordings/metadata/${recording.fileName}.json"
                )
                if (metadataFile.exists()) {
                    metadataFile.delete()
                }
                
                // Reload recordings
                loadRecordings()
                
                // Clear selection if deleted
                if (_selectedRecording.value?.fileName == recording.fileName) {
                    _selectedRecording.value = null
                    dataPlayer.stop()
                }
            }
        }
    }
    
    /**
     * Update filter settings
     */
    fun updateFilterSettings(settings: EEGDataProcessor.FilterSettings) {
        _filterSettings.value = settings
        eegDataProcessor.updateSettings(settings)
    }
    
    /**
     * Toggle 50Hz notch filter
     */
    fun toggleNotchFilter(enabled: Boolean) {
        val current = _filterSettings.value
        updateFilterSettings(current.copy(notchEnabled = enabled))
    }
    
    /**
     * Update bandpass filter settings
     */
    fun updateBandpassFilter(enabled: Boolean, lowFreq: Double, highFreq: Double) {
        // Validate frequency parameters - 使用文件实际采样率
        val sampleRate = fileInfo.value?.sampleRate ?: 250
        val nyquistFreq = sampleRate / 2.0
        if (enabled && lowFreq > 0 && highFreq > lowFreq && highFreq < nyquistFreq) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                firEnabled = enabled,
                firType = com.brainwonderful.naoyu_bluetooth_android.filters.FilterType.BANDPASS,
                firFreq1 = lowFreq,
                firFreq2 = highFreq
            ))
        } else if (!enabled) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(firEnabled = false))
        }
    }
    
    /**
     * Update time constant (high-pass filter) - 按照UniApp版本实现
     */
    fun updateTimeConstant(enabled: Boolean, timeConstant: Double) {
        val current = _filterSettings.value
        updateFilterSettings(current.copy(
            timeConstantEnabled = enabled,
            timeConstantValue = timeConstant
        ))
    }
    
    /**
     * Update low-pass filter
     */
    fun updateLowPassFilter(enabled: Boolean, cutoffFreq: Double) {
        val sampleRate = fileInfo.value?.sampleRate ?: 250
        val nyquistFreq = sampleRate / 2.0
        if (enabled && cutoffFreq > 0 && cutoffFreq < nyquistFreq) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                firEnabled = true,
                firType = com.brainwonderful.naoyu_bluetooth_android.filters.FilterType.LOWPASS,
                firFreq1 = cutoffFreq
            ))
        } else if (!enabled) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(firEnabled = false))
        }
    }
    
    /**
     * Update high-pass filter
     */
    fun updateHighPassFilter(enabled: Boolean, cutoffFreq: Double) {
        val sampleRate = fileInfo.value?.sampleRate ?: 250
        val nyquistFreq = sampleRate / 2.0
        if (enabled && cutoffFreq > 0 && cutoffFreq < nyquistFreq) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                firEnabled = true,
                firType = com.brainwonderful.naoyu_bluetooth_android.filters.FilterType.HIGHPASS,
                firFreq1 = cutoffFreq
            ))
        } else if (!enabled) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(firEnabled = false))
        }
    }
    
    /**
     * Update display buffers with new processed data
     */
    private fun updateDisplayBuffers(data: ProcessedEEGData) {
        // Update AF7 buffer
        val newAf7Buffer = (_af7DisplayData.value + data.processedAF7.toList())
            .takeLast(maxDisplayPoints)
        _af7DisplayData.value = newAf7Buffer
        
        // Update AF8 buffer
        val newAf8Buffer = (_af8DisplayData.value + data.processedAF8.toList())
            .takeLast(maxDisplayPoints)
        _af8DisplayData.value = newAf8Buffer
    }
    
    /**
     * Update raw display buffers with original data
     */
    private fun updateRawDisplayBuffers(packet: EEGPacket) {
        // Update AF7 raw buffer
        val newAf7RawBuffer = (_af7RawDisplayData.value + packet.af7Data.toList())
            .takeLast(maxDisplayPoints)
        _af7RawDisplayData.value = newAf7RawBuffer
        
        // Update AF8 raw buffer
        val newAf8RawBuffer = (_af8RawDisplayData.value + packet.af8Data.toList())
            .takeLast(maxDisplayPoints)
        _af8RawDisplayData.value = newAf8RawBuffer
    }
    
    /**
     * Clear display buffers
     */
    private fun clearDisplayBuffers() {
        _af7DisplayData.value = emptyList()
        _af8DisplayData.value = emptyList()
        _af7RawDisplayData.value = emptyList()
        _af8RawDisplayData.value = emptyList()
    }
    
    /**
     * 开始实时频谱分析
     * 只有在正在播放时才允许进行频谱分析，使用当前采集的滤波参数
     */
    fun startSpectrumAnalysis() {
        if (playbackState.value == DataPlayer.PlaybackState.PLAYING) {
            _isAnalyzingSpectrum.value = true
            
            // 启用FFT处理，保持其他滤波参数不变
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                fftEnabled = true,
                fftSize = 512  // 使用512点FFT
            ))
            
            // 启动异步FFT处理
            eegDataProcessor.startAsyncFFT(viewModelScope)
            
            // 监听异步频谱数据
            spectrumAnalysisJob = viewModelScope.launch {
                eegDataProcessor.spectrumData.collect { (af7Spectrum, af8Spectrum) ->
                    if (_isAnalyzingSpectrum.value) {
                        _af7SpectrumData.value = af7Spectrum
                        _af8SpectrumData.value = af8Spectrum
                    }
                }
            }
            
            // 不清空频谱数据，避免界面闪烁，让新数据自然覆盖旧数据
        }
    }
    
    /**
     * 停止实时频谱分析
     */
    fun stopSpectrumAnalysis() {
        _isAnalyzingSpectrum.value = false
        spectrumAnalysisJob?.cancel()
        spectrumAnalysisJob = null
        
        // 停止异步FFT处理
        eegDataProcessor.stopAsyncFFT()
        
        // 禁用FFT处理以节省资源
        val current = _filterSettings.value
        updateFilterSettings(current.copy(fftEnabled = false))
    }
    
    /**
     * 更新频谱数据
     */
    private fun updateSpectrumData(data: ProcessedEEGData) {
        // 从ProcesEEGData中获取频谱数据
        data.af7Spectrum?.let { spectrum ->
            _af7SpectrumData.value = spectrum
        }
        
        data.af8Spectrum?.let { spectrum ->
            _af8SpectrumData.value = spectrum
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        stopSpectrumAnalysis()
        dataPlayer.cleanup()
        eegDataProcessor.cleanup()
    }
}