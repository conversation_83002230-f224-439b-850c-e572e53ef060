package com.brainwonderful.naoyu_bluetooth_android.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.brainwonderful.naoyu_bluetooth_android.NaoyuApplication
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.ConnectionState
import com.brainwonderful.naoyu_bluetooth_android.data.DataRecorder
import com.brainwonderful.naoyu_bluetooth_android.data.RawDataRecorder
import com.brainwonderful.naoyu_bluetooth_android.data.EEGDataProcessor
import com.brainwonderful.naoyu_bluetooth_android.data.ProcessedEEGData
import com.brainwonderful.naoyu_bluetooth_android.data.DeviceType
import com.brainwonderful.naoyu_bluetooth_android.data.SpectrumData
import com.brainwonderful.naoyu_bluetooth_android.config.EEGProcessingConfig
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay

/**
 * ViewModel for EEG data collection screen
 * Manages real-time EEG data display and filter settings
 */
class CollectViewModel(application: Application) : AndroidViewModel(application) {
    
    private val bluetoothManager = (application as NaoyuApplication).bluetoothManager
    private val dataRecorder = DataRecorder(application)
    private val rawDataRecorder = RawDataRecorder(application)
    
    // Connection state
    val connectionState: StateFlow<ConnectionState> = bluetoothManager.connectionState
    val connectedDevice = bluetoothManager.connectedDevice
    
    // Processed EEG data stream
    val processedEEGData = bluetoothManager.processedEEGData
    
    // Collection state
    private val _isCollecting = MutableStateFlow(false)
    val isCollecting: StateFlow<Boolean> = _isCollecting.asStateFlow()
    
    // Display buffers for waveform
    private val _af7DisplayData = MutableStateFlow<List<Double>>(emptyList())
    val af7DisplayData: StateFlow<List<Double>> = _af7DisplayData.asStateFlow()
    
    private val _af8DisplayData = MutableStateFlow<List<Double>>(emptyList())
    val af8DisplayData: StateFlow<List<Double>> = _af8DisplayData.asStateFlow()
    
    // Signal quality from latest packet
    private val _signalQuality = MutableStateFlow<Boolean?>(null)
    val signalQuality: StateFlow<Boolean?> = _signalQuality.asStateFlow()
    
    // Battery level
    private val _batteryLevel = MutableStateFlow<String>("")
    val batteryLevel: StateFlow<String> = _batteryLevel.asStateFlow()
    
    // Recording state - 独立的保存状态管理
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()
    
    val recordingStats = dataRecorder.recordingStats
    val rawRecordingStats = rawDataRecorder.recordingStats
    
    // Raw data recording state
    private val _isRawRecording = MutableStateFlow(false)
    val isRawRecording: StateFlow<Boolean> = _isRawRecording.asStateFlow()
    
    // Show save dialog
    private val _showSaveDialog = MutableStateFlow(false)
    val showSaveDialog: StateFlow<Boolean> = _showSaveDialog.asStateFlow()
    
    // Filter settings - 与DebugViewModel保持完全一致
    private val _filterSettings = MutableStateFlow(
        EEGDataProcessor.FilterSettings(
            notchEnabled = true,    // 默认开启50Hz陷波
            notchFreq = 50.0,
            firEnabled = true,      // 默认开启带通滤波
            firType = FilterType.BANDPASS,
            firFreq1 = 1.0,        // 默认1-35Hz
            firFreq2 = 35.0,
            fftEnabled = false,
            fftSize = 512,
            timeConstantEnabled = false, // 添加时间常数支持
            timeConstantValue = 0.3
        )
    )
    val filterSettings: StateFlow<EEGDataProcessor.FilterSettings> = _filterSettings.asStateFlow()
    
    // 设备类型和采样率
    private val _deviceType = MutableStateFlow(DeviceType.DBAY)
    val deviceType: StateFlow<DeviceType> = _deviceType.asStateFlow()
    
    // 显示缓冲区大小根据采样率动态计算，使用配置的显示缓冲区时间窗口
    private val maxDisplayPoints: Int
        get() = (deviceType.value.sampleRate * EEGProcessingConfig.BufferConfig.DISPLAY_BUFFER_TIME_SECONDS).toInt()
    
    // 实时频谱分析相关
    private val _isAnalyzingSpectrum = MutableStateFlow(false)
    val isAnalyzingSpectrum: StateFlow<Boolean> = _isAnalyzingSpectrum.asStateFlow()
    
    private val _af7SpectrumData = MutableStateFlow<SpectrumData?>(null)
    val af7SpectrumData: StateFlow<SpectrumData?> = _af7SpectrumData.asStateFlow()
    
    private val _af8SpectrumData = MutableStateFlow<SpectrumData?>(null)
    val af8SpectrumData: StateFlow<SpectrumData?> = _af8SpectrumData.asStateFlow()
    
    private var spectrumAnalysisJob: Job? = null
    
    init {
        // 监听连接设备变化以更新设备类型
        viewModelScope.launch {
            connectedDevice.collect { device ->
                if (device != null) {
                    _deviceType.value = DeviceType.fromDeviceName(device.name)
                }
            }
        }
        
        // Listen to processed EEG data
        viewModelScope.launch {
            processedEEGData.collect { data ->
                // 始终更新信号状态，无论是否在采集
                updateSignalStatus(data)
                
                // 只有在采集时才更新显示缓冲区
                if (_isCollecting.value) {
                    updateDisplayBuffers(data)
                }
                
                // 如果正在进行频谱分析，更新频谱数据
                if (_isAnalyzingSpectrum.value) {
                    updateSpectrumData(data)
                }
            }
        }
        
        // Listen to raw EEG data for recording - 只有在保存状态下才记录
        viewModelScope.launch {
            bluetoothManager.rawEEGData.collect { rawData ->
                if (_isRecording.value) {
                    dataRecorder.addData(rawData)
                }
                // 同时记录原始数据包（如果启用了原始数据记录）
                if (_isRawRecording.value) {
                    rawDataRecorder.addData(rawData)
                }
            }
        }
    }
    
    /**
     * Start data collection - 只控制显示，不自动开始录制
     */
    fun startCollection() {
        if (connectionState.value == ConnectionState.CONNECTED) {
            _isCollecting.value = true
            clearDisplayBuffers()
        }
    }
    
    /**
     * Stop data collection - 停止显示，如果正在保存则先停止保存
     */
    fun stopCollection() {
        _isCollecting.value = false
        
        // 停止采集时也停止频谱分析
        if (_isAnalyzingSpectrum.value) {
            stopSpectrumAnalysis()
        }
        
        // 如果正在保存，先停止保存并显示保存对话框
        if (_isRecording.value || _isRawRecording.value) {
            stopRecording()
        }
    }
    
    /**
     * Save recording with given filename and format
     * @param fileName The name of the file to save
     * @param saveFormat The format to save in: "neeg", "txt", "both", "raw", or "all"
     * @param includeRaw Whether to also save raw data packets in hex format
     */
    fun saveRecording(fileName: String, saveFormat: String = "neeg", includeRaw: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            // 保存处理后的数据
            when (saveFormat) {
                "neeg" -> dataRecorder.saveRecording(fileName, exportTxt = false)
                "txt" -> dataRecorder.saveRecording(fileName, exportTxt = true, onlyTxt = true)
                "both" -> dataRecorder.saveRecording(fileName, exportTxt = true)
                "raw" -> {
                    // 只保存原始数据
                    if (_isRawRecording.value) {
                        rawDataRecorder.saveRecording(fileName)
                    }
                }
                "all" -> {
                    // 保存所有格式
                    dataRecorder.saveRecording(fileName, exportTxt = true)
                    if (_isRawRecording.value) {
                        rawDataRecorder.saveRecording(fileName)
                    }
                }
            }
            
            // 如果额外指定了保存原始数据
            if (includeRaw && _isRawRecording.value && saveFormat != "raw" && saveFormat != "all") {
                rawDataRecorder.saveRecording(fileName)
            }
            
            _showSaveDialog.value = false
        }
    }
    
    /**
     * Discard current recording
     */
    fun discardRecording() {
        dataRecorder.discardRecording()
        rawDataRecorder.discardRecording()
        _showSaveDialog.value = false
    }
    
    /**
     * Update filter settings
     */
    fun updateFilterSettings(settings: EEGDataProcessor.FilterSettings) {
        _filterSettings.value = settings
        bluetoothManager.updateFilterSettings(settings)
    }
    
    /**
     * Toggle 50Hz notch filter
     */
    fun toggleNotchFilter(enabled: Boolean) {
        val current = _filterSettings.value
        updateFilterSettings(current.copy(notchEnabled = enabled))
    }
    
    /**
     * Update bandpass filter settings
     */
    fun updateBandpassFilter(enabled: Boolean, lowFreq: Double, highFreq: Double) {
        // Validate frequency parameters
        val nyquistFreq = deviceType.value.sampleRate / 2
        if (enabled && lowFreq > 0 && highFreq > lowFreq && highFreq < nyquistFreq) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                firEnabled = enabled,
                firType = FilterType.BANDPASS,
                firFreq1 = lowFreq,
                firFreq2 = highFreq
            ))
        } else if (!enabled) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(firEnabled = false))
        }
    }
    
    /**
     * Update time constant (high-pass filter) - 按照UniApp版本实现
     */
    fun updateTimeConstant(enabled: Boolean, timeConstant: Double) {
        val current = _filterSettings.value
        updateFilterSettings(current.copy(
            timeConstantEnabled = enabled,
            timeConstantValue = timeConstant
        ))
    }
    
    /**
     * Update low-pass filter
     */
    fun updateLowPassFilter(enabled: Boolean, cutoffFreq: Double) {
        val nyquistFreq = deviceType.value.sampleRate / 2
        if (enabled && cutoffFreq > 0 && cutoffFreq < nyquistFreq) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                firEnabled = true,
                firType = FilterType.LOWPASS,
                firFreq1 = cutoffFreq
            ))
        } else if (!enabled) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(firEnabled = false))
        }
    }
    
    /**
     * Update high-pass filter
     */
    fun updateHighPassFilter(enabled: Boolean, cutoffFreq: Double) {
        val nyquistFreq = deviceType.value.sampleRate / 2
        if (enabled && cutoffFreq > 0 && cutoffFreq < nyquistFreq) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                firEnabled = true,
                firType = FilterType.HIGHPASS,
                firFreq1 = cutoffFreq
            ))
        } else if (!enabled) {
            val current = _filterSettings.value
            updateFilterSettings(current.copy(firEnabled = false))
        }
    }
    
    /**
     * Update display buffers with new data
     */
    private fun updateDisplayBuffers(data: ProcessedEEGData) {
        // Update AF7 buffer
        val newAf7Buffer = (_af7DisplayData.value + data.processedAF7.toList())
            .takeLast(maxDisplayPoints)
        _af7DisplayData.value = newAf7Buffer
        
        // Update AF8 buffer
        val newAf8Buffer = (_af8DisplayData.value + data.processedAF8.toList())
            .takeLast(maxDisplayPoints)
        _af8DisplayData.value = newAf8Buffer
    }
    
    /**
     * Update signal status from packet
     */
    private fun updateSignalStatus(data: ProcessedEEGData) {
        _signalQuality.value = data.originalPacket.isSignalGood
        
        // Update battery level
        val battery = data.originalPacket.batteryLevel
        _batteryLevel.value = when {
            battery <= 100 -> "$battery%"
            battery == 128 -> "充电中"
            battery == 204 -> "充电完成"
            else -> "未知"
        }
    }
    
    /**
     * Clear display buffers
     */
    private fun clearDisplayBuffers() {
        _af7DisplayData.value = emptyList()
        _af8DisplayData.value = emptyList()
    }
    
    /**
     * Reset filters to default state
     */
    fun resetFilters() {
        bluetoothManager.resetFilters()
        clearDisplayBuffers()
    }
    
    /**
     * 开始实时频谱分析
     * 只有在正在采集时才允许进行频谱分析，使用当前采集的滤波参数
     */
    fun startSpectrumAnalysis() {
        if (connectionState.value == ConnectionState.CONNECTED && _isCollecting.value) {
            _isAnalyzingSpectrum.value = true
            
            // 启用FFT处理，保持其他滤波参数不变
            val current = _filterSettings.value
            updateFilterSettings(current.copy(
                fftEnabled = true,
                fftSize = 512  // 使用512点FFT
            ))
            
            // 不清空频谱数据，避免界面闪烁，让新数据自然覆盖旧数据
        }
    }
    
    /**
     * 停止实时频谱分析
     */
    fun stopSpectrumAnalysis() {
        _isAnalyzingSpectrum.value = false
        spectrumAnalysisJob?.cancel()
        spectrumAnalysisJob = null
        
        // 禁用FFT处理以节省资源
        val current = _filterSettings.value
        updateFilterSettings(current.copy(fftEnabled = false))
    }
    
    /**
     * 更新频谱数据
     */
    private fun updateSpectrumData(data: ProcessedEEGData) {
        // 从ProcessedEEGData中获取频谱数据
        data.af7Spectrum?.let { spectrum ->
            _af7SpectrumData.value = spectrum
        }
        
        data.af8Spectrum?.let { spectrum ->
            _af8SpectrumData.value = spectrum
        }
    }
    
    /**
     * 开始数据保存 - 独立的保存控制
     * @param includeRawData 是否同时记录原始数据包
     */
    fun startRecording(includeRawData: Boolean = true) {
        if (connectionState.value == ConnectionState.CONNECTED && _isCollecting.value && !_isRecording.value) {
            val deviceName = connectedDevice.value?.name ?: "Unknown Device"
            
            // 开始记录处理后的数据
            dataRecorder.startRecording(deviceName)
            _isRecording.value = true
            
            // 如果需要，同时开始记录原始数据包
            if (includeRawData) {
                rawDataRecorder.startRecording(deviceName)
                _isRawRecording.value = true
            }
        }
    }
    
    /**
     * 停止数据保存并显示保存对话框
     */
    fun stopRecording() {
        if (_isRecording.value || _isRawRecording.value) {
            if (_isRecording.value) {
                _isRecording.value = false
                dataRecorder.stopRecording()
            }
            
            if (_isRawRecording.value) {
                _isRawRecording.value = false
                rawDataRecorder.stopRecording()
            }
            
            _showSaveDialog.value = true
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        stopCollection()
        stopSpectrumAnalysis()
        if (_isRecording.value) {
            dataRecorder.stopRecording()
        }
        if (_isRawRecording.value) {
            rawDataRecorder.stopRecording()
        }
    }
}

// Re-export for convenience
typealias FilterType = com.brainwonderful.naoyu_bluetooth_android.filters.FilterType