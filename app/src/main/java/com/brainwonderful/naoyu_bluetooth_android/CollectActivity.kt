package com.brainwonderful.naoyu_bluetooth_android

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.brainwonderful.naoyu_bluetooth_android.ui.screens.CollectScreen
import com.brainwonderful.naoyu_bluetooth_android.ui.theme.NaoyubluetoothandroidTheme
import com.brainwonderful.naoyu_bluetooth_android.viewmodel.CollectViewModel

class CollectActivity : ComponentActivity() {
    
    private val collectViewModel: CollectViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            NaoyubluetoothandroidTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    CollectScreen(
                        viewModel = collectViewModel,
                        onBack = { finish() }
                    )
                }
            }
        }
    }
}