package com.brainwonderful.naoyu_bluetooth_android.config

/**
 * 脑电信号处理系统统一配置管理
 * 包含所有不在UI界面暴露但对计算和显示结果有重要影响的底层参数
 */
object EEGProcessingConfig {
    
    /**
     * 滤波器算法内部参数配置
     */
    object FilterConfig {
        /** 陷波滤波器Beta参数 - 控制滤波器品质因数，影响陷波深度和带宽 */
        const val NOTCH_FILTER_BETA = 0.96
        
        /** 陷波滤波器是否使用Beta参数（true=使用beta, false=使用Q值） */
        const val NOTCH_FILTER_USE_BETA = true
        
        /** FIR滤波器默认阶数 - 影响滤波精度和计算复杂度 */
        const val FIR_FILTER_DEFAULT_ORDER = 64
        
        /** 时间常数滤波器的RC时间常数默认值（秒） */
        const val TIME_CONSTANT_DEFAULT_VALUE = 0.03
        
        /** DC去除滤波器时间常数（秒） - 高通特性去除直流偏移 */
        const val DC_REMOVAL_TIME_CONSTANT = 0.5
        
        /** DC去除滤波器截止频率（Hz） - 计算公式: 1/(2*π*RC) */
        const val DC_REMOVAL_CUTOFF_FREQ = 0.318 // 约等于 1/(2*π*0.5)
    }
    
    /**
     * 数据缓冲区和时间窗口参数配置
     */
    object BufferConfig {
        /** 数据处理缓冲区时间窗口（秒） - 影响滤波器延迟和内存占用 */
        const val PROCESSING_BUFFER_TIME_SECONDS = 4.0
        
        /** 显示缓冲区时间窗口（秒） - 控制显示的历史数据长度 */
        const val DISPLAY_BUFFER_TIME_SECONDS = 5.0
        
        /** 走纸速度对应的显示时间窗口映射 */
        val PAPER_SPEED_TIME_WINDOWS = mapOf(
            "15mm/s" to 8,  // 8秒窗口 - 较慢速度显示更长时间
            "30mm/s" to 4,  // 4秒窗口 - 标准显示窗口
            "60mm/s" to 2   // 2秒窗口 - 快速显示较短时间
        )
        
        /** 默认显示时间窗口（秒） */
        const val DEFAULT_DISPLAY_TIME_WINDOW = 4
        
        /** 循环缓冲区扩展因子 - 防止缓冲区溢出的安全边界 */
        const val BUFFER_EXPANSION_FACTOR = 1.2
    }
    
    /**
     * FFT和频谱分析参数配置
     */
    object SpectrumConfig {
        /** FFT更新间隔（数据包数） - 控制频谱分析的计算频率 */
        const val FFT_UPDATE_INTERVAL = 2
        
        /** FFT窗口函数类型 - 影响频谱分析的频率分辨率和泄露 */
        const val FFT_WINDOW_TYPE = "HANN"  // 汉宁窗
        
        /** FFT重叠率 - 提高时频分辨率的重叠百分比 */
        const val FFT_OVERLAP_RATIO = 0.5
        
        /** 频谱分析的最小有效频率（Hz） - 过滤低频噪声 */
        const val SPECTRUM_MIN_FREQ = 0.5
        
        /** 频谱分析的最大有效频率（Hz） - 基于Nyquist频率 */
        const val SPECTRUM_MAX_FREQ_RATIO = 0.45  // 采样率的45%，避免混叠
        
        /** 频谱平滑系数 - 减少频谱波动的平滑因子 */
        const val SPECTRUM_SMOOTHING_FACTOR = 0.8
    }
    
    /**
     * 信号质量评估参数配置
     */
    object SignalQualityConfig {
        /** 信号质量判断的电压阈值（μV） - 超过此值认为脱落 */
        const val SIGNAL_DROPOUT_THRESHOLD_UV = 2000.0
        
        /** 信号质量评估的时间窗口（秒） - 统计分析窗口 */
        const val QUALITY_ASSESSMENT_WINDOW_SEC = 2.0
        
        /** 异常值检测的标准差倍数 - 超过N倍标准差视为异常 */
        const val OUTLIER_DETECTION_STD_MULTIPLIER = 3.0
        
        /** 信号变化检测的最小变化率 - 判断信号是否有效变化 */
        const val MIN_SIGNAL_CHANGE_RATE = 0.1
        
        /** 噪声水平评估的时间窗口（秒） */
        const val NOISE_ASSESSMENT_WINDOW_SEC = 1.0
        
        /** 基线漂移检测的时间窗口（秒） */
        const val BASELINE_DRIFT_WINDOW_SEC = 10.0
    }
    
    /**
     * 显示和渲染参数配置
     */
    object DisplayConfig {
        /** 波形图渲染帧率（FPS） - 控制UI刷新频率 */
        const val WAVEFORM_RENDER_FPS = 30
        
        /** 波形图抗锯齿开关 - 提高显示质量但消耗更多资源 */
        const val WAVEFORM_ANTIALIASING = true
        
        /** 波形线条宽度（dp） */
        const val WAVEFORM_STROKE_WIDTH = 1.5f
        
        /** 网格线透明度 */
        const val GRID_LINE_ALPHA = 0.3f
        
        /** 网格线宽度（dp） */
        const val GRID_LINE_WIDTH = 0.5f
        
        /** 坐标轴刻度间距的像素密度 */
        const val AXIS_TICK_DENSITY_DP = 50
        
        /** 波形图缩放系数范围 */
        const val WAVEFORM_MIN_ZOOM = 0.1
        const val WAVEFORM_MAX_ZOOM = 10.0
        
        /** 波形显示的垂直边距百分比 */
        const val WAVEFORM_VERTICAL_MARGIN_PERCENT = 0.1
    }
    
    /**
     * 性能优化参数配置
     */
    object PerformanceConfig {
        /** 数据处理线程池大小 */
        const val PROCESSING_THREAD_POOL_SIZE = 2
        
        /** 内存缓存最大占用（MB） */
        const val MAX_MEMORY_CACHE_MB = 50
        
        /** GC触发的缓冲区清理阈值 */
        const val GC_CLEANUP_THRESHOLD = 0.8
        
        /** 批量数据处理的最大批次大小 */
        const val MAX_BATCH_PROCESSING_SIZE = 1000
        
        /** 数据流背压处理的缓冲区大小 */
        const val FLOW_BUFFER_SIZE = 1000
        
        /** 异步处理的超时时间（毫秒） */
        const val ASYNC_PROCESSING_TIMEOUT_MS = 5000L
    }
    
    /**
     * 算法特定参数配置
     */
    object AlgorithmConfig {
        /** Douglas-Peucker算法的容差值 - 控制曲线简化程度 */
        const val DOUGLAS_PEUCKER_TOLERANCE = 0.5
        
        /** 边缘检测的梯度阈值 */
        const val EDGE_DETECTION_GRADIENT_THRESHOLD = 10.0
        
        /** 边缘检测的最小峰值间隔（数据点） */
        const val EDGE_DETECTION_MIN_PEAK_DISTANCE = 10
        
        /** 峰值检测的最小高度比例 */
        const val PEAK_DETECTION_MIN_HEIGHT_RATIO = 0.1
        
        /** 滤波器稳定性检查的收敛阈值 */
        const val FILTER_STABILITY_THRESHOLD = 1e-6
        
        /** 数值计算的最小精度阈值 */
        const val NUMERICAL_PRECISION_THRESHOLD = 1e-12
    }
    
    /**
     * 设备特定参数配置
     */
    object DeviceConfig {
        /** DBAY设备的数据包大小（字节） */
        const val DBAY_PACKET_SIZE = 244
        
        /** DBAY设备的信号部分大小（字节） */
        const val DBAY_SIGNAL_SIZE = 4
        
        /** DBAY设备的单通道数据大小（字节） */
        const val DBAY_CHANNEL_SIZE = 120
        
        /** 蓝牙连接超时时间（毫秒） */
        const val BLUETOOTH_CONNECTION_TIMEOUT_MS = 10000L
        
        /** 蓝牙数据接收超时时间（毫秒） */
        const val BLUETOOTH_DATA_TIMEOUT_MS = 3000L
        
        /** 设备心跳检测间隔（毫秒） */
        const val DEVICE_HEARTBEAT_INTERVAL_MS = 1000L
    }
    
    /**
     * 数据存储和导出参数配置
     */
    object StorageConfig {
        /** 数据文件的最大大小（MB） */
        const val MAX_DATA_FILE_SIZE_MB = 100
        
        /** 原始数据的压缩比例 */
        const val RAW_DATA_COMPRESSION_RATIO = 0.7
        
        /** 临时文件的清理时间（小时） */
        const val TEMP_FILE_CLEANUP_HOURS = 24
        
        /** 数据导出的批次大小 */
        const val EXPORT_BATCH_SIZE = 10000
        
        /** 文件写入缓冲区大小（字节） */
        const val FILE_WRITE_BUFFER_SIZE = 8192
    }
    
    /**
     * 调试和日志参数配置
     */
    object DebugConfig {
        /** 性能统计的采样间隔（毫秒） */
        const val PERFORMANCE_SAMPLING_INTERVAL_MS = 1000L
        
        /** 内存监控的检查间隔（毫秒） */
        const val MEMORY_MONITOR_INTERVAL_MS = 5000L
        
        /** 日志文件的最大大小（MB） */
        const val MAX_LOG_FILE_SIZE_MB = 10
        
        /** 调试模式下的详细日志级别 */
        const val DEBUG_LOG_LEVEL = "VERBOSE"
        
        /** 崩溃报告的上传延迟（毫秒） */
        const val CRASH_REPORT_DELAY_MS = 2000L
    }
}