package com.brainwonderful.naoyu_bluetooth_android

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.content.Intent
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.lifecycle.ViewModelProvider
import com.brainwonderful.naoyu_bluetooth_android.bluetooth.PermissionManager
import com.brainwonderful.naoyu_bluetooth_android.ui.screens.MainScreen
import com.brainwonderful.naoyu_bluetooth_android.ui.theme.NaoyubluetoothandroidTheme
import com.brainwonderful.naoyu_bluetooth_android.viewmodel.BluetoothViewModel

class MainActivity : ComponentActivity() {
    
    private lateinit var bluetoothViewModel: BluetoothViewModel
    
    // 蓝牙开启请求
    private val enableBluetoothLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            checkPermissionsAndStartScan()
        } else {
            Toast.makeText(this, "需要开启蓝牙才能使用本应用", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 权限请求
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            // 权限已授予，可以开始扫描
            bluetoothViewModel.startScan()
        } else {
            Toast.makeText(this, "需要蓝牙权限才能扫描设备", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        bluetoothViewModel = ViewModelProvider(this)[BluetoothViewModel::class.java]
        
        setContent {
            NaoyubluetoothandroidTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen(
                        viewModel = bluetoothViewModel,
                        onNavigateToCollect = {
                            val intent = Intent(this, CollectActivity::class.java)
                            bluetoothViewModel.connectedDevice.value?.let { device ->
                                intent.putExtra("device_name", device.name)
                                intent.putExtra("device_address", device.address)
                            }
                            startActivity(intent)
                        },
                        onNavigateToDebug = {
                            val intent = Intent(this, DebugActivity::class.java)
                            startActivity(intent)
                        },
                        onNavigateToExport = {
                            val intent = Intent(this, ExportActivity::class.java)
                            startActivity(intent)
                        },
                        onNavigateToPhysicalDesign = {
                            val intent = Intent(this, PhysicalDesignActivity::class.java)
                            startActivity(intent)
                        },
                        onRequestPermissions = { requestPermissions() },
                        onRequestEnableBluetooth = { requestEnableBluetooth() }
                    )
                }
            }
        }
    }
    
    /**
     * 请求开启蓝牙
     */
    private fun requestEnableBluetooth() {
        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        enableBluetoothLauncher.launch(enableBtIntent)
    }
    
    /**
     * 请求权限
     */
    private fun requestPermissions() {
        // 只在Android 11及以下版本检查位置服务
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            if (!isLocationEnabled()) {
                Toast.makeText(this, "请先开启位置服务以扫描BLE设备", Toast.LENGTH_LONG).show()
                // 打开位置设置
                startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                return
            }
        }
        
        val permissions = PermissionManager.getRequiredPermissions()
        permissionLauncher.launch(permissions.toTypedArray())
    }
    
    /**
     * 检查位置服务是否开启
     */
    private fun isLocationEnabled(): Boolean {
        val locationManager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || 
               locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
    }
    
    /**
     * 检查权限并开始扫描
     */
    private fun checkPermissionsAndStartScan() {
        if (PermissionManager.hasAllPermissions(this)) {
            bluetoothViewModel.startScan()
        } else {
            requestPermissions()
        }
    }
}