<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    
    <!-- 蓝牙权限 -->
    <!-- Android 11 及以下的蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
    
    <!-- Android 12 及以上的蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    
    <!-- 位置权限（Android 6.0 - 11 蓝牙扫描需要，Android 12+ 使用neverForLocation标志后不再需要） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" android:maxSdkVersion="30" />
    
    <!-- 网络权限（用于文件服务器） -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    
    <!-- 蓝牙特性声明 -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="true" />
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />

    <application
        android:name=".NaoyuApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Naoyubluetoothandroid"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Naoyubluetoothandroid"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".CollectActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified"
            android:theme="@style/Theme.Naoyubluetoothandroid" />
        <activity
            android:name=".DebugActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified"
            android:theme="@style/Theme.Naoyubluetoothandroid" />
        <activity
            android:name=".ExportActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified"
            android:theme="@style/Theme.Naoyubluetoothandroid" />
        <activity
            android:name=".PhysicalDesignActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="unspecified"
            android:theme="@style/Theme.Naoyubluetoothandroid" />
    </application>

</manifest>