package com.brainwonderful.naoyu_bluetooth_android.filters

import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.runner.RunWith
import com.brainwonderful.naoyu_bluetooth_android.utils.TestDataGenerator
import org.junit.Test
import org.junit.Assert.*
import kotlin.math.*

@RunWith(AndroidJUnit4::class)
class DouglasPeuckerTest {
    
    @Test
    fun testDouglasPeuckerSimpleLine() {
        val algorithm = DouglasPeucker()
        
        // 直线上的点，应该只保留端点
        val points = listOf(
            Point(0.0, 0.0),
            Point(1.0, 1.0),
            Point(2.0, 2.0),
            Point(3.0, 3.0),
            Point(4.0, 4.0)
        )
        
        val simplified = algorithm.simplify(points, 0.1)
        
        // 应该只保留起点和终点
        assertEquals("直线简化后点数不正确", 2, simplified.size)
        assertEquals("起点不正确", points.first(), simplified.first())
        assertEquals("终点不正确", points.last(), simplified.last())
    }
    
    @Test
    fun testDouglasPeuckerWithNoise() {
        val algorithm = DouglasPeucker()
        
        // 带小噪声的直线
        val points = listOf(
            Point(0.0, 0.0),
            Point(1.0, 1.01),   // 轻微偏离
            Point(2.0, 1.99),   // 轻微偏离
            Point(3.0, 3.02),   // 轻微偏离
            Point(4.0, 4.0)
        )
        
        // 使用较大的epsilon
        val simplified = algorithm.simplify(points, 0.1)
        
        // 噪声小于epsilon，应该被简化
        assertEquals("带噪声直线简化后点数不正确", 2, simplified.size)
    }
    
    @Test
    fun testDouglasPeuckerComplexCurve() {
        val algorithm = DouglasPeucker()
        
        // V形曲线
        val points = listOf(
            Point(0.0, 0.0),
            Point(1.0, -1.0),
            Point(2.0, -2.0),
            Point(3.0, -1.0),
            Point(4.0, 0.0)
        )
        
        // 使用小的epsilon保留曲线特征
        val simplified = algorithm.simplify(points, 0.5)
        
        // 应该保留V形的关键点
        assertTrue("V形曲线简化过度", simplified.size >= 3)
        assertTrue("未保留V形底部", simplified.any { it.x == 2.0 })
    }
    
    @Test
    fun testDouglasPeuckerWithSineWave() {
        val algorithm = DouglasPeucker()
        
        // 生成正弦波数据
        val sampleRate = 100.0
        val signal = TestDataGenerator.generateSineWave(1.0, sampleRate, 1.0)
        
        // 转换为Point列表
        val points = signal.mapIndexed { index, value ->
            Point(index.toDouble(), value)
        }
        
        // 不同epsilon值的测试
        val epsilonValues = doubleArrayOf(0.01, 0.1, 0.5)
        var previousSize = points.size
        
        epsilonValues.forEach { epsilon ->
            val simplified = algorithm.simplify(points, epsilon)
            
            // epsilon越大，简化程度越高
            assertTrue(
                "epsilon=$epsilon 时简化效果不正确",
                simplified.size < previousSize
            )
            previousSize = simplified.size
            
            // 始终保留端点
            assertEquals("起点丢失", points.first(), simplified.first())
            assertEquals("终点丢失", points.last(), simplified.last())
        }
    }
    
    @Test
    fun testDouglasPeuckerEpsilonEffect() {
        val algorithm = DouglasPeucker()
        
        // 生成复杂信号
        val signal = TestDataGenerator.generateEEGLikeSignal(250.0, 1.0)
        val points = signal.mapIndexed { index, value ->
            Point(index.toDouble(), value)
        }
        
        // 测试不同epsilon值
        val results = mutableMapOf<Double, Int>()
        val epsilons = doubleArrayOf(0.001, 0.01, 0.05, 0.1, 0.2, 0.5, 1.0)
        
        epsilons.forEach { epsilon ->
            val simplified = algorithm.simplify(points, epsilon)
            results[epsilon] = simplified.size
        }
        
        // 验证单调性：epsilon增大，点数减少
        val sortedEpsilons = results.keys.sorted()
        for (i in 0 until sortedEpsilons.size - 1) {
            val eps1 = sortedEpsilons[i]
            val eps2 = sortedEpsilons[i + 1]
            assertTrue(
                "epsilon增大时点数应该减少或相等",
                results[eps1]!! >= results[eps2]!!
            )
        }
    }
    
    @Test
    fun testDouglasPeuckerEdgeCases() {
        val algorithm = DouglasPeucker()
        
        // 空列表
        val emptyList = emptyList<Point>()
        val emptyResult = algorithm.simplify(emptyList, 0.1)
        assertEquals("空列表应返回空结果", 0, emptyResult.size)
        
        // 单点
        val singlePoint = listOf(Point(0.0, 0.0))
        val singleResult = algorithm.simplify(singlePoint, 0.1)
        assertEquals("单点应保持不变", singlePoint, singleResult)
        
        // 两点
        val twoPoints = listOf(
            Point(0.0, 0.0),
            Point(1.0, 1.0)
        )
        val twoResult = algorithm.simplify(twoPoints, 0.1)
        assertEquals("两点应保持不变", twoPoints, twoResult)
    }
    
    @Test
    fun testDouglasPeuckerZeroEpsilon() {
        val algorithm = DouglasPeucker()
        
        // epsilon为0时不应简化
        val points = listOf(
            Point(0.0, 0.0),
            Point(1.0, 0.1),
            Point(2.0, 0.0)
        )
        
        val simplified = algorithm.simplify(points, 0.0)
        
        // 所有点都应保留
        assertEquals("epsilon=0时不应简化", points.size, simplified.size)
    }
    
    @Test
    fun testDouglasPeuckerCircularPattern() {
        val algorithm = DouglasPeucker()
        
        // 生成圆形图案的点
        val numPoints = 100
        val points = (0 until numPoints).map { i ->
            val angle = 2.0 * PI * i / numPoints
            Point(
                cos(angle),
                sin(angle)
            )
        }
        
        // 简化圆形
        val simplified = algorithm.simplify(points, 0.1)
        
        // 应该保留圆的基本形状
        assertTrue("圆形简化过度", simplified.size >= 8) // 至少8个点能表示圆
        assertTrue("圆形简化不够", simplified.size < points.size * 0.5) // 应该有显著简化
        
        // 验证简化后的点仍在圆上（距离圆心约为1）
        for (point in simplified) {
            val distanceFromCenter = sqrt(point.x * point.x + point.y * point.y)
            assertTrue(
                "简化后的点偏离圆形: 距离=${distanceFromCenter}",
                abs(distanceFromCenter - 1.0) < 0.2 // 允许20%的误差
            )
        }
        
        // 验证保留了关键的象限点（Douglas-Peucker会保留曲率变化最大的点）
        // 圆形的关键点通常在0°, 90°, 180°, 270°附近
        val angles = simplified.map { point ->
            var angle = atan2(point.y, point.x)
            if (angle < 0) angle += 2 * PI
            angle
        }
        
        val keyAngles = listOf(0.0, PI/2, PI, 3*PI/2)
        var nearKeyAngles = 0
        
        for (keyAngle in keyAngles) {
            if (angles.any { abs(it - keyAngle) < PI/4 }) {
                nearKeyAngles++
            }
        }
        
        assertTrue(
            "未保留足够的关键象限点",
            nearKeyAngles >= 3 // 至少保留3个象限的关键点
        )
    }
    
    @Test
    fun testDouglasPeuckerPerformance() {
        val algorithm = DouglasPeucker()
        
        // 测试大数据集
        val largeDataset = TestDataGenerator.generateMixedSineWave(
            doubleArrayOf(1.0, 5.0, 10.0),
            doubleArrayOf(1.0, 0.5, 0.3),
            1000.0,
            10.0 // 10秒数据，10000个点
        )
        
        val points = largeDataset.mapIndexed { index, value ->
            Point(index.toDouble(), value)
        }
        
        val startTime = System.nanoTime()
        val simplified = algorithm.simplify(points, 0.1)
        val endTime = System.nanoTime()
        
        val processingTimeMs = (endTime - startTime) / 1_000_000.0
        
        // 验证性能（递归算法应在合理时间内完成）
        assertTrue(
            "处理时间过长: $processingTimeMs ms",
            processingTimeMs < 1000.0 // 1秒内处理10000个点
        )
        
        // 验证简化效果
        val reductionRate = 1.0 - simplified.size.toDouble() / points.size
        assertTrue(
            "简化率: ${reductionRate * 100}%",
            reductionRate > 0.5 // 至少减少50%的点
        )
    }
    
    @Test
    fun testDouglasPeuckerAccuracy() {
        val algorithm = DouglasPeucker()
        
        // 生成已知形状测试精度
        val points = listOf(
            Point(0.0, 0.0),
            Point(1.0, 0.0),
            Point(2.0, 1.0),
            Point(3.0, 0.0),
            Point(4.0, 0.0)
        )
        
        val epsilon = 0.5
        val simplified = algorithm.simplify(points, epsilon)
        
        // 验证简化后的曲线与原始曲线的最大偏差
        var maxDeviation = 0.0
        
        for (i in 0 until points.size) {
            val originalPoint = points[i]
            
            // 找到简化曲线上最近的线段
            var minDistance = Double.MAX_VALUE
            
            for (j in 0 until simplified.size - 1) {
                val distance = calculatePointToLineDistance(
                    originalPoint,
                    simplified[j],
                    simplified[j + 1]
                )
                minDistance = minOf(minDistance, distance)
            }
            
            maxDeviation = maxOf(maxDeviation, minDistance)
        }
        
        // 最大偏差不应超过epsilon
        assertTrue(
            "最大偏差 $maxDeviation 超过epsilon $epsilon",
            maxDeviation <= epsilon * 1.1 // 允许10%的误差
        )
    }
    
    @Test
    fun testDouglasPeuckerWithRealEEGData() {
        val algorithm = DouglasPeucker()
        
        // 使用真实的EEG数据特征
        val eegSignal = TestDataGenerator.generateEEGLikeSignal(250.0, 2.0)
        val points = eegSignal.mapIndexed { index, value ->
            Point(index.toDouble(), value)
        }
        
        // 为EEG数据选择合适的epsilon
        val epsilon = 0.05 // 基于EEG信号的典型振幅
        val simplified = algorithm.simplify(points, epsilon)
        
        // 计算简化前后的关键统计特征
        val originalMean = points.map { it.y }.average()
        val simplifiedMean = simplified.map { it.y }.average()
        
        val originalStd = calculateStandardDeviation(points.map { it.y })
        val simplifiedStd = calculateStandardDeviation(simplified.map { it.y })
        
        // 验证统计特征保持
        assertEquals("均值变化过大", originalMean, simplifiedMean, 0.01)
        assertEquals("标准差变化过大", originalStd, simplifiedStd, originalStd * 0.1)
        
        // 验证数据压缩率
        val compressionRate = 1.0 - simplified.size.toDouble() / points.size
        assertTrue(
            "EEG数据压缩率: ${compressionRate * 100}%",
            compressionRate > 0.3 && compressionRate < 0.9
        )
    }
    
    @Test
    fun testDouglasPeuckerWithIndices() {
        val algorithm = DouglasPeucker()
        
        // 测试 simplifyIndices 方法
        val points = listOf(
            Point(0.0, 0.0),
            Point(1.0, 0.5),
            Point(2.0, 1.0),
            Point(3.0, 0.5),
            Point(4.0, 0.0)
        )
        
        val indices = algorithm.simplifyIndices(points, 0.3)
        
        // 验证返回的是有效的索引
        assertTrue("索引数量应该少于原始点数", indices.size < points.size)
        indices.forEach { index ->
            assertTrue("索引越界", index >= 0 && index < points.size)
        }
        
        // 验证起点和终点被保留
        assertTrue("起点索引未保留", indices.contains(0))
        assertTrue("终点索引未保留", indices.contains(points.size - 1))
    }
    
    // 辅助函数：计算点到线段的距离
    private fun calculatePointToLineDistance(
        point: Point,
        lineStart: Point,
        lineEnd: Point
    ): Double {
        val dx = lineEnd.x - lineStart.x
        val dy = lineEnd.y - lineStart.y
        
        if (dx == 0.0 && dy == 0.0) {
            // 线段退化为点
            return sqrt(
                (point.x - lineStart.x).pow(2) + 
                (point.y - lineStart.y).pow(2)
            )
        }
        
        val t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / 
                (dx * dx + dy * dy)
        
        val tClamped = maxOf(0.0, minOf(1.0, t))
        
        val closestX = lineStart.x + tClamped * dx
        val closestY = lineStart.y + tClamped * dy
        
        return sqrt(
            (point.x - closestX).pow(2) + 
            (point.y - closestY).pow(2)
        )
    }
    
    // 计算标准差
    private fun calculateStandardDeviation(values: List<Double>): Double {
        val mean = values.average()
        val variance = values.map { (it - mean).pow(2) }.average()
        return sqrt(variance)
    }
}