package com.brainwonderful.naoyu_bluetooth_android.filters

import androidx.test.ext.junit.runners.AndroidJUnit4
import com.brainwonderful.naoyu_bluetooth_android.utils.TestDataGenerator
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import kotlin.math.*

@RunWith(AndroidJUnit4::class)
class EdgeDetectionTest {
    
    @Test
    fun testEdgeDetectionSimpleSquareWave() {
        val detector = EdgeDetection()
        
        // 生成简单方波信号
        val signal = doubleArrayOf(
            -1.0, -1.0, -1.0, -1.0, // 负值
            1.0, 1.0, 1.0, 1.0,      // 正值
            -1.0, -1.0, -1.0, -1.0,  // 负值
            1.0, 1.0, 1.0, 1.0       // 正值
        )
        
        // 检测上升沿和下降沿
        val risingEdges = detector.detectEdges(signal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(signal, EdgeType.DESCENDING)
        
        // 预期边缘位置
        val expectedRisingEdges = intArrayOf(4, 12)  // 从负到正
        val expectedFallingEdges = intArrayOf(8)     // 从正到负
        
        assertArrayEquals("上升沿位置不正确", expectedRisingEdges, risingEdges)
        assertArrayEquals("下降沿位置不正确", expectedFallingEdges, fallingEdges)
    }
    
    @Test
    fun testEdgeDetectionWithSineWave() {
        val detector = EdgeDetection()
        
        // 生成正弦波信号，使用-π/2相位确保从负值开始
        val sampleRate = 100.0
        val frequency = 2.0 // 2Hz
        val duration = 2.0  // 2秒
        val signal = TestDataGenerator.generateSineWave(frequency, sampleRate, duration, 1.0, -PI/2)
        
        // 检测边缘
        val risingEdges = detector.detectEdges(signal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(signal, EdgeType.DESCENDING)
        
        // 验证信号从负值开始
        assertTrue("信号应从负值开始", signal[0] < 0.0)
        
        // 2Hz信号在2秒内应该有4个上升沿和4个下降沿
        assertEquals("上升沿数量不正确", 4, risingEdges.size)
        assertEquals("下降沿数量不正确", 4, fallingEdges.size)
        
        // 验证边缘间隔（应该约为半个周期）
        val halfPeriodSamples = (sampleRate / frequency / 2).toInt()
        for (i in 0 until risingEdges.size - 1) {
            val interval = risingEdges[i + 1] - risingEdges[i]
            // 使用整数范围验证，因为边缘检测可能有1-2个样本的误差
            assertTrue(
                "上升沿间隔不正确: 期望约${halfPeriodSamples * 2}，实际$interval",
                interval in (halfPeriodSamples * 2 - 2)..(halfPeriodSamples * 2 + 2)
            )
        }
    }
    
    @Test
    fun testEdgeDetectionWithNoisySignal() {
        val detector = EdgeDetection()
        
        // 生成带噪声的方波
        val cleanSquare = TestDataGenerator.generateSquareWave(5.0, 100.0, 1.0)
        val noise = TestDataGenerator.generateWhiteNoise(100.0, 1.0, 0.1)
        val noisySignal = DoubleArray(cleanSquare.size) { i ->
            cleanSquare[i] + noise[i]
        }
        
        // 检测边缘
        val noisyRisingEdges = detector.detectEdges(noisySignal, EdgeType.RISING)
        val noisyFallingEdges = detector.detectEdges(noisySignal, EdgeType.DESCENDING)
        val cleanRisingEdges = detector.detectEdges(cleanSquare, EdgeType.RISING)
        val cleanFallingEdges = detector.detectEdges(cleanSquare, EdgeType.DESCENDING)
        
        // 噪声可能导致额外的边缘检测
        val totalNoisyEdges = noisyRisingEdges.size + noisyFallingEdges.size
        val totalCleanEdges = cleanRisingEdges.size + cleanFallingEdges.size
        
        // 但主要边缘应该仍然被检测到
        // 验证主要边缘的时间差异在合理范围内
        var matchedEdges = 0
        for (cleanIdx in cleanRisingEdges) {
            if (noisyRisingEdges.any { abs(it - cleanIdx) <= 2 }) {
                matchedEdges++
            }
        }
        
        assertTrue(
            "主要上升沿检测率过低",
            matchedEdges >= cleanRisingEdges.size * 0.8
        )
    }
    
    @Test
    fun testEdgeDetectionAtZeroCrossing() {
        val detector = EdgeDetection()
        
        // 精确的过零点测试
        val signal = doubleArrayOf(
            -0.5, -0.3, -0.1, 0.0, 0.1, 0.3, 0.5,    // 上升过零
            0.5, 0.3, 0.1, 0.0, -0.1, -0.3, -0.5     // 下降过零
        )
        
        val risingEdges = detector.detectEdges(signal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(signal, EdgeType.DESCENDING)
        
        // 应该检测到边缘
        assertEquals("上升沿数量不正确", 1, risingEdges.size)
        assertEquals("下降沿数量不正确", 1, fallingEdges.size)
        
        // 验证位置（在0.0或刚过0.0的位置）
        assertTrue("上升沿位置不正确", risingEdges[0] in 3..4)
        assertTrue("下降沿位置不正确", fallingEdges[0] in 10..11)
    }
    
    @Test
    fun testEdgeDetectionWithDCOffset() {
        val detector = EdgeDetection()
        
        // 生成带DC偏移的正弦波，调整起始相位确保从负值开始
        val sampleRate = 500.0
        val frequency = 2.0
        val duration = 2.0
        // 使用-π/2相位让信号从-1开始，确保有明确的过零点
        val signal = DoubleArray((sampleRate * duration).toInt()) { i ->
            sin(2.0 * PI * frequency * i / sampleRate - PI / 2)
        }
        val signalWithDC = TestDataGenerator.addDCOffset(signal, 2.0)
        
        val edgesOriginal = detector.detectEdges(signal, EdgeType.RISING)
        val edgesWithDC = detector.detectEdges(signalWithDC, EdgeType.RISING)
        
        // 验证信号确实从负值开始
        assertTrue("信号应从负值开始", signal[0] < 0.0)
        
        // 2Hz信号在2秒内应该有4个上升过零点
        assertEquals("原始信号边缘数", 4, edgesOriginal.size)
        assertEquals("DC偏移信号不应有边缘", 0, edgesWithDC.size)
        
        // 验证边缘位置的合理性：上升沿间隔应该是一个完整周期(500Hz/2Hz=250)
        val expectedInterval = sampleRate / frequency  // 完整周期间隔
        for (i in 0 until edgesOriginal.size - 1) {
            val interval = edgesOriginal[i + 1] - edgesOriginal[i]
            assertTrue(
                "边缘间隔异常: 期望约$expectedInterval，实际$interval",
                abs(interval - expectedInterval) < 10  // 允许±10样本误差
            )
        }
    }
    
    @Test
    fun testEdgeDetectionWithTriangleWave() {
        val detector = EdgeDetection()
        
        // 生成三角波，手动调整起始相位确保从负值开始
        val sampleRate = 100.0
        val frequency = 2.0
        val duration = 1.0
        val numSamples = (sampleRate * duration).toInt()
        val period = sampleRate / frequency
        
        // 从相位0.75开始，对应三角波的负值部分
        val signal = DoubleArray(numSamples) { i ->
            val phase = ((i + period * 0.75) % period) / period
            when {
                phase < 0.25 -> 4.0 * phase
                phase < 0.75 -> 2.0 - 4.0 * phase
                else -> 4.0 * phase - 4.0
            }
        }
        
        val risingEdges = detector.detectEdges(signal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(signal, EdgeType.DESCENDING)
        
        // 验证信号从负值开始
        assertTrue("三角波应从负值开始", signal[0] < 0.0)
        
        // 2Hz三角波在1秒内应该有2个上升沿和2个下降沿
        assertEquals("上升沿数量", 2, risingEdges.size)
        assertEquals("下降沿数量", 2, fallingEdges.size)
    }
    
    @Test
    fun testEdgeDetectionEmptySignal() {
        val detector = EdgeDetection()
        
        // 空信号
        val emptySignal = doubleArrayOf()
        val risingEdges = detector.detectEdges(emptySignal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(emptySignal, EdgeType.DESCENDING)
        
        assertEquals("空信号不应有上升沿", 0, risingEdges.size)
        assertEquals("空信号不应有下降沿", 0, fallingEdges.size)
    }
    
    @Test
    fun testEdgeDetectionConstantSignal() {
        val detector = EdgeDetection()
        
        // 常数信号（无变化）
        val constantSignal = DoubleArray(100) { 5.0 }
        val risingEdges = detector.detectEdges(constantSignal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(constantSignal, EdgeType.DESCENDING)
        
        assertEquals("常数信号不应有上升沿", 0, risingEdges.size)
        assertEquals("常数信号不应有下降沿", 0, fallingEdges.size)
        
        // 零信号
        val zeroSignal = DoubleArray(100) { 0.0 }
        val zeroRisingEdges = detector.detectEdges(zeroSignal, EdgeType.RISING)
        val zeroFallingEdges = detector.detectEdges(zeroSignal, EdgeType.DESCENDING)
        
        assertEquals("零信号不应有上升沿", 0, zeroRisingEdges.size)
        assertEquals("零信号不应有下降沿", 0, zeroFallingEdges.size)
    }
    
    @Test
    fun testEdgeDetectionWithChirpSignal() {
        val detector = EdgeDetection()
        
        // 生成线性调频信号（频率从1Hz增加到10Hz）
        val signal = TestDataGenerator.generateChirp(1.0, 10.0, 200.0, 2.0)
        
        val risingEdges = detector.detectEdges(signal, EdgeType.RISING)
        
        // 频率增加，边缘间隔应该逐渐减小
        val intervals = mutableListOf<Int>()
        for (i in 0 until risingEdges.size - 1) {
            intervals.add(risingEdges[i + 1] - risingEdges[i])
        }
        
        // 检查间隔是否大致递减
        var decreasingCount = 0
        for (i in 0 until intervals.size - 1) {
            if (intervals[i] > intervals[i + 1]) {
                decreasingCount++
            }
        }
        
        assertTrue(
            "Chirp信号的边缘间隔应该递减",
            decreasingCount > intervals.size * 0.7
        )
    }
    
    @Test
    fun testEdgeDetectionWithImpulse() {
        val detector = EdgeDetection()
        
        // 生成脉冲信号
        val signal = DoubleArray(100) { -1.0 }
        signal[25] = 1.0  // 单个正脉冲
        signal[50] = 1.0  // 另一个正脉冲
        signal[75] = 1.0  // 第三个正脉冲
        
        val risingEdges = detector.detectEdges(signal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(signal, EdgeType.DESCENDING)
        
        // 每个脉冲应该产生一个上升沿和一个下降沿
        assertEquals("上升沿总数", 3, risingEdges.size)
        assertEquals("下降沿总数", 3, fallingEdges.size)
        
        // 验证上升沿位置
        assertTrue("第一个上升沿位置", risingEdges.contains(25))
        assertTrue("第二个上升沿位置", risingEdges.contains(50))
        assertTrue("第三个上升沿位置", risingEdges.contains(75))
    }
    
    @Test
    fun testEdgeDetectionPerformance() {
        val detector = EdgeDetection()
        
        // 测试大数据集的性能
        val largeSignal = TestDataGenerator.generateMixedSineWave(
            doubleArrayOf(1.0, 5.0, 10.0, 20.0),
            doubleArrayOf(1.0, 0.8, 0.6, 0.4),
            1000.0,
            10.0 // 10秒数据
        )
        
        val startTime = System.nanoTime()
        val risingEdges = detector.detectEdges(largeSignal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(largeSignal, EdgeType.DESCENDING)
        val endTime = System.nanoTime()
        
        val processingTimeMs = (endTime - startTime) / 1_000_000.0
        
        // 边缘检测应该很快（线性时间复杂度）
        assertTrue(
            "处理时间过长: $processingTimeMs ms",
            processingTimeMs < 100.0 // 100ms内处理10秒数据
        )
        
        // 验证检测到合理数量的边缘
        val totalEdges = risingEdges.size + fallingEdges.size
        assertTrue("边缘数量异常", totalEdges > 0 && totalEdges < largeSignal.size / 10)
    }
    
    @Test
    fun testEdgeDetectionWithRealEEGSignal() {
        val detector = EdgeDetection()
        
        // 生成模拟EEG信号
        val eegSignal = TestDataGenerator.generateEEGLikeSignal(250.0, 4.0)
        
        val risingEdges = detector.detectEdges(eegSignal, EdgeType.RISING)
        val fallingEdges = detector.detectEdges(eegSignal, EdgeType.DESCENDING)
        
        // EEG信号应该有合理数量的边缘
        val totalEdges = risingEdges.size + fallingEdges.size
        val edgeRate = totalEdges.toDouble() / eegSignal.size
        
        // 边缘率应该在合理范围内（不太高也不太低）
        assertTrue(
            "EEG信号边缘率异常: $edgeRate",
            edgeRate > 0.01 && edgeRate < 0.5
        )
        
        // 上升沿和下降沿应该大致平衡
        val balance = abs(risingEdges.size - fallingEdges.size).toDouble() / totalEdges
        assertTrue(
            "上升沿和下降沿不平衡: rising=${risingEdges.size}, falling=${fallingEdges.size}",
            balance < 0.1
        )
    }
    
    @Test
    fun testEdgeDetectionEdgeTypes() {
        val detector = EdgeDetection()
        
        // 测试信号，包含明确的上升沿和下降沿
        val signal = doubleArrayOf(
            -1.0, -0.5, 0.0, 0.5, 1.0,     // 上升
            1.0, 0.5, 0.0, -0.5, -1.0,      // 下降
            -1.0, -0.5, 0.0, 0.5, 1.0       // 再次上升
        )
        
        // 分别检测不同类型的边缘
        val risingOnly = detector.detectEdges(signal, EdgeType.RISING)
        val fallingOnly = detector.detectEdges(signal, EdgeType.DESCENDING)
        
        // 验证只检测到指定类型的边缘
        assertEquals("上升沿数量", 2, risingOnly.size)
        assertEquals("下降沿数量", 1, fallingOnly.size)
        
        // 验证边缘位置正确
        assertTrue("第一个上升沿位置", risingOnly[0] in 2..3)
        assertTrue("下降沿位置", fallingOnly[0] in 7..8)
        assertTrue("第二个上升沿位置", risingOnly[1] in 12..13)
    }
}