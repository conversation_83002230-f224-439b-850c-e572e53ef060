package com.brainwonderful.naoyu_bluetooth_android.utils

import kotlin.math.PI
import kotlin.math.sin
import kotlin.random.Random

/**
 * 测试数据生成工具类
 * 用于生成各种标准测试信号，模拟脑电信号特征
 */
object TestDataGenerator {
    
    /**
     * 生成正弦波信号
     * @param frequency 频率 (Hz)
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     * @param amplitude 振幅
     * @param phase 初相位 (弧度)
     */
    fun generateSineWave(
        frequency: Double,
        sampleRate: Double,
        duration: Double,
        amplitude: Double = 1.0,
        phase: Double = 0.0
    ): DoubleArray {
        val numSamples = (sampleRate * duration).toInt()
        return DoubleArray(numSamples) { i ->
            amplitude * sin(2.0 * PI * frequency * i / sampleRate + phase)
        }
    }
    
    /**
     * 生成混合正弦波信号
     * @param frequencies 频率数组 (Hz)
     * @param amplitudes 振幅数组
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     */
    fun generateMixedSineWave(
        frequencies: DoubleArray,
        amplitudes: DoubleArray,
        sampleRate: Double,
        duration: Double
    ): DoubleArray {
        require(frequencies.size == amplitudes.size) { "频率和振幅数组长度必须相同" }
        
        val numSamples = (sampleRate * duration).toInt()
        return DoubleArray(numSamples) { i ->
            frequencies.indices.sumOf { j ->
                amplitudes[j] * sin(2.0 * PI * frequencies[j] * i / sampleRate)
            }
        }
    }
    
    /**
     * 生成白噪声
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     * @param amplitude 振幅
     */
    fun generateWhiteNoise(
        sampleRate: Double,
        duration: Double,
        amplitude: Double = 1.0
    ): DoubleArray {
        val numSamples = (sampleRate * duration).toInt()
        val random = Random(42) // 固定种子以保证可重复性
        return DoubleArray(numSamples) {
            amplitude * (random.nextDouble() * 2.0 - 1.0) // 范围 [-amplitude, amplitude]
        }
    }
    
    /**
     * 生成含噪声的正弦波
     * @param frequency 正弦波频率 (Hz)
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     * @param signalAmplitude 信号振幅
     * @param noiseAmplitude 噪声振幅
     */
    fun generateNoisySineWave(
        frequency: Double,
        sampleRate: Double,
        duration: Double,
        signalAmplitude: Double = 1.0,
        noiseAmplitude: Double = 0.1
    ): DoubleArray {
        val sineWave = generateSineWave(frequency, sampleRate, duration, signalAmplitude)
        val noise = generateWhiteNoise(sampleRate, duration, noiseAmplitude)
        return DoubleArray(sineWave.size) { i -> sineWave[i] + noise[i] }
    }
    
    /**
     * 生成方波信号
     * @param frequency 频率 (Hz)
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     * @param amplitude 振幅
     * @param dutyCycle 占空比 (0-1)
     */
    fun generateSquareWave(
        frequency: Double,
        sampleRate: Double,
        duration: Double,
        amplitude: Double = 1.0,
        dutyCycle: Double = 0.5
    ): DoubleArray {
        val numSamples = (sampleRate * duration).toInt()
        val period = sampleRate / frequency
        return DoubleArray(numSamples) { i ->
            if ((i % period) / period < dutyCycle) amplitude else -amplitude
        }
    }
    
    /**
     * 生成三角波信号
     * @param frequency 频率 (Hz)
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     * @param amplitude 振幅
     */
    fun generateTriangleWave(
        frequency: Double,
        sampleRate: Double,
        duration: Double,
        amplitude: Double = 1.0
    ): DoubleArray {
        val numSamples = (sampleRate * duration).toInt()
        val period = sampleRate / frequency
        return DoubleArray(numSamples) { i ->
            val phase = (i % period) / period
            when {
                phase < 0.25 -> amplitude * (4.0 * phase)
                phase < 0.75 -> amplitude * (2.0 - 4.0 * phase)
                else -> amplitude * (4.0 * phase - 4.0)
            }
        }
    }
    
    /**
     * 生成脉冲信号
     * @param positions 脉冲位置数组 (样本索引)
     * @param totalSamples 总样本数
     * @param amplitude 脉冲振幅
     */
    fun generateImpulse(
        positions: IntArray,
        totalSamples: Int,
        amplitude: Double = 1.0
    ): DoubleArray {
        val signal = DoubleArray(totalSamples)
        positions.forEach { pos ->
            if (pos in 0 until totalSamples) {
                signal[pos] = amplitude
            }
        }
        return signal
    }
    
    /**
     * 生成模拟脑电信号 (EEG-like signal)
     * 包含 Alpha (8-13Hz), Beta (13-30Hz), Theta (4-8Hz), Delta (0.5-4Hz) 波段
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     */
    fun generateEEGLikeSignal(
        sampleRate: Double,
        duration: Double
    ): DoubleArray {
        val frequencies = doubleArrayOf(
            2.0,    // Delta
            6.0,    // Theta
            10.0,   // Alpha
            20.0,   // Beta
            50.0    // 工频干扰
        )
        val amplitudes = doubleArrayOf(
            0.4,    // Delta
            0.3,    // Theta
            0.5,    // Alpha
            0.2,    // Beta
            0.15    // 工频干扰
        )
        
        val signal = generateMixedSineWave(frequencies, amplitudes, sampleRate, duration)
        val noise = generateWhiteNoise(sampleRate, duration, 0.1)
        
        return DoubleArray(signal.size) { i -> signal[i] + noise[i] }
    }
    
    /**
     * 添加DC偏移
     * @param signal 原始信号
     * @param dcOffset DC偏移量
     */
    fun addDCOffset(signal: DoubleArray, dcOffset: Double): DoubleArray {
        return signal.map { it + dcOffset }.toDoubleArray()
    }
    
    /**
     * 生成线性扫频信号 (Chirp)
     * @param startFreq 起始频率 (Hz)
     * @param endFreq 结束频率 (Hz)
     * @param sampleRate 采样率 (Hz)
     * @param duration 持续时间 (秒)
     * @param amplitude 振幅
     */
    fun generateChirp(
        startFreq: Double,
        endFreq: Double,
        sampleRate: Double,
        duration: Double,
        amplitude: Double = 1.0
    ): DoubleArray {
        val numSamples = (sampleRate * duration).toInt()
        val freqRate = (endFreq - startFreq) / duration
        
        return DoubleArray(numSamples) { i ->
            val t = i / sampleRate
            val instantFreq = startFreq + freqRate * t
            amplitude * sin(2.0 * PI * (startFreq * t + freqRate * t * t / 2.0))
        }
    }
}