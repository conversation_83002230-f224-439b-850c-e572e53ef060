# 数据导出功能使用说明

## 功能概述

新增的数据导出功能允许用户通过内网HTTP服务器的方式，在电脑端浏览器中查看、下载和管理手机上保存的数据文件。

## 使用步骤

### 1. 进入导出页面
- 在应用首页右上角，点击"导出"按钮（📤图标）
- 进入数据导出页面

### 2. 启动文件服务器
- 在导出页面中，点击"启动服务器"按钮
- 系统会自动获取手机的内网IP地址
- 服务器启动后会显示访问地址，例如：`http://*************:8080`

### 3. 电脑端访问
- 确保电脑和手机连接在同一个WiFi网络
- 在电脑浏览器中输入显示的访问地址
- 即可看到文件管理页面

### 4. 文件操作
在浏览器页面中可以进行以下操作：
- **查看文件列表**：显示所有已保存的数据文件
- **下载文件**：点击"📥 下载"按钮下载文件到电脑
- **删除文件**：点击"🗑️ 删除"按钮删除不需要的文件
- **刷新列表**：点击"🔄 刷新"按钮更新文件列表
- **自动刷新**：页面每30秒自动刷新一次

## 支持的文件类型

- **NEEG文件**（.neeg）：原始二进制数据文件
- **TXT文件**（.txt）：解析后的文本数据文件
- **RAW文件**（_raw.txt）：原始十六进制数据文件
- **JSON文件**（.json）：元数据文件

## 注意事项

1. **网络要求**：手机和电脑必须连接在同一个WiFi网络
2. **防火墙**：确保电脑防火墙允许访问8080端口
3. **服务器状态**：使用完毕后建议停止服务器以节省电量
4. **文件安全**：删除操作不可撤销，请谨慎操作

## 技术实现

- **HTTP服务器**：基于Java Socket实现的轻量级HTTP服务器
- **文件管理**：支持文件列表、下载、删除等操作
- **响应式设计**：支持手机和电脑浏览器访问
- **实时更新**：自动刷新功能保持文件列表最新

## 故障排除

### 无法访问服务器
1. 检查手机和电脑是否在同一WiFi网络
2. 确认IP地址是否正确
3. 检查防火墙设置
4. 尝试重启服务器

### 文件下载失败
1. 检查文件是否存在
2. 确认网络连接稳定
3. 尝试刷新页面后重新下载

### 页面显示异常
1. 尝试刷新浏览器页面
2. 清除浏览器缓存
3. 使用其他浏览器尝试

## 更新日志

### v1.0.0
- ✅ 新增数据导出功能
- ✅ 实现HTTP文件服务器
- ✅ 支持文件列表查看
- ✅ 支持文件下载和删除
- ✅ 响应式Web界面设计
- ✅ 自动刷新功能
