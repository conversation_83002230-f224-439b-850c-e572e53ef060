# 原始蓝牙数据包记录功能测试说明

## 功能概述

现在的数据采集界面支持以下功能：

1. **处理后数据保存**：保存经过滤波处理的EEG数据（NEEG和TXT格式）
2. **原始数据包保存**：保存每次接收的原始蓝牙数据包，每个字节用十六进制空格隔开，每个包一行

## 实现的功能

### 1. RawDataRecorder类
- 位置：`app/src/main/java/com/brainwonderful/naoyu_bluetooth_android/data/RawDataRecorder.kt`
- 功能：
  - 记录原始蓝牙数据包
  - 每个字节转换为十六进制格式，用空格分隔
  - 每个数据包占一行
  - 支持文件头信息记录
  - 提供统计信息（包数、文件大小等）

### 2. CollectViewModel增强
- 添加了原始数据记录器支持
- 新增状态：`isRawRecording`、`rawRecordingStats`
- 修改了保存功能支持多种格式：
  - `"neeg"`：只保存NEEG格式
  - `"txt"`：只保存TXT格式
  - `"both"`：保存NEEG+TXT格式
  - `"raw"`：只保存原始数据包
  - `"all"`：保存所有格式（NEEG+TXT+原始数据包）

### 3. UI界面更新
- 保存对话框显示处理后数据和原始数据的统计信息
- 增加了原始数据包保存选项
- 记录按钮显示状态更新（显示是否包含原始数据记录）

## 数据格式说明

### 原始数据包格式示例
```
# 原始蓝牙数据包记录文件
# 设备名称: Dbay-001
# 开始时间: 2024-01-15 10:30:25
# 数据格式: 每个字节用十六进制表示，用空格分隔，每个数据包一行
# ========================================

01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F 10 11 12 13 14 15 16 17 18 19 1A 1B 1C 1D 1E 1F 20 21 22 23 24 25 26 27 28 29 2A 2B 2C 2D 2E 2F 30 31 32 33 34 35 36 37 38 39 3A 3B 3C 3D 3E 3F 40 41 42 43 44 45 46 47 48 49 4A 4B 4C 4D 4E 4F 50 51 52 53 54 55 56 57 58 59 5A 5B 5C 5D 5E 5F 60 61 62 63 64 65 66 67 68 69 6A 6B 6C 6D 6E 6F 70 71 72 73 74 75 76 77 78 79 7A 7B 7C 7D 7E 7F 80 81 82 83 84 85 86 87 88 89 8A 8B 8C 8D 8E 8F 90 91 92 93 94 95 96 97 98 99 9A 9B 9C 9D 9E 9F A0 A1 A2 A3 A4 A5 A6 A7 A8 A9 AA AB AC AD AE AF B0 B1 B2 B3 B4 B5 B6 B7 B8 B9 BA BB BC BD BE BF C0 C1 C2 C3 C4 C5 C6 C7 C8 C9 CA CB CC CD CE CF D0 D1 D2 D3 D4 D5 D6 D7 D8 D9 DA DB DC DD DE DF E0 E1 E2 E3 E4 E5 E6 E7 E8 E9 EA EB EC ED EE EF F0 F1 F2 F3
02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F 10 11 12 13 14 15 16 17 18 19 1A 1B 1C 1D 1E 1F 20 21 22 23 24 25 26 27 28 29 2A 2B 2C 2D 2E 2F 30 31 32 33 34 35 36 37 38 39 3A 3B 3C 3D 3E 3F 40 41 42 43 44 45 46 47 48 49 4A 4B 4C 4D 4E 4F 50 51 52 53 54 55 56 57 58 59 5A 5B 5C 5D 5E 5F 60 61 62 63 64 65 66 67 68 69 6A 6B 6C 6D 6E 6F 70 71 72 73 74 75 76 77 78 79 7A 7B 7C 7D 7E 7F 80 81 82 83 84 85 86 87 88 89 8A 8B 8C 8D 8E 8F 90 91 92 93 94 95 96 97 98 99 9A 9B 9C 9D 9E 9F A0 A1 A2 A3 A4 A5 A6 A7 A8 A9 AA AB AC AD AE AF B0 B1 B2 B3 B4 B5 B6 B7 B8 B9 BA BB BC BD BE BF C0 C1 C2 C3 C4 C5 C6 C7 C8 C9 CA CB CC CD CE CF D0 D1 D2 D3 D4 D5 D6 D7 D8 D9 DA DB DC DD DE DF E0 E1 E2 E3 E4 E5 E6 E7 E8 E9 EA EB EC ED EE EF F0 F1 F2 F3 F4
...
```

## 使用流程

1. **连接设备**：在主界面连接蓝牙设备
2. **进入采集界面**：点击进入数据采集界面
3. **开始采集**：点击"开始采集"按钮开始显示数据
4. **开始保存**：点击"开始保存"按钮开始记录数据（默认同时记录处理后数据和原始数据包）
5. **停止保存**：点击"停止保存"按钮停止记录
6. **选择保存格式**：在弹出的对话框中选择要保存的格式
   - 如果记录了原始数据，会显示"原始数据包"和"保存所有格式"选项
7. **输入文件名并保存**

## 文件保存位置

- 处理后数据：`/data/data/[包名]/files/recordings/[文件名].neeg` 或 `[文件名].txt`
- 原始数据包：`/data/data/[包名]/files/recordings/[文件名]_raw.txt`
- 元数据：`/data/data/[包名]/files/recordings/metadata/[文件名].json`

## 技术实现要点

1. **数据流集成**：原始数据记录器监听BluetoothManager的rawEEGData流
2. **并发处理**：使用Kotlin协程和Channel进行异步数据处理
3. **内存优化**：使用缓冲写入，避免频繁磁盘操作
4. **格式兼容**：十六进制格式便于调试和分析
5. **状态管理**：独立的原始数据记录状态，不依赖处理后数据记录
