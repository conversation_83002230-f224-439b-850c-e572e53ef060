# 脑电信号处理系统配置参数文档

本文档详细说明了从代码中提取并统一管理的所有底层配置参数。这些参数不在用户界面中暴露，但对计算和显示结果有重要影响。

## 配置文件位置
`app/src/main/java/com/brainwonderful/naoyu_bluetooth_android/config/EEGProcessingConfig.kt`

## 参数分类详解

### 1. 滤波器算法内部参数 (FilterConfig)

#### 陷波滤波器参数
- **NOTCH_FILTER_BETA**: `0.96`
  - 用途：控制陷波滤波器品质因数，影响陷波深度和带宽
  - 影响：较高值提供更窄的陷波带宽，更精确地消除50Hz工频干扰
  
- **NOTCH_FILTER_USE_BETA**: `true`
  - 用途：选择使用Beta参数还是Q值参数
  - 影响：确保与UniApp版本的滤波器行为一致

#### FIR滤波器参数
- **FIR_FILTER_DEFAULT_ORDER**: `64`
  - 用途：FIR滤波器默认阶数
  - 影响：较高阶数提供更陡峭的滤波特性，但增加计算复杂度

#### 时间常数参数
- **TIME_CONSTANT_DEFAULT_VALUE**: `0.03`
  - 用途：时间常数滤波器的默认RC时间常数（秒）
  - 影响：控制高通滤波特性，去除基线漂移

- **DC_REMOVAL_TIME_CONSTANT**: `0.5`
  - 用途：DC去除滤波器时间常数
  - 影响：自动去除直流偏移，时间常数越大去除越彻底

### 2. 数据缓冲区和时间窗口参数 (BufferConfig)

#### 缓冲区时间窗口
- **PROCESSING_BUFFER_TIME_SECONDS**: `4.0`
  - 用途：数据处理缓冲区时间窗口（秒）
  - 影响：影响滤波器延迟和内存占用，较大值提供更稳定的滤波效果

- **DISPLAY_BUFFER_TIME_SECONDS**: `5.0`
  - 用途：显示缓冲区时间窗口（秒）
  - 影响：控制波形图中显示的历史数据长度

#### 走纸速度映射
- **PAPER_SPEED_TIME_WINDOWS**: 
  - `"15mm/s" -> 8秒`：慢速显示更长时间窗口
  - `"30mm/s" -> 4秒`：标准显示窗口
  - `"60mm/s" -> 2秒`：快速显示较短时间窗口
  - 影响：不同速度下的时间分辨率和数据密度

- **DEFAULT_DISPLAY_TIME_WINDOW**: `4`
  - 用途：默认显示时间窗口（秒）
  - 影响：异常情况下的备用时间窗口设置

### 3. FFT和频谱分析参数 (SpectrumConfig)

#### FFT计算控制
- **FFT_UPDATE_INTERVAL**: `2`
  - 用途：FFT更新间隔（数据包数）
  - 影响：控制频谱分析的计算频率，影响实时性和CPU占用

- **FFT_WINDOW_TYPE**: `"HANN"`
  - 用途：FFT窗口函数类型
  - 影响：汉宁窗减少频谱泄露，提高频率分辨率

- **FFT_OVERLAP_RATIO**: `0.5`
  - 用途：FFT重叠率（50%）
  - 影响：提高时频分辨率，减少时间域的不连续性

#### 频谱有效范围
- **SPECTRUM_MIN_FREQ**: `0.5`
  - 用途：频谱分析的最小有效频率（Hz）
  - 影响：过滤低频噪声和直流分量

- **SPECTRUM_MAX_FREQ_RATIO**: `0.45`
  - 用途：最大有效频率比例（采样率的45%）
  - 影响：避免混叠现象，确保频谱分析准确性

- **SPECTRUM_SMOOTHING_FACTOR**: `0.8`
  - 用途：频谱平滑系数
  - 影响：减少频谱波动，提供更稳定的频谱显示

### 4. 信号质量评估参数 (SignalQualityConfig)

#### 信号检测阈值
- **SIGNAL_DROPOUT_THRESHOLD_UV**: `2000.0`
  - 用途：信号质量判断的电压阈值（μV）
  - 影响：超过此值认为电极脱落，影响信号质量指示

- **QUALITY_ASSESSMENT_WINDOW_SEC**: `2.0`
  - 用途：信号质量评估的时间窗口（秒）
  - 影响：统计分析的时间跨度，影响质量判断的稳定性

#### 异常检测参数
- **OUTLIER_DETECTION_STD_MULTIPLIER**: `3.0`
  - 用途：异常值检测的标准差倍数
  - 影响：超过3倍标准差视为异常，影响数据清洗效果

- **MIN_SIGNAL_CHANGE_RATE**: `0.1`
  - 用途：信号变化检测的最小变化率
  - 影响：判断信号是否有效变化，过滤静止噪声

### 5. 显示和渲染参数 (DisplayConfig)

#### 渲染性能
- **WAVEFORM_RENDER_FPS**: `30`
  - 用途：波形图渲染帧率（FPS）
  - 影响：控制UI刷新频率，平衡流畅度和性能

- **WAVEFORM_ANTIALIASING**: `true`
  - 用途：波形抗锯齿开关
  - 影响：提高显示质量但消耗更多GPU资源

#### 视觉样式
- **WAVEFORM_STROKE_WIDTH**: `1.5f`
  - 用途：波形线条宽度（dp）
  - 影响：波形线条的粗细，影响视觉清晰度

- **GRID_LINE_ALPHA**: `0.3f`
  - 用途：网格线透明度
  - 影响：背景网格的可见度，不干扰波形观察

- **GRID_LINE_WIDTH**: `0.5f`
  - 用途：网格线宽度（dp）
  - 影响：网格线的粗细程度

#### 显示范围
- **WAVEFORM_MIN_ZOOM**: `0.1`
- **WAVEFORM_MAX_ZOOM**: `10.0`
  - 用途：波形图缩放系数范围
  - 影响：用户可缩放的最小和最大倍数

- **WAVEFORM_VERTICAL_MARGIN_PERCENT**: `0.1`
  - 用途：波形显示的垂直边距百分比
  - 影响：波形上下的空白区域比例

### 6. 性能优化参数 (PerformanceConfig)

#### 计算资源
- **PROCESSING_THREAD_POOL_SIZE**: `2`
  - 用途：数据处理线程池大小
  - 影响：并发处理能力，影响实时性能

- **MAX_MEMORY_CACHE_MB**: `50`
  - 用途：内存缓存最大占用（MB）
  - 影响：内存使用上限，防止OOM

- **GC_CLEANUP_THRESHOLD**: `0.8`
  - 用途：GC触发的缓冲区清理阈值
  - 影响：内存管理的积极程度

#### 批处理优化
- **MAX_BATCH_PROCESSING_SIZE**: `1000`
  - 用途：批量数据处理的最大批次大小
  - 影响：批处理效率和内存占用的平衡

- **FLOW_BUFFER_SIZE**: `1000`
  - 用途：数据流背压处理的缓冲区大小
  - 影响：处理高频数据流时的稳定性

- **ASYNC_PROCESSING_TIMEOUT_MS**: `5000L`
  - 用途：异步处理超时时间（毫秒）
  - 影响：异步任务的最大等待时间

### 7. 算法特定参数 (AlgorithmConfig)

#### 数据处理算法
- **DOUGLAS_PEUCKER_TOLERANCE**: `0.5`
  - 用途：Douglas-Peucker曲线简化算法的容差值
  - 影响：控制曲线简化程度，影响数据压缩率

- **EDGE_DETECTION_GRADIENT_THRESHOLD**: `10.0`
  - 用途：边缘检测的梯度阈值
  - 影响：边缘检测的敏感度

- **PEAK_DETECTION_MIN_HEIGHT_RATIO**: `0.1`
  - 用途：峰值检测的最小高度比例
  - 影响：峰值检测的严格程度

#### 数值精度
- **FILTER_STABILITY_THRESHOLD**: `1e-6`
  - 用途：滤波器稳定性检查的收敛阈值
  - 影响：滤波器数值稳定性的判断标准

- **NUMERICAL_PRECISION_THRESHOLD**: `1e-12`
  - 用途：数值计算的最小精度阈值
  - 影响：浮点数计算的精度要求

### 8. 设备特定参数 (DeviceConfig)

#### DBAY设备协议
- **DBAY_PACKET_SIZE**: `244`
  - 用途：DBAY设备的数据包大小（字节）
  - 影响：数据包解析的基础参数

- **DBAY_SIGNAL_SIZE**: `4`
  - 用途：DBAY设备的信号部分大小（字节）
  - 影响：信号状态和电池电量解析

- **DBAY_CHANNEL_SIZE**: `120`
  - 用途：DBAY设备的单通道数据大小（字节）
  - 影响：AF7和AF8通道数据解析

#### 连接参数
- **BLUETOOTH_CONNECTION_TIMEOUT_MS**: `10000L`
  - 用途：蓝牙连接超时时间（毫秒）
  - 影响：连接建立的等待时间

- **BLUETOOTH_DATA_TIMEOUT_MS**: `3000L`
  - 用途：蓝牙数据接收超时时间（毫秒）
  - 影响：数据接收异常检测

- **DEVICE_HEARTBEAT_INTERVAL_MS**: `1000L`
  - 用途：设备心跳检测间隔（毫秒）
  - 影响：连接状态监控频率

### 9. 数据存储参数 (StorageConfig)

#### 文件管理
- **MAX_DATA_FILE_SIZE_MB**: `100`
  - 用途：数据文件最大大小（MB）
  - 影响：单个文件的存储限制

- **RAW_DATA_COMPRESSION_RATIO**: `0.7`
  - 用途：原始数据的压缩比例
  - 影响：存储空间的使用效率

- **TEMP_FILE_CLEANUP_HOURS**: `24`
  - 用途：临时文件清理时间（小时）
  - 影响：临时文件的生存周期

- **EXPORT_BATCH_SIZE**: `10000`
  - 用途：数据导出的批次大小
  - 影响：导出性能和内存占用

### 10. 调试参数 (DebugConfig)

#### 监控间隔
- **PERFORMANCE_SAMPLING_INTERVAL_MS**: `1000L`
  - 用途：性能统计的采样间隔（毫秒）
  - 影响：性能监控的精度

- **MEMORY_MONITOR_INTERVAL_MS**: `5000L`
  - 用途：内存监控的检查间隔（毫秒）
  - 影响：内存泄露检测的及时性

- **MAX_LOG_FILE_SIZE_MB**: `10`
  - 用途：日志文件最大大小（MB）
  - 影响：日志存储的空间限制

## 使用说明

### 参数修改方式
1. 直接修改 `EEGProcessingConfig.kt` 中的相应常量
2. 重新编译应用
3. 所有引用该参数的代码会自动使用新值

### 注意事项
1. **保持数值一致性**：修改参数前确保了解其对整个系统的影响
2. **测试验证**：参数修改后需要充分测试以确保功能正常
3. **性能影响**：某些参数（如FPS、缓冲区大小）会直接影响应用性能
4. **算法精度**：滤波器和信号处理参数会影响算法的精度和效果

### 调优建议
- **内存受限设备**：减少缓冲区大小和FPS
- **高精度需求**：增加滤波器阶数和FFT窗口重叠率
- **实时性优先**：减少FFT更新间隔和处理批次大小
- **电池优化**：降低渲染FPS和监控频率

这种统一配置管理方式使得系统参数调优变得更加便捷和可控，为后续的算法优化和设备适配提供了良好的基础。