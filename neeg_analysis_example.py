#!/usr/bin/env python3
"""
NEEG文件高级数据分析示例
展示如何使用NEEGParser进行脑电数据的深度分析

功能包括：
1. 基础统计分析
2. 频域分析（FFT、功率谱密度）
3. 时频分析（小波变换）
4. 信号质量评估
5. 滤波处理对比
6. 数据导出多种格式

依赖库：
pip install numpy pandas matplotlib scipy seaborn
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal, stats
from scipy.fft import fft, fftfreq
import warnings
warnings.filterwarnings('ignore')

from neeg_parser import NEEGParser


class NEEGAnalyzer:
    """NEEG数据高级分析器"""
    
    def __init__(self, neeg_path: str):
        """
        初始化分析器
        
        Args:
            neeg_path: .neeg文件路径
        """
        self.parser = NEEGParser(neeg_path)
        self.df = self.parser.to_dataframe()
        self.sample_rate = self.parser.get_header().get('sample_rate', 250)
        
        print(f"数据加载完成: {len(self.df)}个采样点，采样率{self.sample_rate}Hz")
    
    def basic_statistics(self):
        """基础统计分析"""
        if self.df.empty:
            print("没有数据进行统计分析")
            return
        
        print("\n" + "="*50)
        print("基础统计分析")
        print("="*50)
        
        # 基本统计信息
        stats_df = self.df[['af7_uv', 'af8_uv']].describe()
        print("\n统计摘要:")
        print(stats_df.round(2))
        
        # 信号质量统计
        quality_stats = self.parser.get_signal_quality_stats()
        print(f"\n信号质量:")
        print(f"  - 信号良好包数: {quality_stats.get('good_signal_packets', 0)}")
        print(f"  - 信号脱落包数: {quality_stats.get('poor_signal_packets', 0)}")
        print(f"  - 信号质量百分比: {quality_stats.get('signal_quality_percentage', 0):.1f}%")
        
        # 数据范围和异常值检测
        af7_outliers = self._detect_outliers(self.df['af7_uv'])
        af8_outliers = self._detect_outliers(self.df['af8_uv'])
        
        print(f"\n异常值检测 (IQR方法):")
        print(f"  - AF7异常值: {len(af7_outliers)}个 ({len(af7_outliers)/len(self.df)*100:.2f}%)")
        print(f"  - AF8异常值: {len(af8_outliers)}个 ({len(af8_outliers)/len(self.df)*100:.2f}%)")
        
        # 通道相关性
        correlation = self.df['af7_uv'].corr(self.df['af8_uv'])
        print(f"\nAF7-AF8通道相关性: {correlation:.3f}")
    
    def _detect_outliers(self, data: pd.Series, k: float = 1.5) -> np.ndarray:
        """使用IQR方法检测异常值"""
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - k * IQR
        upper_bound = Q3 + k * IQR
        return data[(data < lower_bound) | (data > upper_bound)].index.values
    
    def frequency_analysis(self, channel: str = 'both', save_plot: bool = True):
        """
        频域分析
        
        Args:
            channel: 'af7', 'af8', 或 'both'
            save_plot: 是否保存图像
        """
        print("\n" + "="*50)
        print("频域分析")
        print("="*50)
        
        channels = []
        if channel in ['af7', 'both']:
            channels.append(('AF7', self.df['af7_uv'].values))
        if channel in ['af8', 'both']:
            channels.append(('AF8', self.df['af8_uv'].values))
        
        fig, axes = plt.subplots(len(channels), 2, figsize=(15, 6*len(channels)))
        if len(channels) == 1:
            axes = axes.reshape(1, -1)
        
        for i, (ch_name, data) in enumerate(channels):
            # 移除直流分量
            data = data - np.mean(data)
            
            # FFT分析
            fft_data = fft(data)
            freqs = fftfreq(len(data), 1/self.sample_rate)
            
            # 只取正频率部分
            n = len(data) // 2
            freqs_pos = freqs[:n]
            fft_magnitude = np.abs(fft_data[:n])
            
            # 功率谱密度
            freq_psd, psd = signal.welch(data, self.sample_rate, nperseg=1024)
            
            # 绘制FFT幅度谱
            axes[i, 0].plot(freqs_pos, fft_magnitude)
            axes[i, 0].set_xlim(0, 100)  # 只显示0-100Hz
            axes[i, 0].set_xlabel('频率 (Hz)')
            axes[i, 0].set_ylabel('幅度')
            axes[i, 0].set_title(f'{ch_name} - FFT幅度谱')
            axes[i, 0].grid(True, alpha=0.3)
            
            # 绘制功率谱密度
            axes[i, 1].semilogy(freq_psd, psd)
            axes[i, 1].set_xlim(0, 100)
            axes[i, 1].set_xlabel('频率 (Hz)')
            axes[i, 1].set_ylabel('功率谱密度 (μV²/Hz)')
            axes[i, 1].set_title(f'{ch_name} - 功率谱密度')
            axes[i, 1].grid(True, alpha=0.3)
            
            # 计算各频段功率
            freq_bands = {
                'Delta (0.5-4 Hz)': (0.5, 4),
                'Theta (4-8 Hz)': (4, 8),
                'Alpha (8-13 Hz)': (8, 13),
                'Beta (13-30 Hz)': (13, 30),
                'Gamma (30-50 Hz)': (30, 50)
            }
            
            print(f"\n{ch_name}频段功率分析:")
            total_power = np.trapz(psd, freq_psd)
            
            for band_name, (low, high) in freq_bands.items():
                mask = (freq_psd >= low) & (freq_psd <= high)
                band_power = np.trapz(psd[mask], freq_psd[mask])
                relative_power = (band_power / total_power) * 100
                print(f"  {band_name}: {band_power:.2e} μV² ({relative_power:.1f}%)")
        
        plt.tight_layout()
        if save_plot:
            plt.savefig(f'neeg_frequency_analysis_{channel}.png', dpi=300, bbox_inches='tight')
            print(f"\n频域分析图已保存: neeg_frequency_analysis_{channel}.png")
        else:
            plt.show()
    
    def time_frequency_analysis(self, channel: str = 'af7', duration: float = 30.0):
        """
        时频分析（短时傅里叶变换）
        
        Args:
            channel: 'af7' 或 'af8'
            duration: 分析时长（秒）
        """
        print("\n" + "="*50)
        print(f"时频分析 - {channel.upper()}通道")
        print("="*50)
        
        # 选择数据
        if channel == 'af7':
            data = self.df['af7_uv'].values
        else:
            data = self.df['af8_uv'].values
        
        # 截取指定时长
        max_samples = int(duration * self.sample_rate)
        data = data[:min(max_samples, len(data))]
        
        # 移除直流分量
        data = data - np.mean(data)
        
        # 短时傅里叶变换
        nperseg = int(self.sample_rate * 2)  # 2秒窗口
        f, t, Sxx = signal.spectrogram(data, self.sample_rate, nperseg=nperseg)
        
        # 绘制时频图
        plt.figure(figsize=(12, 8))
        plt.pcolormesh(t, f, 10 * np.log10(Sxx), shading='gouraud')
        plt.ylim(0, 50)  # 只显示0-50Hz
        plt.ylabel('频率 (Hz)')
        plt.xlabel('时间 (秒)')
        plt.title(f'{channel.upper()}通道时频分析图')
        plt.colorbar(label='功率谱密度 (dB)')
        
        # 保存图像
        plt.savefig(f'neeg_timefrequency_{channel}.png', dpi=300, bbox_inches='tight')
        print(f"时频分析图已保存: neeg_timefrequency_{channel}.png")
        plt.show()
    
    def filter_comparison(self, save_plot: bool = True):
        """
        滤波处理对比
        展示原始数据与不同滤波器处理后的效果
        """
        print("\n" + "="*50)
        print("滤波处理对比")
        print("="*50)
        
        # 选择AF7数据进行演示
        data = self.df['af7_uv'].values[:int(10 * self.sample_rate)]  # 取前10秒
        time_axis = np.arange(len(data)) / self.sample_rate
        
        # 设计滤波器
        # 1. 50Hz陷波滤波器
        notch_freq = 50.0
        Q = 30  # 品质因子
        b_notch, a_notch = signal.iirnotch(notch_freq, Q, self.sample_rate)
        
        # 2. 1-35Hz带通滤波器
        low_freq = 1.0
        high_freq = 35.0
        b_band, a_band = signal.butter(4, [low_freq, high_freq], btype='band', fs=self.sample_rate)
        
        # 3. 组合滤波器（陷波 + 带通）
        
        # 应用滤波器
        data_notch = signal.filtfilt(b_notch, a_notch, data)
        data_band = signal.filtfilt(b_band, a_band, data)
        data_combined = signal.filtfilt(b_band, a_band, data_notch)
        
        # 绘制对比图
        fig, axes = plt.subplots(4, 1, figsize=(15, 12), sharex=True)
        
        # 原始数据
        axes[0].plot(time_axis, data, 'k-', linewidth=0.8, alpha=0.8)
        axes[0].set_ylabel('幅值 (μV)')
        axes[0].set_title('原始数据')
        axes[0].grid(True, alpha=0.3)
        
        # 50Hz陷波
        axes[1].plot(time_axis, data_notch, 'b-', linewidth=0.8)
        axes[1].set_ylabel('幅值 (μV)')
        axes[1].set_title('50Hz陷波滤波')
        axes[1].grid(True, alpha=0.3)
        
        # 1-35Hz带通
        axes[2].plot(time_axis, data_band, 'g-', linewidth=0.8)
        axes[2].set_ylabel('幅值 (μV)')
        axes[2].set_title('1-35Hz带通滤波')
        axes[2].grid(True, alpha=0.3)
        
        # 组合滤波
        axes[3].plot(time_axis, data_combined, 'r-', linewidth=0.8)
        axes[3].set_ylabel('幅值 (μV)')
        axes[3].set_xlabel('时间 (秒)')
        axes[3].set_title('组合滤波 (陷波 + 带通)')
        axes[3].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            plt.savefig('neeg_filter_comparison.png', dpi=300, bbox_inches='tight')
            print("滤波对比图已保存: neeg_filter_comparison.png")
        else:
            plt.show()
        
        # 计算滤波效果统计
        print("\n滤波效果统计:")
        print(f"原始数据标准差: {np.std(data):.2f} μV")
        print(f"陷波滤波标准差: {np.std(data_notch):.2f} μV")
        print(f"带通滤波标准差: {np.std(data_band):.2f} μV")
        print(f"组合滤波标准差: {np.std(data_combined):.2f} μV")
    
    def signal_quality_analysis(self):
        """信号质量深度分析"""
        print("\n" + "="*50)
        print("信号质量深度分析")
        print("="*50)
        
        # 按信号状态分组分析
        timestamps = np.array(self.parser.timestamps)
        signal_status = np.array(self.parser.signal_status)
        
        # 每包40个点，展开到采样点级别
        sample_signal_status = np.repeat(signal_status, 40)[:len(self.df)]
        
        # 添加信号状态到DataFrame
        df_with_status = self.df.copy()
        df_with_status['signal_good'] = sample_signal_status == 0
        
        # 统计分析
        good_data = df_with_status[df_with_status['signal_good']]
        poor_data = df_with_status[~df_with_status['signal_good']]
        
        print(f"信号良好时段: {len(good_data)}个点 ({len(good_data)/len(df_with_status)*100:.1f}%)")
        print(f"信号脱落时段: {len(poor_data)}个点 ({len(poor_data)/len(df_with_status)*100:.1f}%)")
        
        if len(good_data) > 0 and len(poor_data) > 0:
            print("\n各状态下的数据特征:")
            print("信号良好时段:")
            print(f"  AF7: 均值={good_data['af7_uv'].mean():.2f}, 标准差={good_data['af7_uv'].std():.2f}")
            print(f"  AF8: 均值={good_data['af8_uv'].mean():.2f}, 标准差={good_data['af8_uv'].std():.2f}")
            
            print("信号脱落时段:")
            print(f"  AF7: 均值={poor_data['af7_uv'].mean():.2f}, 标准差={poor_data['af7_uv'].std():.2f}")
            print(f"  AF8: 均值={poor_data['af8_uv'].mean():.2f}, 标准差={poor_data['af8_uv'].std():.2f}")
    
    def export_analysis_report(self, output_path: str = "neeg_analysis_report.html"):
        """导出完整的分析报告"""
        print("\n" + "="*50)
        print("生成分析报告")
        print("="*50)
        
        # 执行所有分析
        summary = self.parser.get_data_summary()
        quality_stats = self.parser.get_signal_quality_stats()
        
        # 创建HTML报告
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>NEEG数据分析报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                h1, h2 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .stats {{ background-color: #f9f9f9; padding: 15px; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <h1>NEEG脑电数据分析报告</h1>
            
            <h2>文件信息</h2>
            <div class="stats">
                <p><strong>设备名称:</strong> {self.parser.get_header().get('device_name', 'Unknown')}</p>
                <p><strong>采样率:</strong> {self.sample_rate} Hz</p>
                <p><strong>记录时长:</strong> {summary.get('duration_minutes', 0):.1f} 分钟</p>
                <p><strong>总采样点:</strong> {summary.get('total_samples', 0):,} 个</p>
                <p><strong>数据包数:</strong> {summary.get('total_packets', 0):,} 个</p>
            </div>
            
            <h2>信号质量</h2>
            <div class="stats">
                <p><strong>信号质量:</strong> {quality_stats.get('signal_quality_percentage', 0):.1f}%</p>
                <p><strong>良好信号包:</strong> {quality_stats.get('good_signal_packets', 0):,} 个</p>
                <p><strong>脱落信号包:</strong> {quality_stats.get('poor_signal_packets', 0):,} 个</p>
            </div>
            
            <h2>数据统计</h2>
            <table>
                <tr><th>通道</th><th>均值 (μV)</th><th>标准差 (μV)</th><th>最小值 (μV)</th><th>最大值 (μV)</th></tr>
                <tr><td>AF7</td><td>{self.df['af7_uv'].mean():.2f}</td><td>{self.df['af7_uv'].std():.2f}</td><td>{self.df['af7_uv'].min():.2f}</td><td>{self.df['af7_uv'].max():.2f}</td></tr>
                <tr><td>AF8</td><td>{self.df['af8_uv'].mean():.2f}</td><td>{self.df['af8_uv'].std():.2f}</td><td>{self.df['af8_uv'].min():.2f}</td><td>{self.df['af8_uv'].max():.2f}</td></tr>
            </table>
            
            <p><em>报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</em></p>
        </body>
        </html>
        """
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"分析报告已保存: {output_path}")


def main():
    """示例用法"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python neeg_analysis_example.py <neeg_file_path>")
        return
    
    neeg_file = sys.argv[1]
    
    try:
        # 创建分析器
        analyzer = NEEGAnalyzer(neeg_file)
        
        # 执行各种分析
        analyzer.basic_statistics()
        analyzer.frequency_analysis('both')
        analyzer.time_frequency_analysis('af7', duration=30.0)
        analyzer.filter_comparison()
        analyzer.signal_quality_analysis()
        
        # 生成完整报告
        analyzer.export_analysis_report()
        
        print("\n" + "="*50)
        print("分析完成！生成的文件:")
        print("- neeg_frequency_analysis_both.png  # 频域分析图")
        print("- neeg_timefrequency_af7.png       # 时频分析图")
        print("- neeg_filter_comparison.png       # 滤波对比图")
        print("- neeg_analysis_report.html        # 完整分析报告")
        print("="*50)
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()