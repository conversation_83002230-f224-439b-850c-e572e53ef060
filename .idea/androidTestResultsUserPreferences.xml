<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidTestResultsUserPreferences">
    <option name="androidTestResultsTableState">
      <map>
        <entry key="-2109539749">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-1881851618">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-1875417273">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-1840075193">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-1792581716">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-1652882023">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-1219410057">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-909287940">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-729415193">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-640478270">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-277926616">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="-169529626">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="168355189">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="204535242">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="403760704">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="886154940">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="1028158587">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="1122929338">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="1217576285">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="1427749032">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="1641700395">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="1738033106">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
        <entry key="1908545284">
          <value>
            <AndroidTestResultsTableState>
              <option name="preferredColumnWidths">
                <map>
                  <entry key="Duration" value="90" />
                  <entry key="Tests" value="360" />
                  <entry key="Xiaomi 22081283C" value="120" />
                </map>
              </option>
            </AndroidTestResultsTableState>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>