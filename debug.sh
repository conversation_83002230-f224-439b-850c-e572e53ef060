#!/bin/bash

# 脑域奇迹蓝牙安卓项目调试工具
# 使用方法: ./debug.sh [命令]

set -e

PACKAGE_NAME="com.brainwonderful.naoyu_bluetooth_android"
MAIN_ACTIVITY="$PACKAGE_NAME/.MainActivity"

echo "🐛 脑域奇迹蓝牙安卓调试工具"
echo "=========================="

# 检查设备连接
check_device() {
    if ! adb devices | grep -q "device$"; then
        echo "❌ 没有检测到连接的设备，请确保："
        echo "   1. 设备已连接并启用USB调试"
        echo "   2. 已授权此计算机进行调试"
        exit 1
    fi
}

case "${1:-help}" in
    "start")
        echo "🚀 启动应用..."
        check_device
        adb shell am start -n "$MAIN_ACTIVITY"
        echo "✅ 应用已启动"
        ;;
    "stop")
        echo "🛑 停止应用..."
        check_device
        adb shell am force-stop "$PACKAGE_NAME"
        echo "✅ 应用已停止"
        ;;
    "restart")
        echo "🔄 重启应用..."
        check_device
        adb shell am force-stop "$PACKAGE_NAME"
        sleep 1
        adb shell am start -n "$MAIN_ACTIVITY"
        echo "✅ 应用已重启"
        ;;
    "log")
        echo "📋 实时显示应用日志 (按Ctrl+C退出)..."
        check_device
        adb logcat -c  # 清理旧日志
        adb logcat | grep -E "($PACKAGE_NAME|naoyu|brainwonderful|AndroidRuntime|System.err)"
        ;;
    "log-crash")
        echo "💥 显示崩溃日志..."
        check_device
        adb logcat -d | grep -E "(FATAL|AndroidRuntime|$PACKAGE_NAME)" | tail -50
        ;;
    "log-save")
        echo "💾 保存日志到文件..."
        check_device
        timestamp=$(date +"%Y%m%d_%H%M%S")
        logfile="debug_log_${timestamp}.txt"
        adb logcat -d > "$logfile"
        echo "✅ 日志已保存到: $logfile"
        ;;
    "memory")
        echo "💾 显示内存使用情况..."
        check_device
        adb shell dumpsys meminfo "$PACKAGE_NAME"
        ;;
    "cpu")
        echo "⚡ 显示CPU使用情况..."
        check_device
        adb shell top -n 1 | grep "$PACKAGE_NAME" || echo "应用未运行"
        ;;
    "network")
        echo "🌐 显示网络使用情况..."
        check_device
        adb shell dumpsys netstats | grep "$PACKAGE_NAME" || echo "无网络数据"
        ;;
    "permissions")
        echo "🔐 显示应用权限..."
        check_device
        adb shell dumpsys package "$PACKAGE_NAME" | grep -A 20 "requested permissions:"
        ;;
    "storage")
        echo "💿 显示存储使用情况..."
        check_device
        adb shell dumpsys package "$PACKAGE_NAME" | grep -E "(dataDir|codeDir|resourceDir)"
        echo ""
        echo "应用数据目录内容:"
        adb shell "ls -la /data/data/$PACKAGE_NAME/ 2>/dev/null || echo '无法访问应用数据目录'"
        ;;
    "database")
        echo "🗄️ 显示数据库信息..."
        check_device
        adb shell "ls -la /data/data/$PACKAGE_NAME/databases/ 2>/dev/null || echo '没有数据库文件'"
        ;;
    "preferences")
        echo "⚙️ 显示SharedPreferences..."
        check_device
        adb shell "ls -la /data/data/$PACKAGE_NAME/shared_prefs/ 2>/dev/null || echo '没有SharedPreferences文件'"
        ;;
    "screenshot")
        echo "📸 截取屏幕..."
        check_device
        timestamp=$(date +"%Y%m%d_%H%M%S")
        filename="debug_screenshot_${timestamp}.png"
        adb exec-out screencap -p > "$filename"
        echo "✅ 截图已保存: $filename"
        ;;
    "record")
        duration=${2:-10}
        echo "🎥 录制屏幕 ${duration}秒..."
        check_device
        timestamp=$(date +"%Y%m%d_%H%M%S")
        filename="debug_record_${timestamp}.mp4"
        adb shell screenrecord --time-limit "$duration" "/sdcard/$filename"
        adb pull "/sdcard/$filename" .
        adb shell rm "/sdcard/$filename"
        echo "✅ 录屏已保存: $filename"
        ;;
    "install-debug")
        echo "📱 安装Debug版本..."
        ./gradlew assembleDebug -x test
        ./gradlew installDebug
        echo "✅ Debug版本已安装"
        ;;
    "profile")
        echo "📊 启动性能分析 (按Ctrl+C退出)..."
        check_device
        echo "时间戳 | CPU% | 内存(MB) | 状态"
        echo "-------|------|----------|------"
        while true; do
            timestamp=$(date +"%H:%M:%S")
            cpu=$(adb shell top -n 1 | grep "$PACKAGE_NAME" | awk '{print $9}' || echo "0")
            memory=$(adb shell dumpsys meminfo "$PACKAGE_NAME" | grep "TOTAL" | awk '{print int($2/1024)}' || echo "0")
            status=$(adb shell "ps | grep $PACKAGE_NAME" >/dev/null && echo "运行中" || echo "未运行")
            echo "$timestamp | $cpu% | ${memory}MB | $status"
            sleep 3
        done
        ;;
    "monkey")
        events=${2:-100}
        echo "🐒 运行Monkey测试 ($events 个事件)..."
        check_device
        adb shell monkey -p "$PACKAGE_NAME" -v "$events"
        ;;
    "clear-data")
        echo "🧹 清理应用数据..."
        check_device
        adb shell pm clear "$PACKAGE_NAME"
        echo "✅ 应用数据已清理"
        ;;
    "backup")
        echo "💾 备份应用数据..."
        check_device
        timestamp=$(date +"%Y%m%d_%H%M%S")
        backup_file="app_backup_${timestamp}.ab"
        adb backup -f "$backup_file" "$PACKAGE_NAME"
        echo "✅ 应用数据已备份到: $backup_file"
        ;;
    "device-info")
        echo "📱 设备信息..."
        check_device
        echo "设备型号: $(adb shell getprop ro.product.model)"
        echo "Android版本: $(adb shell getprop ro.build.version.release)"
        echo "API级别: $(adb shell getprop ro.build.version.sdk)"
        echo "CPU架构: $(adb shell getprop ro.product.cpu.abi)"
        echo "屏幕密度: $(adb shell wm density | cut -d' ' -f3)"
        echo "屏幕尺寸: $(adb shell wm size | cut -d' ' -f3)"
        ;;
    "help"|*)
        echo "📖 调试命令使用说明:"
        echo ""
        echo "🚀 应用控制:"
        echo "   ./debug.sh start           - 启动应用"
        echo "   ./debug.sh stop            - 停止应用"
        echo "   ./debug.sh restart         - 重启应用"
        echo "   ./debug.sh install-debug   - 安装Debug版本"
        echo ""
        echo "📋 日志调试:"
        echo "   ./debug.sh log             - 实时显示应用日志"
        echo "   ./debug.sh log-crash       - 显示崩溃日志"
        echo "   ./debug.sh log-save        - 保存日志到文件"
        echo ""
        echo "📊 性能监控:"
        echo "   ./debug.sh memory          - 显示内存使用"
        echo "   ./debug.sh cpu             - 显示CPU使用"
        echo "   ./debug.sh network         - 显示网络使用"
        echo "   ./debug.sh profile         - 实时性能监控"
        echo ""
        echo "🔍 应用信息:"
        echo "   ./debug.sh permissions     - 显示应用权限"
        echo "   ./debug.sh storage         - 显示存储使用"
        echo "   ./debug.sh database        - 显示数据库信息"
        echo "   ./debug.sh preferences     - 显示SharedPreferences"
        echo ""
        echo "📸 截图录屏:"
        echo "   ./debug.sh screenshot      - 截取屏幕"
        echo "   ./debug.sh record [秒数]   - 录制屏幕 (默认10秒)"
        echo ""
        echo "🧪 测试工具:"
        echo "   ./debug.sh monkey [事件数] - 运行Monkey测试 (默认100事件)"
        echo ""
        echo "🛠️ 数据管理:"
        echo "   ./debug.sh clear-data      - 清理应用数据"
        echo "   ./debug.sh backup          - 备份应用数据"
        echo ""
        echo "📱 设备信息:"
        echo "   ./debug.sh device-info     - 显示设备信息"
        echo ""
        echo "💡 提示: 大部分命令需要设备连接并启用USB调试"
        ;;
esac 