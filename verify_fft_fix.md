# FFT算法修复验证指南

## 修复内容

本次修复将安卓版本的FFT算法调整为与Python `eeg_spectrum.py` 版本数值一致，主要包括：

### 1. 数据预处理改进
- ✅ **添加DC偏移移除**：自动计算并移除信号均值
- ✅ **窗函数应用**：正确应用Hanning窗函数
- ✅ **窗函数补偿**：计算并应用窗函数补偿系数

### 2. FFT计算修正
- ✅ **正确归一化**：`magnitude = 2.0 * abs(fft) / N * window_correction`
- ✅ **单边谱转换**：除DC和Nyquist频率外，其他频率乘以2.0
- ✅ **物理意义**：幅值现在具有与输入信号相同的物理单位（如μV）

### 3. 代码结构优化
- ✅ **C++层完整实现**：所有信号处理逻辑在C++层完成
- ✅ **接口简化**：Kotlin层只需调用，无需额外处理
- ✅ **注释更新**：移除不正确的注释，添加准确的说明

## 验证方法

### 1. 运行单元测试
```bash
./gradlew test
```

关键测试：
- `testFFTConsistencyWithPython()`: 验证与Python版本的数值一致性
- `testFFTDCRemoval()`: 验证DC偏移移除功能

### 2. 预期结果对比

#### 修复前（原始FFT模值）
```
输入：5μV, 10Hz正弦波
输出：~2560 (无单位，随FFT点数变化)
```

#### 修复后（Python兼容）
```
输入：5μV, 10Hz正弦波  
输出：~5.0μV (物理意义明确，与输入一致)
```

### 3. 实际应用验证

在EEG数据采集界面观察：
- **频谱图纵轴**：现在显示真实的μV值
- **频段功率**：数值具有物理意义，可与文献对比
- **主频检测**：更准确的幅值估计

## 技术细节

### Python参考实现
```python
# eeg_spectrum.py 中的标准计算
window_function = signal.windows.hann(len(data))
windowed_data = data * window_function
window_correction = 1.0 / np.mean(window_function)
spectrum = np.fft.rfft(windowed_data)
amplitude = 2.0 * np.abs(spectrum) / len(data) * window_correction
```

### 安卓C++实现（修复后）
```cpp
// 1. 去除DC偏移
removeDCOffset(processedInput);

// 2. 应用窗函数
std::vector<double> window = generateWindow(fftSize, WindowType::HANNING);
applyWindow(processedInput, window);

// 3. 计算补偿系数
double windowCorrection = calculateWindowCorrection(window);

// 4. FFT + 归一化
double normalizedMagnitude = std::abs(data[i]) / fftSize;
result.magnitudes[i] = 2.0 * normalizedMagnitude * windowCorrection; // (除DC/Nyquist)
```

## 兼容性说明

- ✅ **向后兼容**：现有API接口不变
- ✅ **性能优化**：C++实现保持高性能
- ✅ **精度提升**：结果现在具有科学计算精度
- ⚠️ **数值变化**：频谱图的数值会发生显著变化（这是预期的修正）

## 注意事项

1. **UI适配**：频谱图的Y轴范围可能需要调整
2. **阈值更新**：基于幅值的判断逻辑需要使用新的数值范围
3. **数据对比**：新旧版本的频谱数据不可直接对比

修复完成后，安卓版本的频谱分析结果将与Python科学计算版本保持数值一致，为后续的算法验证和跨平台对比提供可靠基础。 