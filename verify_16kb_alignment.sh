#!/bin/bash

# 验证16KB页面大小对齐的脚本
# 用于检查native库是否符合Android 15+的16KB页面大小要求

echo "=== 16KB页面大小对齐验证 ==="
echo ""

# 查找所有生成的.so文件
SO_FILES=$(find app/build -name "libnaoyu_filters.so" -type f | grep -E "(arm64-v8a|armeabi-v7a|x86|x86_64)")

if [ -z "$SO_FILES" ]; then
    echo "❌ 未找到native库文件，请先构建项目"
    echo "运行: ./gradlew assembleDebug"
    exit 1
fi

# 检查llvm-readelf工具
READELF_PATH=""
if [ -f "/opt/homebrew/Cellar/llvm/20.1.8/bin/llvm-readelf" ]; then
    READELF_PATH="/opt/homebrew/Cellar/llvm/20.1.8/bin/llvm-readelf"
elif command -v llvm-readelf &> /dev/null; then
    READELF_PATH="llvm-readelf"
elif command -v readelf &> /dev/null; then
    READELF_PATH="readelf"
else
    echo "❌ 未找到readelf工具，请安装llvm:"
    echo "brew install llvm"
    exit 1
fi

echo "使用工具: $READELF_PATH"
echo ""

# 检查每个架构的库文件
ALL_ALIGNED=true

for SO_FILE in $SO_FILES; do
    ARCH=$(echo "$SO_FILE" | grep -o -E "(arm64-v8a|armeabi-v7a|x86|x86_64)")
    echo "检查架构: $ARCH"
    echo "文件: $SO_FILE"
    
    # 获取LOAD段信息
    LOAD_INFO=$($READELF_PATH -l "$SO_FILE" | grep LOAD)
    
    if [ -z "$LOAD_INFO" ]; then
        echo "❌ 无法读取LOAD段信息"
        ALL_ALIGNED=false
        continue
    fi
    
    echo "LOAD段信息:"
    echo "$LOAD_INFO"
    
    # 检查对齐值是否为16KB (0x4000)
    ALIGNMENT_VALUES=$(echo "$LOAD_INFO" | awk '{print $NF}')
    
    ARCH_ALIGNED=true
    for ALIGN in $ALIGNMENT_VALUES; do
        if [ "$ALIGN" != "0x4000" ]; then
            echo "❌ 发现非16KB对齐: $ALIGN"
            ARCH_ALIGNED=false
            ALL_ALIGNED=false
        fi
    done
    
    if [ "$ARCH_ALIGNED" = true ]; then
        echo "✅ $ARCH 架构已正确对齐到16KB"
    else
        echo "❌ $ARCH 架构对齐不正确"
    fi
    
    echo ""
done

if [ "$ALL_ALIGNED" = true ]; then
    echo "🎉 所有架构的native库都已正确对齐到16KB页面大小！"
    echo "✅ 符合Android 15+的要求"
else
    echo "❌ 部分库文件对齐不正确，需要修复"
    exit 1
fi
