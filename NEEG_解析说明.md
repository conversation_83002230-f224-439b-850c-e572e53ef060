# NEEG文件解析说明

## 概述

NEEG（NaoYu EEG）是Android脑电采集应用生成的二进制数据格式，包含完整的原始脑电数据和时间戳信息。本文档详细说明如何使用Python解析NEEG文件。

## 文件格式结构

### 整体结构
```
┌─────────────────┬──────────────────────────────────────┐
│   文件头(64字节) │           数据包序列               │
├─────────────────┼──────────────────────────────────────┤
│ 魔数+版本+设备信息│ [时间戳8字节+原始数据244字节] × N包  │
└─────────────────┴──────────────────────────────────────┘
```

### 文件头格式（64字节）
| 字段 | 偏移 | 大小 | 类型 | 说明 |
|------|------|------|------|------|
| 魔数 | 0 | 4 | ASCII | "NEEG" |
| 版本 | 4 | 2 | uint16 | 文件格式版本 |
| 采样率 | 6 | 2 | uint16 | 250Hz |
| 通道数 | 8 | 1 | uint8 | 2（AF7+AF8） |
| 设备名 | 9 | 32 | string | 蓝牙设备名称 |
| 开始时间戳 | 41 | 8 | uint64 | 毫秒时间戳 |
| 保留字段 | 49 | 15 | bytes | 预留空间 |

### 数据包格式（252字节）
每个数据包包含：
- **时间戳**（8字节）：包到达时间的毫秒时间戳
- **原始蓝牙数据**（244字节）：
  - 信号数据（4字节）：包序号、信号状态、保留、电池电量
  - AF7通道（120字节）：40个数据点 × 3字节ADC值
  - AF8通道（120字节）：40个数据点 × 3字节ADC值

### ADC数据转换
```
原始3字节ADC值 → 24位有符号整数 → 微伏值

转换公式（UniApp兼容）：
1. raw_value = (byte1 * 0x1000000 + byte2 * 0x10000 + byte3 * 0x100) / 0x100
2. microvolts = raw_value * (4.84 / 2^24) * 10^6 / 6
```

## Python解析器使用

### 1. 基础解析

```python
from neeg_parser import NEEGParser

# 创建解析器
parser = NEEGParser('recording.neeg')

# 获取文件头信息
header = parser.get_header()
print(f"设备: {header['device_name']}")
print(f"采样率: {header['sample_rate']}Hz")
print(f"开始时间: {header['start_time']}")

# 获取数据摘要
summary = parser.get_data_summary()
print(f"记录时长: {summary['duration_minutes']:.1f}分钟")
print(f"总采样点: {summary['total_samples']:,}个")
```

### 2. 数据转换

```python
# 转换为pandas DataFrame
df = parser.to_dataframe()
print(df.head())

# 转换为NumPy数组
timestamps, af7_data, af8_data = parser.to_numpy()

# 导出为CSV
parser.export_to_csv('eeg_data.csv')
```

### 3. 数据可视化

```python
# 绘制波形图（前10秒）
parser.plot_channels(duration=10.0, save_path='waveform.png')

# 绘制指定时间段
parser.plot_channels(start_time=30.0, duration=20.0)
```

### 4. 信号质量评估

```python
# 获取信号质量统计
quality = parser.get_signal_quality_stats()
print(f"信号质量: {quality['signal_quality_percentage']:.1f}%")
print(f"良好信号包: {quality['good_signal_packets']}个")
print(f"脱落信号包: {quality['poor_signal_packets']}个")
```

## 高级分析示例

### 1. 频域分析

```python
from neeg_analysis_example import NEEGAnalyzer

analyzer = NEEGAnalyzer('recording.neeg')

# 频域分析（FFT + 功率谱密度）
analyzer.frequency_analysis('both')

# 时频分析（短时傅里叶变换）
analyzer.time_frequency_analysis('af7', duration=30.0)
```

### 2. 滤波处理对比

```python
# 对比原始数据与滤波后效果
analyzer.filter_comparison()
```

### 3. 完整分析报告

```python
# 执行所有分析并生成HTML报告
analyzer.basic_statistics()
analyzer.signal_quality_analysis()
analyzer.export_analysis_report('report.html')
```

## 命令行使用

### 基础解析
```bash
python neeg_parser.py recording.neeg
```

### 高级分析
```bash
python neeg_analysis_example.py recording.neeg
```

生成文件：
- `recording.neeg_waveform.png` - 波形图
- `recording.neeg.csv` - CSV数据
- `neeg_frequency_analysis_both.png` - 频域分析图
- `neeg_timefrequency_af7.png` - 时频分析图
- `neeg_filter_comparison.png` - 滤波对比图
- `neeg_analysis_report.html` - 完整分析报告

## 数据格式说明

### DataFrame结构
| 列名 | 类型 | 说明 |
|------|------|------|
| timestamp_ms | int64 | 毫秒时间戳 |
| af7_uv | float64 | AF7通道数据（微伏） |
| af8_uv | float64 | AF8通道数据（微伏） |
| datetime | datetime64 | 时间索引 |

### 采样参数
- **采样率**: 250Hz
- **通道**: AF7-FPz, AF8-FPz
- **数据精度**: 24位ADC，微伏级精度
- **时间精度**: 毫秒级时间戳

## 依赖库安装

```bash
pip install numpy pandas matplotlib scipy seaborn
```

## 注意事项

1. **内存使用**: 长时间记录的文件可能很大，建议分段处理
2. **时间同步**: 时间戳基于设备本地时间，可能需要校准
3. **信号质量**: 根据signal_status字段评估数据可靠性
4. **数据完整性**: 244字节原始数据包含所有采集信息，支持重新处理

## 扩展功能

### 自定义滤波器
```python
from scipy import signal

# 设计自定义滤波器
b, a = signal.butter(4, [1, 40], btype='band', fs=250)

# 应用滤波
filtered_data = signal.filtfilt(b, a, df['af7_uv'].values)
```

### 特征提取
```python
# 计算频段功率
from scipy.fft import fft, fftfreq

def calculate_band_power(data, fs, low_freq, high_freq):
    fft_data = fft(data)
    freqs = fftfreq(len(data), 1/fs)
    
    # 选择频段
    mask = (freqs >= low_freq) & (freqs <= high_freq)
    band_power = np.mean(np.abs(fft_data[mask])**2)
    
    return band_power

# 计算Alpha波功率
alpha_power = calculate_band_power(df['af7_uv'].values, 250, 8, 13)
```

### 实时监控
```python
# 监控文件变化（用于实时分析）
import time
import os

def monitor_file(file_path, callback):
    last_size = 0
    while True:
        current_size = os.path.getsize(file_path)
        if current_size > last_size:
            # 文件有新数据
            callback(file_path, last_size, current_size)
            last_size = current_size
        time.sleep(1.0)
```

通过这套完整的解析工具，可以对NEEG文件进行深入的数据分析和可视化处理。