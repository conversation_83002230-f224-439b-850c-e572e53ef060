# 测试指南

## 单元测试说明

由于滤波器算法使用了 Native (C++) 实现，测试需要在 Android 设备或模拟器上运行。

### 测试文件位置

- **Android Instrumented Tests**: `app/src/androidTest/java/com/brainwonderful/naoyu_bluetooth_android/`
  - 包含所有滤波器的测试代码
  - 需要在设备/模拟器上运行

- **本地单元测试**: `app/src/test/java/com/brainwonderful/naoyu_bluetooth_android/`
  - 仅包含不依赖 Native 代码的测试

### 运行测试

#### 在 Android Studio 中运行

1. 连接 Android 设备或启动模拟器
2. 在项目视图中找到 `androidTest` 目录
3. 右键点击测试类或整个目录
4. 选择 "Run Tests"

#### 使用命令行运行

```bash
# 运行所有 Android 测试
./gradlew connectedAndroidTest

# 运行特定的测试类
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.brainwonderful.naoyu_bluetooth_android.filters.NotchFilterTest

# 生成测试报告
./gradlew connectedAndroidTest --continue
# 报告位置: app/build/reports/androidTests/connected/index.html
```

### 测试覆盖的算法

1. **NotchFilterTest** - 50Hz 陷波滤波器测试
   - 频率响应测试
   - 稳定性测试
   - 相位响应测试
   - 不同参数测试

2. **FIRFilterTest** - FIR 滤波器测试
   - 低通、高通、带通滤波测试
   - 线性相位特性测试
   - 脉冲响应测试
   - 参数验证测试

3. **FFTTest** - 快速傅里叶变换测试
   - 单频/多频信号测试
   - 逆变换测试
   - Parseval 定理验证
   - 相位信息测试

4. **EdgeDetectionTest** - 边缘检测测试
   - 上升沿/下降沿检测
   - 不同信号类型测试
   - 噪声环境测试

5. **DouglasPeuckerTest** - 曲线简化算法测试
   - 不同 epsilon 值测试
   - 形状保持测试
   - 性能测试
   - 统计特征保持测试

### 测试数据生成器

`TestDataGenerator` 提供了各种标准测试信号：
- 正弦波、方波、三角波
- 白噪声
- 混合频率信号
- 模拟 EEG 信号
- 脉冲信号
- 线性调频信号

### 注意事项

1. 确保设备/模拟器已连接并且 ADB 可用
2. 首次运行可能需要较长时间编译 Native 代码
3. 某些测试（如性能测试）在模拟器上可能不准确
4. 建议在真实设备上运行以获得更准确的测试结果